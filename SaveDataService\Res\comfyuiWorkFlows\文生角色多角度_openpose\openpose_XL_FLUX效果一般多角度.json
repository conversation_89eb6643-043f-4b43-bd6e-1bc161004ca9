{"id": "fa172aa1-69cd-4bf5-bbf0-f495b4dfa35f", "revision": 0, "last_node_id": 64, "last_link_id": 104, "nodes": [{"id": 61, "type": "IPAdapterModelLoader", "pos": [-1316.8018798828125, -660.7552490234375], "size": [315, 58], "flags": {}, "order": 0, "mode": 4, "inputs": [{"localized_name": "ipadapter_file", "name": "ipadapter_file", "type": "COMBO", "widget": {"name": "ipadapter_file"}, "link": null}], "outputs": [{"localized_name": "IPADAPTER", "name": "IPADAPTER", "type": "IPADAPTER", "slot_index": 0, "links": [94]}], "properties": {"cnr_id": "comfyui_ipadapter_plus", "ver": "2.0.0", "Node name for S&R": "IPAdapterModelLoader"}, "widgets_values": ["ip-adapter-plus_sdxl_vit-h.safetensors"], "color": "#232", "bgcolor": "#353"}, {"id": 62, "type": "CLIPVisionLoader", "pos": [-1310.8018798828125, -464.7551574707031], "size": [315, 58], "flags": {}, "order": 1, "mode": 4, "inputs": [{"localized_name": "clip名称", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}], "outputs": [{"localized_name": "CLIP视觉", "name": "CLIP_VISION", "type": "CLIP_VISION", "links": [95]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPVisionLoader"}, "widgets_values": ["CLIP-ViT-H-14-laion2B-s32B-b79K.safetensors"], "color": "#232", "bgcolor": "#353"}, {"id": 59, "type": "IPAdapterAdvanced", "pos": [-959.8019409179688, -645.7552490234375], "size": [315, 278], "flags": {}, "order": 15, "mode": 4, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 93}, {"localized_name": "ipadapter", "name": "ipadapter", "type": "IPADAPTER", "link": 94}, {"localized_name": "image", "name": "image", "type": "IMAGE", "link": 92}, {"localized_name": "image_negative", "name": "image_negative", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "attn_mask", "name": "attn_mask", "shape": 7, "type": "MASK", "link": null}, {"localized_name": "clip_vision", "name": "clip_vision", "shape": 7, "type": "CLIP_VISION", "link": 95}, {"localized_name": "weight", "name": "weight", "type": "FLOAT", "widget": {"name": "weight"}, "link": null}, {"localized_name": "weight_type", "name": "weight_type", "type": "COMBO", "widget": {"name": "weight_type"}, "link": null}, {"localized_name": "combine_embeds", "name": "combine_embeds", "type": "COMBO", "widget": {"name": "combine_embeds"}, "link": null}, {"localized_name": "start_at", "name": "start_at", "type": "FLOAT", "widget": {"name": "start_at"}, "link": null}, {"localized_name": "end_at", "name": "end_at", "type": "FLOAT", "widget": {"name": "end_at"}, "link": null}, {"localized_name": "embeds_scaling", "name": "embeds_scaling", "type": "COMBO", "widget": {"name": "embeds_scaling"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [96]}], "properties": {"cnr_id": "comfyui_ipadapter_plus", "ver": "2.0.0", "Node name for S&R": "IPAdapterAdvanced"}, "widgets_values": [1, "style transfer precise", "concat", 0, 1, "V only"], "color": "#232", "bgcolor": "#353"}, {"id": 48, "type": "ControlNetApplyAdvanced", "pos": [-1540, 584], "size": [315, 186], "flags": {}, "order": 23, "mode": 0, "inputs": [{"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 77}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 74}, {"localized_name": "ControlNet", "name": "control_net", "type": "CONTROL_NET", "link": 79}, {"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 101}, {"localized_name": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}, {"localized_name": "强度", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}, "link": null}, {"localized_name": "开始百分比", "name": "start_percent", "type": "FLOAT", "widget": {"name": "start_percent"}, "link": null}, {"localized_name": "结束百分比", "name": "end_percent", "type": "FLOAT", "widget": {"name": "end_percent"}, "link": null}], "outputs": [{"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [76]}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [75]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ControlNetApplyAdvanced"}, "widgets_values": [1, 0, 1], "color": "#323", "bgcolor": "#535"}, {"id": 53, "type": "PreviewImage", "pos": [-1514, 827], "size": [350.2998046875, 246.95558166503906], "flags": {}, "order": 21, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 82}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": [], "color": "#323", "bgcolor": "#535"}, {"id": 44, "type": "K<PERSON><PERSON><PERSON>", "pos": [-1161, -128], "size": [315, 262], "flags": {}, "order": 25, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 96}, {"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 76}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 75}, {"localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 83}, {"localized_name": "种子", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [84]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [523180677813903, "randomize", 4, 2, "dpmpp_sde", "karras", 1], "color": "#223", "bgcolor": "#335"}, {"id": 57, "type": "PreviewImage", "pos": [-770, -88], "size": [210, 246], "flags": {}, "order": 27, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 86}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 56, "type": "VAEDecode", "pos": [-818, 328], "size": [210, 46], "flags": {}, "order": 26, "mode": 0, "inputs": [{"localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 84}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 90}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [86, 91]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEDecode"}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 39, "type": "UltimateSDUpscale", "pos": [1445.4814453125, 0.9259588718414307], "size": [315, 614], "flags": {}, "order": 31, "mode": 4, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 60}, {"localized_name": "model", "name": "model", "type": "MODEL", "link": 61}, {"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 62}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 63}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 64}, {"localized_name": "upscale_model", "name": "upscale_model", "type": "UPSCALE_MODEL", "link": 65}, {"localized_name": "upscale_by", "name": "upscale_by", "type": "FLOAT", "widget": {"name": "upscale_by"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "denoise", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}, {"localized_name": "mode_type", "name": "mode_type", "type": "COMBO", "widget": {"name": "mode_type"}, "link": null}, {"localized_name": "tile_width", "name": "tile_width", "type": "INT", "widget": {"name": "tile_width"}, "link": null}, {"localized_name": "tile_height", "name": "tile_height", "type": "INT", "widget": {"name": "tile_height"}, "link": null}, {"localized_name": "mask_blur", "name": "mask_blur", "type": "INT", "widget": {"name": "mask_blur"}, "link": null}, {"localized_name": "tile_padding", "name": "tile_padding", "type": "INT", "widget": {"name": "tile_padding"}, "link": null}, {"localized_name": "seam_fix_mode", "name": "seam_fix_mode", "type": "COMBO", "widget": {"name": "seam_fix_mode"}, "link": null}, {"localized_name": "seam_fix_denoise", "name": "seam_fix_denoise", "type": "FLOAT", "widget": {"name": "seam_fix_denoise"}, "link": null}, {"localized_name": "seam_fix_width", "name": "seam_fix_width", "type": "INT", "widget": {"name": "seam_fix_width"}, "link": null}, {"localized_name": "seam_fix_mask_blur", "name": "seam_fix_mask_blur", "type": "INT", "widget": {"name": "seam_fix_mask_blur"}, "link": null}, {"localized_name": "seam_fix_padding", "name": "seam_fix_padding", "type": "INT", "widget": {"name": "seam_fix_padding"}, "link": null}, {"localized_name": "force_uniform_tiles", "name": "force_uniform_tiles", "type": "BOOLEAN", "widget": {"name": "force_uniform_tiles"}, "link": null}, {"localized_name": "tiled_decode", "name": "tiled_decode", "type": "BOOLEAN", "widget": {"name": "tiled_decode"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [66]}], "properties": {"cnr_id": "comfyui_ultimatesdupscale", "ver": "1.1.3", "Node name for S&R": "UltimateSDUpscale"}, "widgets_values": [2, 353402288652380, "randomize", 20, 8, "euler", "simple", 0.2, "Linear", 512, 512, 8, 32, "None", 1, 64, 8, 16, true, false], "color": "#432", "bgcolor": "#653"}, {"id": 25, "type": "RandomNoise", "pos": [-83.68489074707031, 449.53558349609375], "size": [315, 82], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "噪波随机种", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "link": null}], "outputs": [{"localized_name": "噪波", "name": "NOISE", "type": "NOISE", "links": [37]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "RandomNoise"}, "widgets_values": [101117514126118, "randomize"], "color": "#322", "bgcolor": "#533"}, {"id": 42, "type": "PreviewImage", "pos": [1124.3154296875, 126.53561401367188], "size": [210, 246], "flags": {}, "order": 32, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 68}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": [], "color": "#322", "bgcolor": "#533"}, {"id": 11, "type": "DualCLIPLoader", "pos": [-503.6845397949219, 163.53562927246094], "size": [315, 130], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "CLIP名称1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "CLIP名称2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [10]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp8_e4m3fn.safetensors", "clip_l.safetensors", "flux", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 41, "type": "UpscaleModelLoader", "pos": [1441.4814453125, -106.07404327392578], "size": [315, 58], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "模型名称", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}], "outputs": [{"localized_name": "放大模型", "name": "UPSCALE_MODEL", "type": "UPSCALE_MODEL", "links": [65]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "UpscaleModelLoader"}, "widgets_values": ["4xUltrasharp_4xUltrasharpV10.pt"], "color": "#432", "bgcolor": "#653"}, {"id": 16, "type": "KSamplerSelect", "pos": [-82.68488311767578, 602.5357666015625], "size": [315, 58], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}], "outputs": [{"localized_name": "采样器", "name": "SAMPLER", "type": "SAMPLER", "links": [19]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"], "color": "#322", "bgcolor": "#533"}, {"id": 43, "type": "CheckpointLoaderSimple", "pos": [-2346, 58], "size": [315, 98], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "Checkpoint名称", "name": "ckpt_name", "type": "COMBO", "widget": {"name": "ckpt_name"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [93]}, {"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [70, 71]}, {"localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 2, "links": [90]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["XL\\juggernautXL_ragnarokBy.safetensors"], "color": "#223", "bgcolor": "#335"}, {"id": 60, "type": "LoadImage", "pos": [-1741.8018798828125, -653.7552490234375], "size": [315, 314], "flags": {}, "order": 7, "mode": 4, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [92]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["ComfyUI_temp_enibp_00001_.png", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 12, "type": "UNETLoader", "pos": [-509.68463134765625, 22.535673141479492], "size": [315, 82], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "UNet名称", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "数据类型", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [38, 39, 61]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev.safetensors", "fp8_e4m3fn"], "color": "#322", "bgcolor": "#533"}, {"id": 10, "type": "VAELoader", "pos": [-494.68450927734375, 371.5356140136719], "size": [315, 58], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [12, 64, 89]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 46, "type": "CLIPTextEncode", "pos": [-1968.8841552734375, 198.8107147216797], "size": [419.47125244140625, 88], "flags": {}, "order": 14, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 71}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [74]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""], "color": "#223", "bgcolor": "#335"}, {"id": 29, "type": "Image Save", "pos": [1850.8717041015625, -367.96844482421875], "size": [1304.25146484375, 1462.2845458984375], "flags": {}, "order": 33, "mode": 4, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": 66}, {"localized_name": "output_path", "name": "output_path", "type": "STRING", "widget": {"name": "output_path"}, "link": null}, {"localized_name": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}, {"localized_name": "filename_delimiter", "name": "filename_delimiter", "type": "STRING", "widget": {"name": "filename_delimiter"}, "link": null}, {"localized_name": "filename_number_padding", "name": "filename_number_padding", "type": "INT", "widget": {"name": "filename_number_padding"}, "link": null}, {"localized_name": "filename_number_start", "name": "filename_number_start", "type": "COMBO", "widget": {"name": "filename_number_start"}, "link": null}, {"localized_name": "extension", "name": "extension", "type": "COMBO", "widget": {"name": "extension"}, "link": null}, {"localized_name": "dpi", "name": "dpi", "type": "INT", "widget": {"name": "dpi"}, "link": null}, {"localized_name": "quality", "name": "quality", "type": "INT", "widget": {"name": "quality"}, "link": null}, {"localized_name": "optimize_image", "name": "optimize_image", "type": "COMBO", "widget": {"name": "optimize_image"}, "link": null}, {"localized_name": "lossless_webp", "name": "lossless_webp", "type": "COMBO", "widget": {"name": "lossless_webp"}, "link": null}, {"localized_name": "overwrite_mode", "name": "overwrite_mode", "type": "COMBO", "widget": {"name": "overwrite_mode"}, "link": null}, {"localized_name": "show_history", "name": "show_history", "type": "COMBO", "widget": {"name": "show_history"}, "link": null}, {"localized_name": "show_history_by_prefix", "name": "show_history_by_prefix", "type": "COMBO", "widget": {"name": "show_history_by_prefix"}, "link": null}, {"localized_name": "embed_workflow", "name": "embed_workflow", "type": "COMBO", "widget": {"name": "embed_workflow"}, "link": null}, {"localized_name": "show_previews", "name": "show_previews", "type": "COMBO", "widget": {"name": "show_previews"}, "link": null}], "outputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "links": null}, {"localized_name": "files", "name": "files", "type": "STRING", "links": null}], "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "1.0.2", "Node name for S&R": "Image Save"}, "widgets_values": ["flux[time(%Y-%m-%d)]", "ComfyUI", "_", 4, "false", "png", 300, 100, "true", "false", "false", "false", "true", "true", "true"], "color": "#332922", "bgcolor": "#593930", "shape": 1}, {"id": 58, "type": "VAEEncode", "pos": [-26.468481063842773, 259.3309020996094], "size": [210, 46], "flags": {}, "order": 28, "mode": 0, "inputs": [{"localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 91}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 89}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [88]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEEncode"}, "widgets_values": [], "color": "#322", "bgcolor": "#533"}, {"id": 63, "type": "GetImageSize+", "pos": [-1729.7861328125, 326.5638427734375], "size": [159.50155639648438, 66], "flags": {}, "order": 18, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 98}], "outputs": [{"localized_name": "width", "name": "width", "type": "INT", "links": [99]}, {"localized_name": "height", "name": "height", "type": "INT", "links": [100]}, {"localized_name": "count", "name": "count", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "GetImageSize+"}}, {"id": 17, "type": "BasicScheduler", "pos": [-65.68489837646484, 744.5357666015625], "size": [315, 106], "flags": {}, "order": 16, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 38}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"localized_name": "Sigmas", "name": "SIGMAS", "type": "SIGMAS", "links": [20]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 20, 0.5000000000000001], "color": "#322", "bgcolor": "#533"}, {"id": 54, "type": "SDXLEmptyLatentSizePicker+", "pos": [-1471, 206], "size": [315, 170], "flags": {}, "order": 22, "mode": 0, "inputs": [{"localized_name": "resolution", "name": "resolution", "type": "COMBO", "widget": {"name": "resolution"}, "link": null}, {"localized_name": "batch_size", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}, "link": null}, {"localized_name": "width_override", "name": "width_override", "type": "INT", "widget": {"name": "width_override"}, "link": 99}, {"localized_name": "height_override", "name": "height_override", "type": "INT", "widget": {"name": "height_override"}, "link": 100}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [83]}, {"localized_name": "width", "name": "width", "type": "INT", "links": null}, {"localized_name": "height", "name": "height", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "SDXLEmptyLatentSizePicker+"}, "widgets_values": ["1216x832 (1.46)", 1, 0, 0], "color": "#223", "bgcolor": "#335"}, {"id": 52, "type": "LoadImage", "pos": [-2275, 613], "size": [315, 314], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [80, 98]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["image_oVUA41J6_1735208483939_raw.jpg", "image"], "color": "#323", "bgcolor": "#535", "shape": 4}, {"id": 51, "type": "OpenposePreprocessor", "pos": [-1900, 789], "size": [315, 174], "flags": {}, "order": 17, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 80}, {"localized_name": "detect_hand", "name": "detect_hand", "shape": 7, "type": "COMBO", "widget": {"name": "detect_hand"}, "link": null}, {"localized_name": "detect_body", "name": "detect_body", "shape": 7, "type": "COMBO", "widget": {"name": "detect_body"}, "link": null}, {"localized_name": "detect_face", "name": "detect_face", "shape": 7, "type": "COMBO", "widget": {"name": "detect_face"}, "link": null}, {"localized_name": "resolution", "name": "resolution", "shape": 7, "type": "INT", "widget": {"name": "resolution"}, "link": null}, {"localized_name": "scale_stick_for_xinsr_cn", "name": "scale_stick_for_xinsr_cn", "shape": 7, "type": "COMBO", "widget": {"name": "scale_stick_for_xinsr_cn"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [82, 101]}, {"localized_name": "POSE_KEYPOINT", "name": "POSE_KEYPOINT", "type": "POSE_KEYPOINT", "links": null}], "properties": {"cnr_id": "comfyui_controlnet_aux", "ver": "1.0.7", "Node name for S&R": "OpenposePreprocessor"}, "widgets_values": ["enable", "enable", "enable", 512, "disable"], "color": "#323", "bgcolor": "#535"}, {"id": 50, "type": "ControlNetLoader", "pos": [-1922.2413330078125, 647.6243896484375], "size": [315, 58], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "ControlNet名称", "name": "control_net_name", "type": "COMBO", "widget": {"name": "control_net_name"}, "link": null}], "outputs": [{"localized_name": "ControlNet", "name": "CONTROL_NET", "type": "CONTROL_NET", "slot_index": 0, "links": [79]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ControlNetLoader"}, "widgets_values": ["XL\\diffusion_pytorch_model_XLpose.safetensors"], "color": "#323", "bgcolor": "#535"}, {"id": 45, "type": "CLIPTextEncode", "pos": [-1953.4332275390625, -72.44208526611328], "size": [400, 200], "flags": {}, "order": 19, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 70}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 102}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [77]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["a dark warrior, digital painting, zbrush, artstation,character sheet, in different poses and angles, including front view, side view, and back view, turnaround sheet"], "color": "#223", "bgcolor": "#335"}, {"id": 8, "type": "VAEDecode", "pos": [816.84765625, 13.93917465209961], "size": [210, 46], "flags": {}, "order": 30, "mode": 0, "inputs": [{"localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 24}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 12}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [60, 68]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEDecode"}, "widgets_values": [], "color": "#322", "bgcolor": "#533"}, {"id": 13, "type": "SamplerCustomAdvanced", "pos": [755.1044311523438, 231.23733520507812], "size": [355.20001220703125, 106], "flags": {}, "order": 29, "mode": 0, "inputs": [{"localized_name": "噪波", "name": "noise", "type": "NOISE", "link": 37}, {"localized_name": "引导器", "name": "guider", "type": "GUIDER", "link": 30}, {"localized_name": "采样器", "name": "sampler", "type": "SAMPLER", "link": 19}, {"localized_name": "西格玛", "name": "sigmas", "type": "SIGMAS", "link": 20}, {"localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 88}], "outputs": [{"localized_name": "Latent", "name": "output", "type": "LATENT", "slot_index": 0, "links": [24]}, {"localized_name": "降噪Latent", "name": "denoised_output", "type": "LATENT", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": [], "color": "#322", "bgcolor": "#533"}, {"id": 22, "type": "BasicGuider", "pos": [457.3792724609375, 262.2373352050781], "size": [241.79998779296875, 46], "flags": {}, "order": 24, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 39}, {"localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 40}], "outputs": [{"localized_name": "引导器", "name": "GUIDER", "type": "GUIDER", "slot_index": 0, "links": [30]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "BasicGuider"}, "widgets_values": [], "color": "#322", "bgcolor": "#533"}, {"id": 6, "type": "CLIPTextEncode", "pos": [128.3267822265625, 60.096885681152344], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 20, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 10}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 104}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [40, 62]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["a dark warrior, digital painting, zbrush, artstation,character sheet, in different poses and angles, including front view, side view, and back view, turnaround sheet"], "color": "#322", "bgcolor": "#533"}, {"id": 40, "type": "CLIPTextEncode", "pos": [22.315078735351562, -183.46437072753906], "size": [400, 200], "flags": {}, "order": 13, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": null}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [63]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""], "color": "#322", "bgcolor": "#533"}, {"id": 64, "type": "CR Text", "pos": [-439.6815185546875, -239.42822265625], "size": [400, 200], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "text", "name": "text", "type": "*", "links": [102, 104]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text"}, "widgets_values": ["a dark warrior, digital painting, zbrush, artstation,character sheet, in different poses and angles, including front view, side view, and back view, turnaround sheet"]}], "links": [[10, 11, 0, 6, 0, "CLIP"], [12, 10, 0, 8, 1, "VAE"], [19, 16, 0, 13, 2, "SAMPLER"], [20, 17, 0, 13, 3, "SIGMAS"], [24, 13, 0, 8, 0, "LATENT"], [30, 22, 0, 13, 1, "GUIDER"], [37, 25, 0, 13, 0, "NOISE"], [38, 12, 0, 17, 0, "MODEL"], [39, 12, 0, 22, 0, "MODEL"], [40, 6, 0, 22, 1, "CONDITIONING"], [60, 8, 0, 39, 0, "IMAGE"], [61, 12, 0, 39, 1, "MODEL"], [62, 6, 0, 39, 2, "CONDITIONING"], [63, 40, 0, 39, 3, "CONDITIONING"], [64, 10, 0, 39, 4, "VAE"], [65, 41, 0, 39, 5, "UPSCALE_MODEL"], [66, 39, 0, 29, 0, "IMAGE"], [68, 8, 0, 42, 0, "IMAGE"], [70, 43, 1, 45, 0, "CLIP"], [71, 43, 1, 46, 0, "CLIP"], [74, 46, 0, 48, 1, "CONDITIONING"], [75, 48, 1, 44, 2, "CONDITIONING"], [76, 48, 0, 44, 1, "CONDITIONING"], [77, 45, 0, 48, 0, "CONDITIONING"], [79, 50, 0, 48, 2, "CONTROL_NET"], [80, 52, 0, 51, 0, "IMAGE"], [82, 51, 0, 53, 0, "IMAGE"], [83, 54, 0, 44, 3, "LATENT"], [84, 44, 0, 56, 0, "LATENT"], [86, 56, 0, 57, 0, "IMAGE"], [88, 58, 0, 13, 4, "LATENT"], [89, 10, 0, 58, 1, "VAE"], [90, 43, 2, 56, 1, "VAE"], [91, 56, 0, 58, 0, "IMAGE"], [92, 60, 0, 59, 2, "IMAGE"], [93, 43, 0, 59, 0, "MODEL"], [94, 61, 0, 59, 1, "IPADAPTER"], [95, 62, 0, 59, 5, "CLIP_VISION"], [96, 59, 0, 44, 0, "MODEL"], [98, 52, 0, 63, 0, "IMAGE"], [99, 63, 0, 54, 2, "INT"], [100, 63, 1, 54, 3, "INT"], [101, 51, 0, 48, 3, "IMAGE"], [102, 64, 0, 45, 1, "STRING"], [104, 64, 0, 6, 1, "STRING"]], "groups": [{"id": 1, "title": "IPAdapter", "bounding": [-1761.1480712890625, -844.3704833984375, 1134, 520], "color": "#8A8", "font_size": 24, "flags": {}}, {"id": 2, "title": "ControlNet", "bounding": [-2280, 462, 1124, 622], "color": "#a1309b", "font_size": 24, "flags": {}}, {"id": 3, "title": "SDXL First Pass", "bounding": [-2359, -273, 1801, 667], "color": "#88A", "font_size": 24, "flags": {}}, {"id": 4, "title": "Flux Second Pass", "bounding": [-515.6846313476562, -297.46435546875, 1888, 1171], "color": "#A88", "font_size": 24, "flags": {}}, {"id": 5, "title": "Upscaler", "bounding": [1418.4814453125, -246.18516540527344, 373, 937], "color": "#b58b2a", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.2794145953766202, "offset": [3380.4357849975686, 1499.362390058055]}, "frontendVersion": "1.18.9", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}