{"id": "baaa0747-e1ed-4556-b9ad-288da8f5e2c5", "revision": 0, "last_node_id": 120, "last_link_id": 340, "nodes": [{"id": 33, "type": "BrushNetLoader", "pos": [96.10049438476562, 503.08837890625], "size": [301.9520263671875, 150.2537078857422], "flags": {"collapsed": false}, "order": 0, "mode": 0, "inputs": [{"localized_name": "brushnet", "name": "brushnet", "type": "COMBO", "widget": {"name": "brushnet"}, "link": null}, {"localized_name": "dtype", "name": "dtype", "type": "COMBO", "widget": {"name": "dtype"}, "link": null}], "outputs": [{"label": "brushnet", "localized_name": "brushnet", "name": "brushnet", "type": "BRMODEL", "slot_index": 0, "links": [69]}], "properties": {"cnr_id": "comfyui-brushnet", "ver": "1.0.2", "Node name for S&R": "BrushNetLoader"}, "widgets_values": ["BrushNet\\random_mask_brushnet_ckpt_sdxl_v0\\random_mask_brushnet_ckpt_sdxl_v0\\diffusion_pytorch_model.safetensors", "float16"]}, {"id": 16, "type": "VAELoader", "pos": [778.1004638671875, 157.08839416503906], "size": [315, 58], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"label": "VAE", "localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [82, 86]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAELoader"}, "widgets_values": ["sdxl_vae.safetensors"]}, {"id": 13, "type": "KSampler //Inspire", "pos": [436.1004943847656, 164.08839416503906], "size": [315, 594], "flags": {}, "order": 31, "mode": 0, "inputs": [{"label": "model", "localized_name": "model", "name": "model", "type": "MODEL", "link": 75}, {"label": "positive", "localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 160}, {"label": "negative", "localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 196}, {"label": "latent_image", "localized_name": "latent_image", "name": "latent_image", "type": "LATENT", "link": 79}, {"localized_name": "scheduler_func_opt", "name": "scheduler_func_opt", "shape": 7, "type": "SCHEDULER_FUNC", "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "denoise", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}, {"localized_name": "noise_mode", "name": "noise_mode", "type": "COMBO", "widget": {"name": "noise_mode"}, "link": null}, {"localized_name": "batch_seed_mode", "name": "batch_seed_mode", "type": "COMBO", "widget": {"name": "batch_seed_mode"}, "link": null}, {"localized_name": "variation_seed", "name": "variation_seed", "type": "INT", "widget": {"name": "variation_seed"}, "link": null}, {"localized_name": "variation_strength", "name": "variation_strength", "type": "FLOAT", "widget": {"name": "variation_strength"}, "link": null}, {"localized_name": "variation_method", "name": "variation_method", "shape": 7, "type": "COMBO", "widget": {"name": "variation_method"}, "link": null}, {"localized_name": "internal_seed", "name": "internal_seed", "shape": 7, "type": "INT", "widget": {"name": "internal_seed"}, "link": null}], "outputs": [{"label": "LATENT", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [81]}], "properties": {"cnr_id": "comfyui-inspire-pack", "ver": "1.18.0", "Node name for S&R": "KSampler //Inspire"}, "widgets_values": [522573196010461, "randomize", 20, 8, "euler_ancestral", "normal", 1, "CPU", "incremental", 0, 0, "linear", 0]}, {"id": 100, "type": "Reroute", "pos": [-57.5601692199707, -420.701416015625], "size": [75, 26], "flags": {}, "order": 26, "mode": 2, "inputs": [{"name": "", "type": "*", "link": 337}], "outputs": [{"name": "", "type": "IMAGE", "slot_index": 0, "links": [286, 321, 322, 323, 324]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 102, "type": "Note", "pos": [-1037.33349609375, -52.33575439453125], "size": [210, 88], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": [""], "color": "#c09430", "bgcolor": "#653"}, {"id": 114, "type": "Any Switch (rgthree)", "pos": [683.1005249023438, 819.0883178710938], "size": [252, 106], "flags": {"collapsed": true}, "order": 17, "mode": 0, "inputs": [{"dir": 3, "name": "any_01", "type": "MASK", "link": null}, {"dir": 3, "name": "any_02", "type": "MASK", "link": 316}, {"dir": 3, "name": "any_03", "type": "MASK", "link": null}, {"dir": 3, "name": "any_04", "type": "MASK", "link": null}, {"name": "any_05", "type": "MASK", "link": null}], "outputs": [{"dir": 4, "label": "MASK", "name": "*", "shape": 3, "type": "MASK", "slot_index": 0, "links": [311]}], "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0"}, "widgets_values": []}, {"id": 22, "type": "CLIPSetLastLayer", "pos": [-935, -349], "size": [315, 58], "flags": {}, "order": 11, "mode": 0, "inputs": [{"label": "clip", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 94}, {"localized_name": "停止在CLIP层", "name": "stop_at_clip_layer", "type": "INT", "widget": {"name": "stop_at_clip_layer"}, "link": null}], "outputs": [{"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [64, 147]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPSetLastLayer"}, "widgets_values": [-2]}, {"id": 88, "type": "PreviewImage", "pos": [1287, 119], "size": [641.0221557617188, 533.9127807617188], "flags": {}, "order": 35, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 233}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 21, "type": "LoadImage", "pos": [2016, -756], "size": [1136.2645263671875, 607.06884765625], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [186, 188, 204]}, {"label": "MASK", "localized_name": "遮罩", "name": "MASK", "type": "MASK", "slot_index": 1, "links": [317]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["clipspace/clipspace-mask-184934.69999999925.png [input]", "image"]}, {"id": 12, "type": "BNK_CLIPTextEncodeAdvanced", "pos": [-599, -217], "size": [373.63037109375, 136], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "clip", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 64}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}, {"localized_name": "token_normalization", "name": "token_normalization", "type": "COMBO", "widget": {"name": "token_normalization"}, "link": null}, {"localized_name": "weight_interpretation", "name": "weight_interpretation", "type": "COMBO", "widget": {"name": "weight_interpretation"}, "link": null}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [157, 320]}], "properties": {"cnr_id": "ComfyUI_ADV_CLIP_emb", "ver": "63984deefb005da1ba90a1175e21d91040da38ab", "Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "widgets_values": ["worst quality,low quality, normal quality, monochrome, lowres, watermark, spots", "none", "A1111"]}, {"id": 38, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-943, -225], "size": [315, 126], "flags": {}, "order": 9, "mode": 4, "inputs": [{"label": "model", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 325}, {"label": "clip", "localized_name": "CLIPCLIP", "name": "clip", "type": "CLIP", "link": 95}, {"localized_name": "LoRA名称", "name": "lora_name", "type": "COMBO", "widget": {"name": "lora_name"}, "link": null}, {"localized_name": "模型强度", "name": "strength_model", "type": "FLOAT", "widget": {"name": "strength_model"}, "link": null}, {"localized_name": "CLIP强度", "name": "strength_clip", "type": "FLOAT", "widget": {"name": "strength_clip"}, "link": null}], "outputs": [{"label": "MODEL", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [93]}, {"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [94]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["style\\StS_age_slider_v1_initial_release.safetensors", -3, 1]}, {"id": 32, "type": "BrushNet", "pos": [109, 196], "size": [315, 246], "flags": {}, "order": 27, "mode": 0, "inputs": [{"label": "model", "localized_name": "model", "name": "model", "type": "MODEL", "link": 93}, {"label": "vae", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 86}, {"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 286}, {"label": "mask", "localized_name": "mask", "name": "mask", "type": "MASK", "link": 329}, {"label": "brushnet", "localized_name": "brushnet", "name": "brushnet", "type": "BRMODEL", "link": 69}, {"label": "positive", "localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 319}, {"label": "negative", "localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 320}, {"localized_name": "scale", "name": "scale", "type": "FLOAT", "widget": {"name": "scale"}, "link": null}, {"localized_name": "start_at", "name": "start_at", "type": "INT", "widget": {"name": "start_at"}, "link": null}, {"localized_name": "end_at", "name": "end_at", "type": "INT", "widget": {"name": "end_at"}, "link": null}, {"label": "clip", "name": "clip", "type": "PPCLIP", "link": null}], "outputs": [{"label": "model", "localized_name": "model", "name": "model", "type": "MODEL", "slot_index": 0, "links": [75]}, {"label": "positive", "localized_name": "positive", "name": "positive", "type": "CONDITIONING", "slot_index": 1, "links": [160]}, {"label": "negative", "localized_name": "negative", "name": "negative", "type": "CONDITIONING", "slot_index": 2, "links": [196]}, {"label": "latent", "localized_name": "latent", "name": "latent", "type": "LATENT", "slot_index": 3, "links": [79]}], "properties": {"cnr_id": "comfyui-brushnet", "ver": "1.0.2", "Node name for S&R": "BrushNet"}, "widgets_values": [1, 0, 10000]}, {"id": 70, "type": "ImageCrop", "pos": [-493.77008056640625, 689.5936889648438], "size": [315, 130], "flags": {"collapsed": false}, "order": 19, "mode": 4, "inputs": [{"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 204}, {"label": "宽度", "localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 174}, {"label": "高度", "localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 175}, {"label": "X", "localized_name": "x", "name": "x", "type": "INT", "widget": {"name": "x"}, "link": 208}, {"label": "Y", "localized_name": "y", "name": "y", "type": "INT", "widget": {"name": "y"}, "link": 207}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [336]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageCrop", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "widgets_values": [512, 512, 0, 0], "color": "rgba(0,0,0,.8)"}, {"id": 69, "type": "Mask Crop Region", "pos": [-484.5552673339844, 862.0274047851562], "size": [314.73175048828125, 237.8780517578125], "flags": {}, "order": 16, "mode": 4, "inputs": [{"label": "遮罩", "localized_name": "mask", "name": "mask", "type": "MASK", "link": 185}, {"localized_name": "padding", "name": "padding", "type": "INT", "widget": {"name": "padding"}, "link": null}, {"localized_name": "region_type", "name": "region_type", "type": "COMBO", "widget": {"name": "region_type"}, "link": null}], "outputs": [{"label": "遮罩", "localized_name": "cropped_mask", "name": "cropped_mask", "type": "MASK", "slot_index": 0, "links": [316]}, {"label": "裁剪数据", "localized_name": "crop_data", "name": "crop_data", "type": "CROP_DATA", "links": null}, {"label": "上", "localized_name": "top_int", "name": "top_int", "type": "INT", "slot_index": 2, "links": [207, 210, 227]}, {"label": "左", "localized_name": "left_int", "name": "left_int", "type": "INT", "slot_index": 3, "links": [208, 225, 228]}, {"label": "右", "localized_name": "right_int", "name": "right_int", "type": "INT", "links": null}, {"label": "下", "localized_name": "bottom_int", "name": "bottom_int", "type": "INT", "slot_index": 5, "links": []}, {"label": "宽度", "localized_name": "width_int", "name": "width_int", "type": "INT", "slot_index": 6, "links": [174, 197, 215, 230]}, {"label": "高度", "localized_name": "height_int", "name": "height_int", "type": "INT", "slot_index": 7, "links": [175, 198, 214, 231]}], "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "1.0.2", "Node name for S&R": "Mask Crop Region", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "widgets_values": [64, "minority"], "color": "rgba(0,0,0,.8)"}, {"id": 115, "type": "PrimitiveInt", "pos": [-758.2280883789062, 1154.8095703125], "size": [270, 82], "flags": {}, "order": 4, "mode": 4, "inputs": [{"localized_name": "value", "name": "value", "type": "INT", "widget": {"name": "value"}, "link": null}], "outputs": [{"localized_name": "整数", "name": "INT", "type": "INT", "links": [326, 327, 331, 332]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PrimitiveInt"}, "widgets_values": [1024, "fixed"]}, {"id": 82, "type": "CropMask", "pos": [-735.735595703125, 954.50146484375], "size": [232.6815948486328, 141.9348602294922], "flags": {}, "order": 21, "mode": 4, "inputs": [{"localized_name": "遮罩", "name": "mask", "type": "MASK", "link": 209}, {"localized_name": "x", "name": "x", "type": "INT", "widget": {"name": "x"}, "link": 225}, {"localized_name": "y", "name": "y", "type": "INT", "widget": {"name": "y"}, "link": 210}, {"localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 215}, {"localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 214}], "outputs": [{"localized_name": "遮罩", "name": "MASK", "type": "MASK", "slot_index": 0, "links": [328]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CropMask"}, "widgets_values": [0, 0, 512, 512]}, {"id": 117, "type": "ResizeMask", "pos": [-479.9787902832031, 1130.4896240234375], "size": [270, 194], "flags": {}, "order": 24, "mode": 4, "inputs": [{"localized_name": "mask", "name": "mask", "type": "MASK", "link": 328}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 331}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 332}, {"localized_name": "keep_proportions", "name": "keep_proportions", "type": "BOOLEAN", "widget": {"name": "keep_proportions"}, "link": null}, {"localized_name": "upscale_method", "name": "upscale_method", "type": "COMBO", "widget": {"name": "upscale_method"}, "link": null}, {"localized_name": "crop", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"localized_name": "mask", "name": "mask", "type": "MASK", "links": [329]}, {"localized_name": "width", "name": "width", "type": "INT", "links": null}, {"localized_name": "height", "name": "height", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.1.0", "Node name for S&R": "ResizeMask"}, "widgets_values": [512, 512, false, "bicubic", "disabled"]}, {"id": 78, "type": "easy showAnything", "pos": [-700.8305053710938, 141.0292510986328], "size": [210, 88], "flags": {"collapsed": false}, "order": 18, "mode": 0, "inputs": [{"localized_name": "输入任何", "name": "anything", "shape": 7, "type": "*", "link": 197}], "outputs": [{"localized_name": "输出", "name": "output", "type": "*", "links": null}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy showAnything"}, "widgets_values": []}, {"id": 68, "type": "Any Switch (rgthree)", "pos": [-1008.403564453125, 129.6139678955078], "size": [252, 106], "flags": {}, "order": 8, "mode": 0, "inputs": [{"dir": 3, "name": "any_01", "type": "MASK", "link": 317}, {"dir": 3, "name": "any_02", "type": "MASK", "link": null}, {"dir": 3, "name": "any_03", "type": "MASK", "link": null}, {"dir": 3, "name": "any_04", "type": "MASK", "link": null}, {"name": "any_05", "type": "MASK", "link": null}], "outputs": [{"dir": 4, "label": "MASK", "name": "*", "shape": 3, "type": "MASK", "slot_index": 0, "links": [181]}], "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0"}, "widgets_values": []}, {"id": 73, "type": "MaskToSEGS", "pos": [-937.22802734375, 304.865478515625], "size": [315, 154], "flags": {"collapsed": true}, "order": 10, "mode": 0, "inputs": [{"localized_name": "mask", "name": "mask", "type": "MASK", "link": 181}, {"localized_name": "combined", "name": "combined", "type": "BOOLEAN", "widget": {"name": "combined"}, "link": null}, {"localized_name": "crop_factor", "name": "crop_factor", "type": "FLOAT", "widget": {"name": "crop_factor"}, "link": null}, {"localized_name": "bbox_fill", "name": "bbox_fill", "type": "BOOLEAN", "widget": {"name": "bbox_fill"}, "link": null}, {"localized_name": "drop_size", "name": "drop_size", "type": "INT", "widget": {"name": "drop_size"}, "link": null}, {"localized_name": "contour_fill", "name": "contour_fill", "type": "BOOLEAN", "widget": {"name": "contour_fill"}, "link": null}], "outputs": [{"localized_name": "SEGS", "name": "SEGS", "type": "SEGS", "slot_index": 0, "links": [182]}], "properties": {"cnr_id": "comfyui-impact-pack", "ver": "8.14.2", "Node name for S&R": "MaskToSEGS"}, "widgets_values": [false, 3, false, 10, false]}, {"id": 79, "type": "easy showAnything", "pos": [-443.9531555175781, 166.80706787109375], "size": [210, 88], "flags": {"collapsed": false}, "order": 20, "mode": 0, "inputs": [{"localized_name": "输入任何", "name": "anything", "shape": 7, "type": "*", "link": 198}], "outputs": [{"localized_name": "输出", "name": "output", "type": "*", "links": null}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy showAnything"}, "widgets_values": []}, {"id": 72, "type": "ImageResize+", "pos": [-811.7279052734375, 683.3562622070312], "size": [315, 218], "flags": {}, "order": 23, "mode": 4, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 336}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 326}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 327}, {"localized_name": "interpolation", "name": "interpolation", "type": "COMBO", "widget": {"name": "interpolation"}, "link": null}, {"localized_name": "method", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}, {"localized_name": "condition", "name": "condition", "type": "COMBO", "widget": {"name": "condition"}, "link": null}, {"localized_name": "multiple_of", "name": "multiple_of", "type": "INT", "widget": {"name": "multiple_of"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [179, 294, 337]}, {"label": "宽度", "localized_name": "width", "name": "width", "type": "INT", "slot_index": 1, "links": []}, {"label": "高度", "localized_name": "height", "name": "height", "type": "INT", "slot_index": 2, "links": []}], "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "ImageResize+", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "widgets_values": [1024, 1024, "nearest", "stretch", "always", 0], "color": "rgba(0,0,0,.8)"}, {"id": 71, "type": "PreviewImage", "pos": [-972.3994750976562, 1139.6182861328125], "size": [443.807373046875, 246.00006103515625], "flags": {}, "order": 25, "mode": 4, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 179}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 64, "type": "ControlNetLoader", "pos": [11.67587661743164, -842.00634765625], "size": [315, 58], "flags": {}, "order": 5, "mode": 2, "inputs": [{"localized_name": "ControlNet名称", "name": "control_net_name", "type": "COMBO", "widget": {"name": "control_net_name"}, "link": null}], "outputs": [{"localized_name": "ControlNet", "name": "CONTROL_NET", "type": "CONTROL_NET", "links": [161]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ControlNetLoader"}, "widgets_values": ["xinsir\\diffusion_pytorch_model_DepthXL.safetensors"]}, {"id": 63, "type": "ControlNetApplyAdvanced", "pos": [-30.775310516357422, -686.6383666992188], "size": [315, 186], "flags": {}, "order": 29, "mode": 2, "inputs": [{"label": "positive", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 156}, {"label": "negative", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 157}, {"label": "control_net", "localized_name": "ControlNet", "name": "control_net", "type": "CONTROL_NET", "link": 161}, {"label": "image", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 323}, {"localized_name": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}, {"localized_name": "强度", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}, "link": null}, {"localized_name": "开始百分比", "name": "start_percent", "type": "FLOAT", "widget": {"name": "start_percent"}, "link": null}, {"localized_name": "结束百分比", "name": "end_percent", "type": "FLOAT", "widget": {"name": "end_percent"}, "link": null}], "outputs": [{"label": "positive", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [246]}, {"label": "negative", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [247]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ControlNetApplyAdvanced"}, "widgets_values": [0.5, 0, 1]}, {"id": 98, "type": "PreviewImage", "pos": [-34.051109313964844, -356.1424560546875], "size": [463.5942687988281, 292.39581298828125], "flags": {}, "order": 28, "mode": 2, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 322}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 94, "type": "ControlNetLoader", "pos": [498.2930908203125, -868.6502685546875], "size": [315, 58], "flags": {}, "order": 6, "mode": 2, "inputs": [{"localized_name": "ControlNet名称", "name": "control_net_name", "type": "COMBO", "widget": {"name": "control_net_name"}, "link": null}], "outputs": [{"localized_name": "ControlNet", "name": "CONTROL_NET", "type": "CONTROL_NET", "links": [248]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ControlNetLoader"}, "widgets_values": ["CN-anytest_v3-50000_am_dim256.safetensors"]}, {"id": 93, "type": "ControlNetApplyAdvanced", "pos": [524.2220458984375, -646.6052856445312], "size": [315, 186], "flags": {"collapsed": false}, "order": 32, "mode": 2, "inputs": [{"label": "positive", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 246}, {"label": "negative", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 247}, {"label": "control_net", "localized_name": "ControlNet", "name": "control_net", "type": "CONTROL_NET", "link": 248}, {"label": "image", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 321}, {"localized_name": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}, {"localized_name": "强度", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}, "link": null}, {"localized_name": "开始百分比", "name": "start_percent", "type": "FLOAT", "widget": {"name": "start_percent"}, "link": null}, {"localized_name": "结束百分比", "name": "end_percent", "type": "FLOAT", "widget": {"name": "end_percent"}, "link": null}], "outputs": [{"label": "positive", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": []}, {"label": "negative", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ControlNetApplyAdvanced"}, "widgets_values": [0.3, 0, 1]}, {"id": 92, "type": "PreviewImage", "pos": [609.0265502929688, -390.1661682128906], "size": [465.6659240722656, 298.5339660644531], "flags": {}, "order": 30, "mode": 2, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 324}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 89, "type": "PreviewImage", "pos": [1301.1953125, 807.4132690429688], "size": [1195.3343505859375, 780.8441162109375], "flags": {}, "order": 39, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 240}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 119, "type": "PreviewImage", "pos": [734.1458129882812, 842.9989013671875], "size": [508.3212585449219, 740.3815307617188], "flags": {}, "order": 36, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 338}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 75, "type": "SegsToCombinedMask", "pos": [-444.28985595703125, 325.2391662597656], "size": [211.85116577148438, 26], "flags": {}, "order": 15, "mode": 0, "inputs": [{"localized_name": "segs", "name": "segs", "type": "SEGS", "link": 183}], "outputs": [{"localized_name": "遮罩", "name": "MASK", "type": "MASK", "slot_index": 0, "links": [185, 209]}], "properties": {"cnr_id": "comfyui-impact-pack", "ver": "8.14.2", "Node name for S&R": "SegsToCombinedMask"}, "widgets_values": []}, {"id": 74, "type": "ImpactSEGSPicker", "pos": [-709.5494384765625, 286.3276672363281], "size": [210, 132], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "segs", "name": "segs", "type": "SEGS", "link": 182}, {"localized_name": "fallback_image_opt", "name": "fallback_image_opt", "shape": 7, "type": "IMAGE", "link": 186}, {"localized_name": "picks", "name": "picks", "type": "STRING", "widget": {"name": "picks"}, "link": null}], "outputs": [{"localized_name": "SEGS", "name": "SEGS", "type": "SEGS", "slot_index": 0, "links": [183]}], "properties": {"cnr_id": "comfyui-impact-pack", "ver": "8.14.2", "Node name for S&R": "ImpactSEGSPicker"}, "widgets_values": ["1", "image"]}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [-903.0326538085938, -530.8593139648438], "size": [315, 98], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "Checkpoint名称", "name": "ckpt_name", "type": "COMBO", "widget": {"name": "ckpt_name"}, "link": null}], "outputs": [{"label": "MODEL", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [325]}, {"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [95]}, {"label": "VAE", "localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 2, "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["animaPencilXL_v500.safetensors"]}, {"id": 77, "type": "LayerMask: Mask<PERSON>row", "pos": [785.0127563476562, 549.7725830078125], "size": [315, 106], "flags": {"collapsed": false}, "order": 22, "mode": 0, "inputs": [{"label": "遮罩", "localized_name": "mask", "name": "mask", "type": "MASK", "link": 311}, {"localized_name": "invert_mask", "name": "invert_mask", "type": "BOOLEAN", "widget": {"name": "invert_mask"}, "link": null}, {"localized_name": "grow", "name": "grow", "type": "INT", "widget": {"name": "grow"}, "link": null}, {"localized_name": "blur", "name": "blur", "type": "INT", "widget": {"name": "blur"}, "link": null}], "outputs": [{"label": "遮罩", "localized_name": "mask", "name": "mask", "type": "MASK", "slot_index": 0, "links": [193]}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerMask: Mask<PERSON>row", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "widgets_values": [false, 4, 4], "color": "rgba(0,0,0,.8)"}, {"id": 35, "type": "VAEDecode", "pos": [846.4210205078125, 271.0527648925781], "size": [210, 46], "flags": {}, "order": 33, "mode": 0, "inputs": [{"label": "samples", "localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 81}, {"label": "vae", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 82}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [229, 233, 338, 340]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 17, "type": "PreviewImage", "pos": [2049.9599609375, 163.6477508544922], "size": [1142.9974365234375, 625.2789306640625], "flags": {}, "order": 40, "mode": 0, "inputs": [{"label": "images", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 194}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 56, "type": "BNK_CLIPTextEncodeAdvanced", "pos": [-592, -442], "size": [372.8017272949219, 166.20712280273438], "flags": {}, "order": 14, "mode": 0, "inputs": [{"label": "clip", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 147}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}, {"localized_name": "token_normalization", "name": "token_normalization", "type": "COMBO", "widget": {"name": "token_normalization"}, "link": null}, {"localized_name": "weight_interpretation", "name": "weight_interpretation", "type": "COMBO", "widget": {"name": "weight_interpretation"}, "link": null}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [156, 319]}], "properties": {"cnr_id": "ComfyUI_ADV_CLIP_emb", "ver": "63984deefb005da1ba90a1175e21d91040da38ab", "Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "widgets_values": ["\nA little girl, wearing a yellow sweater and a white skirt, white collar,standing", "none", "A1111"]}, {"id": 87, "type": "ImageResize+", "pos": [-81.0478744506836, 1192.472900390625], "size": [315, 218], "flags": {}, "order": 34, "mode": 4, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 229}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 230}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 231}, {"localized_name": "interpolation", "name": "interpolation", "type": "COMBO", "widget": {"name": "interpolation"}, "link": null}, {"localized_name": "method", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}, {"localized_name": "condition", "name": "condition", "type": "COMBO", "widget": {"name": "condition"}, "link": null}, {"localized_name": "multiple_of", "name": "multiple_of", "type": "INT", "widget": {"name": "multiple_of"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": []}, {"label": "宽度", "localized_name": "width", "name": "width", "type": "INT", "slot_index": 1, "links": []}, {"label": "高度", "localized_name": "height", "name": "height", "type": "INT", "slot_index": 2, "links": []}], "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "ImageResize+", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "widgets_values": [1024, 1024, "bicubic", "stretch", "always", 0], "color": "rgba(0,0,0,.8)"}, {"id": 76, "type": "ImageCompositeMasked", "pos": [831.8697509765625, 347.2520751953125], "size": [315, 146], "flags": {}, "order": 38, "mode": 0, "inputs": [{"label": "目标图像", "localized_name": "目标图像", "name": "destination", "type": "IMAGE", "link": 188}, {"label": "源图像", "localized_name": "来源图像", "name": "source", "type": "IMAGE", "link": 239}, {"label": "遮罩", "localized_name": "遮罩", "name": "mask", "shape": 7, "type": "MASK", "link": 193}, {"label": "X", "localized_name": "x", "name": "x", "type": "INT", "widget": {"name": "x"}, "link": 228}, {"label": "Y", "localized_name": "y", "name": "y", "type": "INT", "widget": {"name": "y"}, "link": 227}, {"localized_name": "缩放来源图像", "name": "resize_source", "type": "BOOLEAN", "widget": {"name": "resize_source"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [194]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageCompositeMasked", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "widgets_values": [0, 0, false], "color": "rgba(0,0,0,.8)"}, {"id": 80, "type": "LayerColor: ColorAdapter", "pos": [1269.406005859375, -94.78841400146484], "size": [210, 80], "flags": {"collapsed": false, "pinned": false}, "order": 37, "mode": 0, "inputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 340}, {"label": "color_ref_image", "localized_name": "color_ref_image", "name": "color_ref_image", "type": "IMAGE", "link": 294}, {"localized_name": "opacity", "name": "opacity", "type": "INT", "widget": {"name": "opacity"}, "link": null}], "outputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [239, 240]}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerColor: ColorAdapter", "ttNbgOverride": {"color": "#1f1f48", "groupcolor": "#88A"}}, "widgets_values": [60], "color": "#1f1f48"}], "links": [[64, 22, 0, 12, 0, "CLIP"], [69, 33, 0, 32, 4, "BRMODEL"], [75, 32, 0, 13, 0, "MODEL"], [79, 32, 3, 13, 3, "LATENT"], [81, 13, 0, 35, 0, "LATENT"], [82, 16, 0, 35, 1, "VAE"], [86, 16, 0, 32, 1, "VAE"], [93, 38, 0, 32, 0, "MODEL"], [94, 38, 1, 22, 0, "CLIP"], [95, 4, 1, 38, 1, "CLIP"], [147, 22, 0, 56, 0, "CLIP"], [156, 56, 0, 63, 0, "CONDITIONING"], [157, 12, 0, 63, 1, "CONDITIONING"], [160, 32, 1, 13, 1, "CONDITIONING"], [161, 64, 0, 63, 2, "CONTROL_NET"], [174, 69, 6, 70, 1, "INT"], [175, 69, 7, 70, 2, "INT"], [179, 72, 0, 71, 0, "IMAGE"], [181, 68, 0, 73, 0, "MASK"], [182, 73, 0, 74, 0, "SEGS"], [183, 74, 0, 75, 0, "SEGS"], [185, 75, 0, 69, 0, "MASK"], [186, 21, 0, 74, 1, "IMAGE"], [188, 21, 0, 76, 0, "IMAGE"], [193, 77, 0, 76, 2, "MASK"], [194, 76, 0, 17, 0, "IMAGE"], [196, 32, 2, 13, 2, "CONDITIONING"], [197, 69, 6, 78, 0, "*"], [198, 69, 7, 79, 0, "*"], [204, 21, 0, 70, 0, "IMAGE"], [207, 69, 2, 70, 4, "INT"], [208, 69, 3, 70, 3, "INT"], [209, 75, 0, 82, 0, "MASK"], [210, 69, 2, 82, 2, "INT"], [214, 69, 7, 82, 4, "INT"], [215, 69, 6, 82, 3, "INT"], [225, 69, 3, 82, 1, "INT"], [227, 69, 2, 76, 4, "INT"], [228, 69, 3, 76, 3, "INT"], [229, 35, 0, 87, 0, "IMAGE"], [230, 69, 6, 87, 1, "INT"], [231, 69, 7, 87, 2, "INT"], [233, 35, 0, 88, 0, "IMAGE"], [239, 80, 0, 76, 1, "IMAGE"], [240, 80, 0, 89, 0, "IMAGE"], [246, 63, 0, 93, 0, "CONDITIONING"], [247, 63, 1, 93, 1, "CONDITIONING"], [248, 94, 0, 93, 2, "CONTROL_NET"], [286, 100, 0, 32, 2, "IMAGE"], [294, 72, 0, 80, 1, "IMAGE"], [311, 114, 0, 77, 0, "MASK"], [316, 69, 0, 114, 1, "MASK"], [317, 21, 1, 68, 0, "MASK"], [319, 56, 0, 32, 5, "CONDITIONING"], [320, 12, 0, 32, 6, "CONDITIONING"], [321, 100, 0, 93, 3, "IMAGE"], [322, 100, 0, 98, 0, "IMAGE"], [323, 100, 0, 63, 3, "IMAGE"], [324, 100, 0, 92, 0, "IMAGE"], [325, 4, 0, 38, 0, "MODEL"], [326, 115, 0, 72, 1, "INT"], [327, 115, 0, 72, 2, "INT"], [328, 82, 0, 117, 0, "MASK"], [329, 117, 0, 32, 3, "MASK"], [331, 115, 0, 117, 1, "INT"], [332, 115, 0, 117, 2, "INT"], [336, 70, 0, 72, 0, "IMAGE"], [337, 72, 0, 100, 0, "*"], [338, 35, 0, 119, 0, "IMAGE"], [340, 35, 0, 80, 0, "IMAGE"]], "groups": [{"id": 3, "title": "Group", "bounding": [-1045.8143310546875, 539.0109252929688, 1340.203857421875, 972.74755859375], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "架子啊模型", "bounding": [-1052, -490, 855, 453], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "选择蒙版切割图片", "bounding": [-1046, 59.066688537597656, 1085.0802001953125, 444.52471923828125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "Brushnet重绘", "bounding": [75, 38, 1054, 870], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 8, "title": "CN控制", "bounding": [-188, -949, 1340, 932], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 9, "title": "校色复原", "bounding": [1220, -233, 772, 269], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 10, "title": "Group", "bounding": [321.1341552734375, 944.8869018554688, 860.8291015625, 474.6335754394531], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.4090909090909112, "offset": [2237.1237335572887, 191.40226018899824]}, "frontendVersion": "1.18.9", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}