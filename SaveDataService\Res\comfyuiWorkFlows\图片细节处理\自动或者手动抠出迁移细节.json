{"id": "18d52e24-faab-483d-aec5-f035b99248d0", "revision": 0, "last_node_id": 114, "last_link_id": 238, "nodes": [{"id": 95, "type": "LayerUtility: ColorPicker", "pos": [-464, -1017], "size": [210, 94], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "color", "name": "color", "type": "COLOR", "widget": {"name": "color"}, "link": null}, {"localized_name": "mode", "name": "mode", "type": "COMBO", "widget": {"name": "mode"}, "link": null}], "outputs": [{"localized_name": "value", "name": "value", "type": "STRING", "slot_index": 0, "links": [180]}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerUtility: ColorPicker"}, "widgets_values": ["#f7f7f7", "HEX"], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 102, "type": "SimpleMath+", "pos": [-451.9440002441406, -728.8571166992188], "size": [315, 78], "flags": {}, "order": 1, "mode": 4, "inputs": [{"localized_name": "a", "name": "a", "shape": 7, "type": "INT,FLOAT", "link": null}, {"localized_name": "b", "name": "b", "shape": 7, "type": "INT,FLOAT", "link": null}, {"localized_name": "value", "name": "value", "type": "STRING", "widget": {"name": "value"}, "link": null}], "outputs": [{"localized_name": "整数", "name": "INT", "type": "INT", "slot_index": 0, "links": []}, {"localized_name": "浮点", "name": "FLOAT", "type": "FLOAT", "links": null}], "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "SimpleMath+"}, "widgets_values": [""]}, {"id": 92, "type": "PreviewImage", "pos": [728.2693481445312, -605.71826171875], "size": [575.1788330078125, 295.68121337890625], "flags": {}, "order": 7, "mode": 2, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 205}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 105, "type": "GetImageSize+", "pos": [-412.4648132324219, -894.6340942382812], "size": [159.50155639648438, 66], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 200}], "outputs": [{"localized_name": "width", "name": "width", "type": "INT", "links": [208]}, {"localized_name": "height", "name": "height", "type": "INT", "links": [209]}, {"localized_name": "count", "name": "count", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 90, "type": "PreviewImage", "pos": [389.1820983886719, -493.98443603515625], "size": [336.8946228027344, 737.948974609375], "flags": {}, "order": 4, "mode": 2, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 198}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 85, "type": "LayerMask: SegmentAnythingUltra V2", "pos": [-37.711708068847656, -509.7351989746094], "size": [330.8785095214844, 366], "flags": {}, "order": 3, "mode": 2, "inputs": [{"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 158}, {"localized_name": "SAM模型", "name": "sam_model", "type": "COMBO", "widget": {"name": "sam_model"}, "link": null}, {"localized_name": "Grounding Dino模型", "name": "grounding_dino_model", "type": "COMBO", "widget": {"name": "grounding_dino_model"}, "link": null}, {"localized_name": "阈值", "name": "threshold", "type": "FLOAT", "widget": {"name": "threshold"}, "link": null}, {"localized_name": "细节方法", "name": "detail_method", "type": "COMBO", "widget": {"name": "detail_method"}, "link": null}, {"localized_name": "细节腐蚀", "name": "detail_erode", "type": "INT", "widget": {"name": "detail_erode"}, "link": null}, {"localized_name": "细节膨胀", "name": "detail_dilate", "type": "INT", "widget": {"name": "detail_dilate"}, "link": null}, {"localized_name": "黑点", "name": "black_point", "type": "FLOAT", "widget": {"name": "black_point"}, "link": null}, {"localized_name": "白点", "name": "white_point", "type": "FLOAT", "widget": {"name": "white_point"}, "link": null}, {"localized_name": "处理细节", "name": "process_detail", "type": "BOOLEAN", "widget": {"name": "process_detail"}, "link": null}, {"localized_name": "提示", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": null}, {"localized_name": "设备", "name": "device", "type": "COMBO", "widget": {"name": "device"}, "link": null}, {"localized_name": "最大百万像素", "name": "max_megapixels", "type": "FLOAT", "widget": {"name": "max_megapixels"}, "link": null}, {"localized_name": "缓存模型", "name": "cache_model", "type": "BOOLEAN", "widget": {"name": "cache_model"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [205, 206]}, {"localized_name": "mask", "name": "mask", "type": "MASK", "slot_index": 1, "links": []}], "properties": {"cnr_id": "ComfyUI_LayerStyle_Advance", "ver": "fe35b54bd2781206994176f8913db4afabffcdb1", "Node name for S&R": "LayerMask: SegmentAnythingUltra V2"}, "widgets_values": ["sam_vit_h (2.56GB)", "GroundingDINO_SwinB (938MB)", 0.3, "VITMatte", 6, 6, 0.15, 0.99, true, "hair", "cuda", 2, false], "color": "rgba(27, 80, 119, 0.7)"}, {"id": 97, "type": "PreviewImage", "pos": [1755.8245849609375, -551.403076171875], "size": [491.9326477050781, 312.9626770019531], "flags": {}, "order": 14, "mode": 2, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 189}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 112, "type": "PreviewImage", "pos": [414.78961181640625, -1662.634033203125], "size": [140, 246], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 226}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 111, "type": "MaskPreview+", "pos": [656.6830444335938, -1609.1783447265625], "size": [500.89678955078125, 258], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "mask", "name": "mask", "type": "MASK", "link": 225}], "outputs": [], "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "MaskPreview+"}, "widgets_values": []}, {"id": 99, "type": "LayerUtility: ImageBlendAdvance V2", "pos": [1364.81396484375, -561.59521484375], "size": [315, 338], "flags": {}, "order": 11, "mode": 2, "inputs": [{"localized_name": "background_image", "name": "background_image", "type": "IMAGE", "link": 186}, {"localized_name": "layer_image", "name": "layer_image", "type": "IMAGE", "link": 206}, {"localized_name": "layer_mask", "name": "layer_mask", "shape": 7, "type": "MASK", "link": null}, {"localized_name": "invert_mask", "name": "invert_mask", "type": "BOOLEAN", "widget": {"name": "invert_mask"}, "link": null}, {"localized_name": "blend_mode", "name": "blend_mode", "type": "COMBO", "widget": {"name": "blend_mode"}, "link": null}, {"localized_name": "opacity", "name": "opacity", "type": "INT", "widget": {"name": "opacity"}, "link": null}, {"localized_name": "x_percent", "name": "x_percent", "type": "FLOAT", "widget": {"name": "x_percent"}, "link": null}, {"localized_name": "y_percent", "name": "y_percent", "type": "FLOAT", "widget": {"name": "y_percent"}, "link": null}, {"localized_name": "mirror", "name": "mirror", "type": "COMBO", "widget": {"name": "mirror"}, "link": null}, {"localized_name": "scale", "name": "scale", "type": "FLOAT", "widget": {"name": "scale"}, "link": null}, {"localized_name": "aspect_ratio", "name": "aspect_ratio", "type": "FLOAT", "widget": {"name": "aspect_ratio"}, "link": null}, {"localized_name": "rotate", "name": "rotate", "type": "FLOAT", "widget": {"name": "rotate"}, "link": null}, {"localized_name": "transform_method", "name": "transform_method", "type": "COMBO", "widget": {"name": "transform_method"}, "link": null}, {"localized_name": "anti_aliasing", "name": "anti_aliasing", "type": "INT", "widget": {"name": "anti_aliasing"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [189]}, {"localized_name": "mask", "name": "mask", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerUtility: ImageBlendAdvance V2"}, "widgets_values": [true, "normal", 100, 50, 50, "None", 1, 1, 0, "lanc<PERSON>s", 0], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 3, "type": "LoadImage", "pos": [-1080.885009765625, -1218.917724609375], "size": [589.18896484375, 468.7266845703125], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [158, 198, 200, 217]}, {"label": "遮罩", "localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": [210]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["clipspace/clipspace-mask-1796224.2000000011.png [input]", "image"]}, {"id": 110, "type": "ImageCrop+", "pos": [177.53387451171875, -1323.21875], "size": [315, 194], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 217}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 218}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 219}, {"localized_name": "position", "name": "position", "type": "COMBO", "widget": {"name": "position"}, "link": null}, {"localized_name": "x_offset", "name": "x_offset", "type": "INT", "widget": {"name": "x_offset"}, "link": 221}, {"localized_name": "y_offset", "name": "y_offset", "type": "INT", "widget": {"name": "y_offset"}, "link": 220}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [226, 236]}, {"localized_name": "x", "name": "x", "type": "INT", "links": [223]}, {"localized_name": "y", "name": "y", "type": "INT", "links": [224]}], "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "ImageCrop+"}, "widgets_values": [256, 256, "top-left", 0, 0]}, {"id": 107, "type": "ImageCompositeMasked", "pos": [760.7720947265625, -1234.6527099609375], "size": [315, 146], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "目标图像", "localized_name": "目标图像", "name": "destination", "type": "IMAGE", "link": 238}, {"label": "源图像", "localized_name": "来源图像", "name": "source", "type": "IMAGE", "link": 236}, {"label": "遮罩", "localized_name": "遮罩", "name": "mask", "shape": 7, "type": "MASK", "link": 237}, {"label": "X", "localized_name": "x", "name": "x", "type": "INT", "widget": {"name": "x"}, "link": 223}, {"label": "Y", "localized_name": "y", "name": "y", "type": "INT", "widget": {"name": "y"}, "link": 224}, {"localized_name": "缩放来源图像", "name": "resize_source", "type": "BOOLEAN", "widget": {"name": "resize_source"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [215]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageCompositeMasked", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "widgets_values": [0, 0, false], "color": "rgba(0,0,0,.8)"}, {"id": 108, "type": "Mask Crop Region", "pos": [-245.991943359375, -1308.5374755859375], "size": [315, 222], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "mask", "name": "mask", "type": "MASK", "link": 210}, {"localized_name": "padding", "name": "padding", "type": "INT", "widget": {"name": "padding"}, "link": null}, {"localized_name": "region_type", "name": "region_type", "type": "COMBO", "widget": {"name": "region_type"}, "link": null}], "outputs": [{"localized_name": "cropped_mask", "name": "cropped_mask", "type": "MASK", "links": [225, 237]}, {"localized_name": "crop_data", "name": "crop_data", "type": "CROP_DATA", "links": null}, {"localized_name": "top_int", "name": "top_int", "type": "INT", "slot_index": 2, "links": [220]}, {"localized_name": "left_int", "name": "left_int", "type": "INT", "slot_index": 3, "links": [221]}, {"localized_name": "right_int", "name": "right_int", "type": "INT", "links": null}, {"localized_name": "bottom_int", "name": "bottom_int", "type": "INT", "links": null}, {"localized_name": "width_int", "name": "width_int", "type": "INT", "slot_index": 6, "links": [218]}, {"localized_name": "height_int", "name": "height_int", "type": "INT", "slot_index": 7, "links": [219]}], "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "1.0.2", "Node name for S&R": "Mask Crop Region"}, "widgets_values": [64, "dominant"]}, {"id": 94, "type": "LayerUtility: ColorImage V2", "pos": [368.9263916015625, -1041.0029296875], "size": [315, 130], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "size_as", "name": "size_as", "shape": 7, "type": "*", "link": null}, {"localized_name": "size", "name": "size", "type": "COMBO", "widget": {"name": "size"}, "link": null}, {"localized_name": "custom_width", "name": "custom_width", "type": "INT", "widget": {"name": "custom_width"}, "link": 208}, {"localized_name": "custom_height", "name": "custom_height", "type": "INT", "widget": {"name": "custom_height"}, "link": 209}, {"localized_name": "color", "name": "color", "type": "STRING", "widget": {"name": "color"}, "link": 180}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [186, 238]}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerUtility: ColorImage V2"}, "widgets_values": ["custom", 512, 512, "#000000"], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 109, "type": "PreviewImage", "pos": [1124.2640380859375, -1257.293212890625], "size": [488.9880065917969, 246], "flags": {}, "order": 15, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 215}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}], "links": [[158, 3, 0, 85, 0, "IMAGE"], [180, 95, 0, 94, 4, "STRING"], [186, 94, 0, 99, 0, "IMAGE"], [189, 99, 0, 97, 0, "IMAGE"], [198, 3, 0, 90, 0, "IMAGE"], [200, 3, 0, 105, 0, "IMAGE"], [205, 85, 0, 92, 0, "IMAGE"], [206, 85, 0, 99, 1, "IMAGE"], [208, 105, 0, 94, 2, "INT"], [209, 105, 1, 94, 3, "INT"], [210, 3, 1, 108, 0, "MASK"], [215, 107, 0, 109, 0, "IMAGE"], [217, 3, 0, 110, 0, "IMAGE"], [218, 108, 6, 110, 1, "INT"], [219, 108, 7, 110, 2, "INT"], [220, 108, 2, 110, 5, "INT"], [221, 108, 3, 110, 4, "INT"], [223, 110, 1, 107, 3, "INT"], [224, 110, 2, 107, 4, "INT"], [225, 108, 0, 111, 0, "MASK"], [226, 110, 0, 112, 0, "IMAGE"], [236, 110, 0, 107, 1, "IMAGE"], [237, 108, 0, 107, 2, "MASK"], [238, 94, 0, 107, 0, "IMAGE"]], "groups": [{"id": 1, "title": "Group", "bounding": [-151, -669, 2776, 1497], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 1.2839025177495051, "offset": [1760.6540673595202, 1473.8390721941983]}, "frontendVersion": "1.18.9", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}