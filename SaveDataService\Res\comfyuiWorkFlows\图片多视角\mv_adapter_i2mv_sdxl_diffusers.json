{"id": "fbfeddf5-17bf-45d2-81cc-d5ac9dbe4479", "revision": 0, "last_node_id": 12, "last_link_id": 12, "nodes": [{"id": 6, "type": "DiffusersMVModelMakeup", "pos": [944.978759765625, 234.29940795898438], "size": [315, 218], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "pipeline", "name": "pipeline", "type": "PIPELINE", "link": 11}, {"localized_name": "scheduler", "name": "scheduler", "type": "SCHEDULER", "link": 3}, {"localized_name": "autoencoder", "name": "autoencoder", "type": "AUTOENCODER", "link": 4}, {"localized_name": "load_mvadapter", "name": "load_mvadapter", "type": "BOOLEAN", "widget": {"name": "load_mvadapter"}, "link": null}, {"localized_name": "adapter_path", "name": "adapter_path", "type": "STRING", "widget": {"name": "adapter_path"}, "link": null}, {"localized_name": "adapter_name", "name": "adapter_name", "type": "COMBO", "widget": {"name": "adapter_name"}, "link": null}, {"localized_name": "num_views", "name": "num_views", "type": "INT", "widget": {"name": "num_views"}, "link": null}, {"localized_name": "enable_vae_slicing", "name": "enable_vae_slicing", "shape": 7, "type": "BOOLEAN", "widget": {"name": "enable_vae_slicing"}, "link": null}, {"localized_name": "enable_vae_tiling", "name": "enable_vae_tiling", "shape": 7, "type": "BOOLEAN", "widget": {"name": "enable_vae_tiling"}, "link": null}], "outputs": [{"localized_name": "PIPELINE", "name": "PIPELINE", "type": "PIPELINE", "slot_index": 0, "links": [5]}], "properties": {"cnr_id": "comfyui-mvadapter", "ver": "1.0.1", "Node name for S&R": "DiffusersMVModelMakeup"}, "widgets_values": [true, "huanngzh/mv-adapter", "mvadapter_i2mv_sdxl.safetensors", 6, true, false]}, {"id": 5, "type": "DiffusersMVVaeLoader", "pos": [519.3989868164062, 334.48828125], "size": [315, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "vae_name", "name": "vae_name", "type": "STRING", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "AUTOENCODER", "name": "AUTOENCODER", "type": "AUTOENCODER", "slot_index": 0, "links": [4]}], "properties": {"cnr_id": "comfyui-mvadapter", "ver": "1.0.1", "Node name for S&R": "DiffusersMVVaeLoader"}, "widgets_values": ["madebyollin/sdxl-vae-fp16-fix"]}, {"id": 3, "type": "DiffusersMVSchedulerLoader", "pos": [515.5944213867188, 125.65931701660156], "size": [327.5999755859375, 130], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "pipeline", "name": "pipeline", "type": "PIPELINE", "link": 12}, {"localized_name": "scheduler_name", "name": "scheduler_name", "type": "COMBO", "widget": {"name": "scheduler_name"}, "link": null}, {"localized_name": "shift_snr", "name": "shift_snr", "type": "BOOLEAN", "widget": {"name": "shift_snr"}, "link": null}, {"localized_name": "shift_mode", "name": "shift_mode", "type": "COMBO", "widget": {"name": "shift_mode"}, "link": null}, {"localized_name": "shift_scale", "name": "shift_scale", "type": "FLOAT", "widget": {"name": "shift_scale"}, "link": null}], "outputs": [{"localized_name": "SCHEDULER", "name": "SCHEDULER", "type": "SCHEDULER", "slot_index": 0, "links": [3]}], "properties": {"cnr_id": "comfyui-mvadapter", "ver": "1.0.1", "Node name for S&R": "DiffusersMVSchedulerLoader"}, "widgets_values": ["DDPM", true, "interpolated", 8]}, {"id": 2, "type": "BiRefNet", "pos": [521.8474731445312, -224.9335479736328], "size": [315, 58], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "ckpt_name", "name": "ckpt_name", "type": "STRING", "widget": {"name": "ckpt_name"}, "link": null}], "outputs": [{"localized_name": "FUNCTION", "name": "FUNCTION", "type": "FUNCTION", "slot_index": 0, "links": [6]}], "properties": {"cnr_id": "comfyui-mvadapter", "ver": "1.0.1", "Node name for S&R": "BiRefNet"}, "widgets_values": ["ZhengPeng7/BiRefNet"]}, {"id": 10, "type": "PreviewImage", "pos": [1337.1131591796875, -263.8614501953125], "size": [313.3982849121094, 246], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 8}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 9, "type": "ImagePreprocessor", "pos": [944.402099609375, 75.06153869628906], "size": [315, 102], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "remove_bg_fn", "name": "remove_bg_fn", "type": "FUNCTION", "link": 6}, {"localized_name": "image", "name": "image", "type": "IMAGE", "link": 7}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [8, 9]}], "properties": {"cnr_id": "comfyui-mvadapter", "ver": "1.0.1", "Node name for S&R": "ImagePreprocessor"}, "widgets_values": [768, 768]}, {"id": 11, "type": "PreviewImage", "pos": [1778.79638671875, -213.63694763183594], "size": [365.73077392578125, 534.254150390625], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 10}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 7, "type": "DiffusersMVSampler", "pos": [1346.986083984375, 104.88616180419922], "size": [400, 394], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "pipeline", "name": "pipeline", "type": "PIPELINE", "link": 5}, {"localized_name": "reference_image", "name": "reference_image", "shape": 7, "type": "IMAGE", "link": 9}, {"localized_name": "controlnet_image", "name": "controlnet_image", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "azimuth_degrees", "name": "azimuth_degrees", "shape": 7, "type": "LIST", "link": null}, {"localized_name": "num_views", "name": "num_views", "type": "INT", "widget": {"name": "num_views"}, "link": null}, {"localized_name": "prompt", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": null}, {"localized_name": "negative_prompt", "name": "negative_prompt", "type": "STRING", "widget": {"name": "negative_prompt"}, "link": null}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "controlnet_conditioning_scale", "name": "controlnet_conditioning_scale", "shape": 7, "type": "FLOAT", "widget": {"name": "controlnet_conditioning_scale"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [10]}], "properties": {"cnr_id": "comfyui-mvadapter", "ver": "1.0.1", "Node name for S&R": "DiffusersMVSampler"}, "widgets_values": [6, "A decorative figurine of a young anime-style girl", "watermark, ugly, deformed, noisy, blurry, low contrast", 768, 768, 50, 3, 87003760814249, "randomize", 1, [false, true], [false, true]]}, {"id": 12, "type": "DiffusersMVPipelineLoader", "pos": [495.7587890625, -84.21444702148438], "size": [315, 122], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "ckpt_name", "name": "ckpt_name", "type": "STRING", "widget": {"name": "ckpt_name"}, "link": null}, {"localized_name": "pipeline_name", "name": "pipeline_name", "type": "COMBO", "widget": {"name": "pipeline_name"}, "link": null}], "outputs": [{"localized_name": "PIPELINE", "name": "PIPELINE", "type": "PIPELINE", "slot_index": 0, "links": [11, 12]}, {"localized_name": "AUTOENCODER", "name": "AUTOENCODER", "type": "AUTOENCODER", "links": null}, {"localized_name": "SCHEDULER", "name": "SCHEDULER", "type": "SCHEDULER", "links": null}], "properties": {"cnr_id": "comfyui-mvadapter", "ver": "1.0.1", "Node name for S&R": "DiffusersMVPipelineLoader"}, "widgets_values": ["stabilityai/stable-diffusion-xl-base-1.0", "MVAdapterI2MVSDXLPipeline"]}, {"id": 8, "type": "LoadImage", "pos": [940.2247314453125, -300.4877014160156], "size": [315, 314], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [7]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["ComfyUI_00248_.png", "image"]}], "links": [[3, 3, 0, 6, 1, "SCHEDULER"], [4, 5, 0, 6, 2, "AUTOENCODER"], [5, 6, 0, 7, 0, "PIPELINE"], [6, 2, 0, 9, 0, "FUNCTION"], [7, 8, 0, 9, 1, "IMAGE"], [8, 9, 0, 10, 0, "IMAGE"], [9, 9, 0, 7, 1, "IMAGE"], [10, 7, 0, 11, 0, "IMAGE"], [11, 12, 0, 6, 0, "PIPELINE"], [12, 12, 0, 3, 0, "PIPELINE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.8264462809917354, "offset": [242.5139326613629, 313.37915715409395]}, "frontendVersion": "1.18.9", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}