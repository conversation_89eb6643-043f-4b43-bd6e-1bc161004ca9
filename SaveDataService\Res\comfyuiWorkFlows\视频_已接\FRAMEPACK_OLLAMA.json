{"id": "ce2cb810-7775-4564-8928-dd5bed1053cd", "revision": 0, "last_node_id": 120, "last_link_id": 220, "nodes": [{"id": 64, "type": "GetNode", "pos": [-330.2825622558594, -477.43524169921875], "size": [210, 60], "flags": {"collapsed": true}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP_VISION", "type": "CLIP_VISION", "links": [149]}], "title": "Get_ClipVisionModle", "properties": {}, "widgets_values": ["ClipVisionModle"], "color": "#233", "bgcolor": "#355", "shape": 1}, {"id": 68, "type": "GetNode", "pos": [-340.2825622558594, -657.435302734375], "size": [210, 34], "flags": {"collapsed": true}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [155]}], "title": "Get_VAE", "properties": {}, "widgets_values": ["VAE"], "color": "#322", "bgcolor": "#533", "shape": 1}, {"id": 69, "type": "GetNode", "pos": [-350.2825622558594, -27.43519401550293], "size": [210, 34], "flags": {"collapsed": true}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [158]}], "title": "Get_VAE", "properties": {}, "widgets_values": ["VAE"], "color": "#322", "bgcolor": "#533", "shape": 1}, {"id": 65, "type": "GetNode", "pos": [-360.2825622558594, 142.5647735595703], "size": [210, 34], "flags": {"collapsed": true}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP_VISION", "type": "CLIP_VISION", "links": [150]}], "title": "Get_ClipVisionModle", "properties": {}, "widgets_values": ["ClipVisionModle"], "color": "#233", "bgcolor": "#355", "shape": 1}, {"id": 50, "type": "ImageResize+", "pos": [-700.2825317382812, -787.4353637695312], "size": [315, 218], "flags": {}, "order": 33, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 122}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 128}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 127}, {"localized_name": "interpolation", "name": "interpolation", "type": "COMBO", "widget": {"name": "interpolation"}, "link": null}, {"localized_name": "method", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}, {"localized_name": "condition", "name": "condition", "type": "COMBO", "widget": {"name": "condition"}, "link": null}, {"localized_name": "multiple_of", "name": "multiple_of", "type": "INT", "widget": {"name": "multiple_of"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [125]}, {"localized_name": "width", "name": "width", "type": "INT", "links": null}, {"localized_name": "height", "name": "height", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_essentials", "ver": "76e9d1e4399bd025ce8b12c290753d58f9f53e93", "Node name for S&R": "ImageResize+", "aux_id": "kijai/ComfyUI_essentials"}, "widgets_values": [512, 512, "lanc<PERSON>s", "stretch", "always", 0], "shape": 1}, {"id": 51, "type": "FramePackFindNearestBucket", "pos": [-700.2825317382812, -907.435302734375], "size": [310, 80], "flags": {}, "order": 29, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 126}, {"localized_name": "base_resolution", "name": "base_resolution", "type": "INT", "widget": {"name": "base_resolution"}, "link": null}], "outputs": [{"localized_name": "width", "name": "width", "type": "INT", "links": [128, 136]}, {"localized_name": "height", "name": "height", "type": "INT", "links": [127, 137]}], "properties": {"aux_id": "kijai/ComfyUI-FramePackWrapper", "ver": "4f9030a9f4c0bd67d86adf3d3dc07e37118c40bd", "Node name for S&R": "FramePackFindNearestBucket"}, "widgets_values": [512], "shape": 1}, {"id": 48, "type": "GetImageSizeAndCount", "pos": [-340.2825622558594, -897.435302734375], "size": [277.20001220703125, 86], "flags": {}, "order": 37, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 125}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [116, 156]}, {"label": "432 width", "localized_name": "width", "name": "width", "type": "INT", "links": null}, {"label": "560 height", "localized_name": "height", "name": "height", "type": "INT", "links": null}, {"label": "1 count", "localized_name": "count", "name": "count", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "8ecf5cd05e0a1012087b0da90eea9a13674668db", "Node name for S&R": "GetImageSizeAndCount"}, "widgets_values": [], "shape": 1}, {"id": 59, "type": "ImageResize+", "pos": [-720.2825317382812, -257.4352111816406], "size": [315, 218], "flags": {}, "order": 34, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 138}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 136}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 137}, {"localized_name": "interpolation", "name": "interpolation", "type": "COMBO", "widget": {"name": "interpolation"}, "link": null}, {"localized_name": "method", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}, {"localized_name": "condition", "name": "condition", "type": "COMBO", "widget": {"name": "condition"}, "link": null}, {"localized_name": "multiple_of", "name": "multiple_of", "type": "INT", "widget": {"name": "multiple_of"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [139]}, {"localized_name": "width", "name": "width", "type": "INT", "links": null}, {"localized_name": "height", "name": "height", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_essentials", "ver": "76e9d1e4399bd025ce8b12c290753d58f9f53e93", "Node name for S&R": "ImageResize+", "aux_id": "kijai/ComfyUI_essentials"}, "widgets_values": [512, 512, "lanc<PERSON>s", "stretch", "always", 0], "shape": 1}, {"id": 62, "type": "VAEEncode", "pos": [-350.2825622558594, -117.43519592285156], "size": [210, 46], "flags": {}, "order": 43, "mode": 0, "inputs": [{"localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 152}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 158}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "links": [179]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "VAEEncode"}, "widgets_values": [], "color": "#322", "bgcolor": "#533", "shape": 1}, {"id": 91, "type": "Reroute", "pos": [149.71759033203125, 112.5648193359375], "size": [75, 26], "flags": {}, "order": 48, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 179}], "outputs": [{"name": "", "type": "LATENT", "links": [181]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 94, "type": "Reroute", "pos": [929.71728515625, 112.5648193359375], "size": [75, 26], "flags": {}, "order": 52, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 181}], "outputs": [{"name": "", "type": "LATENT", "links": [184]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 92, "type": "Reroute", "pos": [149.71759033203125, 72.56481170654297], "size": [75, 26], "flags": {}, "order": 46, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 180}], "outputs": [{"name": "", "type": "LATENT", "links": [182]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 89, "type": "Reroute", "pos": [149.71759033203125, 152.5647735595703], "size": [75, 26], "flags": {}, "order": 45, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 176}], "outputs": [{"name": "", "type": "CLIP_VISION_OUTPUT", "links": [178]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 100, "type": "Reroute", "pos": [149.71759033203125, 192.5648193359375], "size": [75, 26], "flags": {}, "order": 47, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 193}], "outputs": [{"name": "", "type": "CLIP_VISION_OUTPUT", "links": [191]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 17, "type": "CLIPVisionEncode", "pos": [-340.2825622558594, -607.4351196289062], "size": [380.4000244140625, 78], "flags": {}, "order": 40, "mode": 0, "inputs": [{"localized_name": "clip视觉", "name": "clip_vision", "type": "CLIP_VISION", "link": 149}, {"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 116}, {"localized_name": "裁剪", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"localized_name": "CLIP视觉输出", "name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "links": [176]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["center"], "color": "#233", "bgcolor": "#355", "shape": 1}, {"id": 57, "type": "CLIPVisionEncode", "pos": [-370.2825622558594, 12.56482219696045], "size": [380.4000244140625, 78], "flags": {}, "order": 42, "mode": 0, "inputs": [{"localized_name": "clip视觉", "name": "clip_vision", "type": "CLIP_VISION", "link": 150}, {"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 151}, {"localized_name": "裁剪", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"localized_name": "CLIP视觉输出", "name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "links": [193]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["center"], "color": "#233", "bgcolor": "#355", "shape": 1}, {"id": 87, "type": "Reroute", "pos": [929.71728515625, 192.5648193359375], "size": [75, 26], "flags": {}, "order": 51, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 191}], "outputs": [{"name": "", "type": "CLIP_VISION_OUTPUT", "links": [173]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 83, "type": "Label (rgthree)", "pos": [139.71762084960938, -937.435302734375], "size": [269.53125, 50], "flags": {"allow_interaction": true}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "title": "ADD Motion", "properties": {"fontSize": 50, "fontFamily": "<PERSON><PERSON>", "fontColor": "#ffffff", "textAlign": "left", "backgroundColor": "transparent", "padding": 0, "borderRadius": 0}, "color": "#fff0", "bgcolor": "#fff0", "shape": 1}, {"id": 60, "type": "GetImageSizeAndCount", "pos": [-360.2825622558594, -257.4352111816406], "size": [277.20001220703125, 86], "flags": {}, "order": 38, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 139}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [151, 152]}, {"label": "432 width", "localized_name": "width", "name": "width", "type": "INT", "links": null}, {"label": "560 height", "localized_name": "height", "name": "height", "type": "INT", "links": null}, {"label": "1 count", "localized_name": "count", "name": "count", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "8ecf5cd05e0a1012087b0da90eea9a13674668db", "Node name for S&R": "GetImageSizeAndCount"}, "widgets_values": [], "shape": 1}, {"id": 63, "type": "SetNode", "pos": [-1390.2823486328125, -87.43519592285156], "size": [210, 60], "flags": {"collapsed": true}, "order": 26, "mode": 0, "inputs": [{"name": "CLIP_VISION", "type": "CLIP_VISION", "link": 148}], "outputs": [{"name": "*", "type": "*", "links": null}], "title": "Set_ClipVisionModle", "properties": {"previousName": "ClipVisionModle"}, "widgets_values": ["ClipVisionModle"], "color": "#233", "bgcolor": "#355", "shape": 1}, {"id": 66, "type": "SetNode", "pos": [-1320.2825927734375, 52.56480026245117], "size": [210, 60], "flags": {"collapsed": true}, "order": 27, "mode": 0, "inputs": [{"name": "VAE", "type": "VAE", "link": 153}], "outputs": [{"name": "*", "type": "*", "links": null}], "title": "Set_VAE", "properties": {"previousName": "VAE"}, "widgets_values": ["VAE"], "color": "#322", "bgcolor": "#533", "shape": 1}, {"id": 67, "type": "GetNode", "pos": [1529.717529296875, -897.435302734375], "size": [210, 60], "flags": {"collapsed": true}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [154]}], "title": "Get_VAE", "properties": {}, "widgets_values": ["VAE"], "color": "#322", "bgcolor": "#533", "shape": 1}, {"id": 44, "type": "GetImageSizeAndCount", "pos": [1529.717529296875, -647.4352416992188], "size": [310, 90], "flags": {}, "order": 55, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 96}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [97]}, {"label": "432 width", "localized_name": "width", "name": "width", "type": "INT", "links": null}, {"label": "560 height", "localized_name": "height", "name": "height", "type": "INT", "links": null}, {"label": "433 count", "localized_name": "count", "name": "count", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "8ecf5cd05e0a1012087b0da90eea9a13674668db", "Node name for S&R": "GetImageSizeAndCount"}, "widgets_values": [], "shape": 1}, {"id": 55, "type": "<PERSON>downNote", "pos": [-2180.282958984375, -537.4351806640625], "size": [459.8609619140625, 285.9714660644531], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Model links:\n\n[https://huggingface.co/Kijai/HunyuanVideo_comfy/blob/main/FramePackI2V_HY_fp8_e4m3fn.safetensors](https://huggingface.co/Kijai/HunyuanVideo_comfy/blob/main/FramePackI2V_HY_fp8_e4m3fn.safetensors)\n\n[https://huggingface.co/Kijai/HunyuanVideo_comfy/blob/main/FramePackI2V_HY_bf16.safetensors](https://huggingface.co/Kijai/HunyuanVideo_comfy/blob/main/FramePackI2V_HY_bf16.safetensors)\n\nsigclip:\n\n[https://huggingface.co/Comfy-Org/sigclip_vision_384/tree/main](https://huggingface.co/Comfy-Org/sigclip_vision_384/tree/main)\n\ntext encoder and VAE:\n\n[https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged/tree/main/split_files](https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged/tree/main/split_files)"], "color": "#432", "bgcolor": "#653", "shape": 1}, {"id": 104, "type": "Label (rgthree)", "pos": [-350, -1150], "size": [336.8408203125, 30], "flags": {"allow_interaction": true}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "title": "Choose start - end image", "properties": {"fontSize": 30, "fontFamily": "<PERSON><PERSON>", "fontColor": "#ffffff", "textAlign": "left", "backgroundColor": "transparent", "padding": 0, "borderRadius": 0}, "color": "#fff0", "bgcolor": "#fff0", "shape": 1}, {"id": 105, "type": "Label (rgthree)", "pos": [-1510, -1300], "size": [2103.955078125, 100], "flags": {"allow_interaction": true}, "order": 8, "mode": 0, "inputs": [], "outputs": [], "title": "FRAMEPACK V1 WWW.SNEAKYROBOT.ORG", "properties": {"fontSize": 100, "fontFamily": "<PERSON><PERSON>", "fontColor": "#ffffff", "textAlign": "left", "backgroundColor": "transparent", "padding": 0, "borderRadius": 0}, "color": "#fff0", "bgcolor": "#fff0"}, {"id": 20, "type": "VAEEncode", "pos": [-340.2825622558594, -757.4353637695312], "size": [280, 46], "flags": {}, "order": 41, "mode": 0, "inputs": [{"localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 156}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 155}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "links": [180]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "VAEEncode"}, "widgets_values": [], "color": "#322", "bgcolor": "#533", "shape": 1}, {"id": 95, "type": "Reroute", "pos": [929.71728515625, 72.56481170654297], "size": [75, 26], "flags": {}, "order": 50, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 182}], "outputs": [{"name": "", "type": "LATENT", "links": [185]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 93, "type": "Reroute", "pos": [929.71728515625, 152.5647735595703], "size": [75, 26], "flags": {}, "order": 49, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 178}], "outputs": [{"name": "", "type": "CLIP_VISION_OUTPUT", "links": [183]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 15, "type": "ConditioningZeroOut", "pos": [849.71728515625, -877.435302734375], "size": [390, 26], "flags": {"collapsed": true}, "order": 44, "mode": 0, "inputs": [{"localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 118}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [108]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "ConditioningZeroOut"}, "widgets_values": [], "color": "#332922", "bgcolor": "#593930", "shape": 1}, {"id": 84, "type": "Label (rgthree)", "pos": [629.71728515625, -837.435302734375], "size": [353.349609375, 30], "flags": {"allow_interaction": true}, "order": 9, "mode": 0, "inputs": [], "outputs": [], "title": "FINAL PROMPT OUTPUT", "properties": {"fontSize": 30, "fontFamily": "<PERSON><PERSON>", "fontColor": "#ffffff", "textAlign": "left", "backgroundColor": "transparent", "padding": 0, "borderRadius": 0}, "color": "#fff0", "bgcolor": "#fff0", "shape": 1}, {"id": 99, "type": "Label (rgthree)", "pos": [629.71728515625, -317.43524169921875], "size": [260.0244140625, 30], "flags": {"allow_interaction": true}, "order": 10, "mode": 0, "inputs": [], "outputs": [], "title": "SYSTEM PROMPT", "properties": {"fontSize": 30, "fontFamily": "<PERSON><PERSON>", "fontColor": "#ffffff", "textAlign": "left", "backgroundColor": "transparent", "padding": 0, "borderRadius": 0}, "color": "#fff0", "bgcolor": "#fff0", "shape": 1}, {"id": 27, "type": "FramePackTorchCompileSettings", "pos": [-2220.282958984375, -887.435302734375], "size": [531.5999755859375, 202], "flags": {}, "order": 11, "mode": 4, "inputs": [{"localized_name": "backend", "name": "backend", "type": "COMBO", "widget": {"name": "backend"}, "link": null}, {"localized_name": "fullgraph", "name": "fullgraph", "type": "BOOLEAN", "widget": {"name": "fullgraph"}, "link": null}, {"localized_name": "mode", "name": "mode", "type": "COMBO", "widget": {"name": "mode"}, "link": null}, {"localized_name": "dynamic", "name": "dynamic", "type": "BOOLEAN", "widget": {"name": "dynamic"}, "link": null}, {"localized_name": "dynamo_cache_size_limit", "name": "dynamo_cache_size_limit", "type": "INT", "widget": {"name": "dynamo_cache_size_limit"}, "link": null}, {"localized_name": "compile_single_blocks", "name": "compile_single_blocks", "type": "BOOLEAN", "widget": {"name": "compile_single_blocks"}, "link": null}, {"localized_name": "compile_double_blocks", "name": "compile_double_blocks", "type": "BOOLEAN", "widget": {"name": "compile_double_blocks"}, "link": null}], "outputs": [{"localized_name": "torch_compile_args", "name": "torch_compile_args", "type": "FRAMEPACKCOMPILEARGS", "links": [198, 199]}], "properties": {"aux_id": "lllyasviel/FramePack", "ver": "0e5fe5d7ca13c76fb8e13708f4b92e7c7a34f20c", "Node name for S&R": "FramePackTorchCompileSettings"}, "widgets_values": ["inductor", false, "default", false, 64, true, true], "shape": 1}, {"id": 109, "type": "Label (rgthree)", "pos": [-1400, -1150], "size": [260.0830078125, 30], "flags": {"allow_interaction": true}, "order": 12, "mode": 0, "inputs": [], "outputs": [], "title": "Use Torch Complile", "properties": {"fontSize": 30, "fontFamily": "<PERSON><PERSON>", "fontColor": "#ffffff", "textAlign": "left", "backgroundColor": "transparent", "padding": 0, "borderRadius": 0}, "color": "#fff0", "bgcolor": "#fff0", "shape": 1}, {"id": 108, "type": "Fast Groups Bypasser (rgthree)", "pos": [-1804.072021484375, -1196.8736572265625], "size": [290, 110], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"name": "OPT_CONNECTION", "type": "*", "links": null}], "title": "MODEL SELECTOR", "properties": {"matchColors": "yellow", "matchTitle": "", "showNav": true, "sort": "position", "customSortAlphabet": "", "toggleRestriction": "default"}}, {"id": 54, "type": "DownloadAndLoadFramePackModel", "pos": [-1620.2825927734375, -907.435302734375], "size": [470, 130], "flags": {}, "order": 25, "mode": 4, "inputs": [{"localized_name": "compile_args", "name": "compile_args", "shape": 7, "type": "FRAMEPACKCOMPILEARGS", "link": 199}, {"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "base_precision", "name": "base_precision", "type": "COMBO", "widget": {"name": "base_precision"}, "link": null}, {"localized_name": "quantization", "name": "quantization", "type": "COMBO", "widget": {"name": "quantization"}, "link": null}, {"localized_name": "attention_mode", "name": "attention_mode", "shape": 7, "type": "COMBO", "widget": {"name": "attention_mode"}, "link": null}], "outputs": [{"localized_name": "model", "name": "model", "type": "FramePackMODEL", "links": []}], "properties": {"aux_id": "kijai/ComfyUI-FramePackWrapper", "ver": "49fe507eca8246cc9d08a8093892f40c1180e88f", "Node name for S&R": "DownloadAndLoadFramePackModel"}, "widgets_values": ["lllyasviel/FramePackI2V_HY", "bf16", "disabled", "sdpa"], "shape": 1}, {"id": 102, "type": "Fast Groups Bypasser (rgthree)", "pos": [-1309.5692138671875, -1185.9241943359375], "size": [290, 100], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [{"name": "OPT_CONNECTION", "type": "*", "links": null}], "title": "MODEL SELECTOR", "properties": {"matchColors": "RED", "matchTitle": "", "showNav": true, "sort": "position", "customSortAlphabet": "", "toggleRestriction": "default"}}, {"id": 18, "type": "CLIPVisionLoader", "pos": [-1610.2825927734375, -47.435203552246094], "size": [400, 60], "flags": {}, "order": 15, "mode": 0, "inputs": [{"localized_name": "clip名称", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}], "outputs": [{"localized_name": "CLIP视觉", "name": "CLIP_VISION", "type": "CLIP_VISION", "links": [148]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPVisionLoader"}, "widgets_values": ["sigclip_vision_patch14_384.safetensors"], "color": "#2a363b", "bgcolor": "#3f5159", "shape": 1}, {"id": 12, "type": "VAELoader", "pos": [-1610.2825927734375, 92.56481170654297], "size": [469.0488586425781, 58], "flags": {}, "order": 16, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": [153]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "VAELoader"}, "widgets_values": ["hunyuan_video_vae_bf16.safetensors"], "color": "#322", "bgcolor": "#533", "shape": 1}, {"id": 103, "type": "Label (rgthree)", "pos": [-900, -1150], "size": [193.447265625, 30], "flags": {"allow_interaction": true}, "order": 17, "mode": 0, "inputs": [], "outputs": [], "title": "<PERSON><PERSON> Model", "properties": {"fontSize": 30, "fontFamily": "<PERSON><PERSON>", "fontColor": "#ffffff", "textAlign": "left", "backgroundColor": "transparent", "padding": 0, "borderRadius": 0}, "color": "#fff0", "bgcolor": "#fff0", "shape": 1}, {"id": 33, "type": "VAEDecodeTiled", "pos": [1529.717529296875, -847.435302734375], "size": [315, 150], "flags": {}, "order": 54, "mode": 0, "inputs": [{"localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 85}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 154}, {"localized_name": "分块尺寸", "name": "tile_size", "type": "INT", "widget": {"name": "tile_size"}, "link": null}, {"localized_name": "重叠", "name": "overlap", "type": "INT", "widget": {"name": "overlap"}, "link": null}, {"localized_name": "时间尺寸", "name": "temporal_size", "type": "INT", "widget": {"name": "temporal_size"}, "link": null}, {"localized_name": "时间重叠", "name": "temporal_overlap", "type": "INT", "widget": {"name": "temporal_overlap"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [96]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "VAEDecodeTiled"}, "widgets_values": [128, 32, 32, 4], "color": "#322", "bgcolor": "#533", "shape": 1}, {"id": 113, "type": "Label (rgthree)", "pos": [-1064.0177001953125, -1153.066650390625], "size": [536.2060546875, 30], "flags": {"allow_interaction": true}, "order": 18, "mode": 0, "inputs": [], "outputs": [], "title": "PORT MODEL YOU ARE USING HERE", "properties": {"fontSize": 30, "fontFamily": "<PERSON><PERSON>", "fontColor": "#ffffff", "textAlign": "left", "backgroundColor": "transparent", "padding": 0, "borderRadius": 0}, "color": "#fff0", "bgcolor": "#fff0", "shape": 1}, {"id": 117, "type": "Reroute", "pos": [-1064.8995361328125, -1083.904052734375], "size": [75, 26], "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 214}], "outputs": [{"name": "", "type": "CLIP", "links": [215]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 115, "type": "Reroute", "pos": [-1066.046875, -1122.2781982421875], "size": [157.60000610351562, 26], "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 212}], "outputs": [{"name": "FramePackMODEL", "type": "FramePackMODEL", "links": [213]}], "properties": {"showOutputText": true, "horizontal": false}}, {"id": 118, "type": "Reroute", "pos": [502.5613708496094, -1089.4036865234375], "size": [75, 26], "flags": {}, "order": 32, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 215}], "outputs": [{"name": "", "type": "CLIP", "links": [216]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 116, "type": "Reroute", "pos": [954.6407470703125, -1120.0115966796875], "size": [75, 26], "flags": {}, "order": 36, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 213}], "outputs": [{"name": "", "type": "FramePackMODEL", "links": [217]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 23, "type": "VHS_VideoCombine", "pos": [1859.717529296875, -847.435302734375], "size": [908.428955078125, 1499.6671142578125], "flags": {}, "order": 56, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": 97}, {"localized_name": "audio", "name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"localized_name": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"localized_name": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}, {"localized_name": "frame_rate", "name": "frame_rate", "type": "FLOAT", "widget": {"name": "frame_rate"}, "link": null}, {"localized_name": "loop_count", "name": "loop_count", "type": "INT", "widget": {"name": "loop_count"}, "link": null}, {"localized_name": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}, {"localized_name": "format", "name": "format", "type": "COMBO", "widget": {"name": "format"}, "link": null}, {"localized_name": "pingpong", "name": "pingpong", "type": "BOOLEAN", "widget": {"name": "pingpong"}, "link": null}, {"localized_name": "save_output", "name": "save_output", "type": "BOOLEAN", "widget": {"name": "save_output"}, "link": null}], "outputs": [{"localized_name": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 30, "loop_count": 0, "filename_prefix": "FramePack", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": true, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "FramePack_00001.mp4", "subfolder": "", "type": "output", "format": "video/h264-mp4", "frame_rate": 30, "workflow": "FramePack_00001.png", "fullpath": "H:\\ComfyUI_WAN2.1\\ComfyUI\\output\\FramePack_00001.mp4"}}}, "shape": 1}, {"id": 101, "type": "Fast Groups Bypasser (rgthree)", "pos": [-754.0718383789062, -1196.8736572265625], "size": [290, 100], "flags": {}, "order": 19, "mode": 0, "inputs": [], "outputs": [{"name": "OPT_CONNECTION", "type": "*", "links": null}], "title": "IMAGE INPUT", "properties": {"matchColors": "GREEN", "matchTitle": "", "showNav": true, "sort": "position", "customSortAlphabet": "", "toggleRestriction": "default"}}, {"id": 39, "type": "FramePackSampler", "pos": [1089.71728515625, -907.435302734375], "size": [365.07305908203125, 814.6473388671875], "flags": {}, "order": 53, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "FramePackMODEL", "link": 217}, {"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 114}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 108}, {"localized_name": "start_latent", "name": "start_latent", "type": "LATENT", "link": 185}, {"localized_name": "image_embeds", "name": "image_embeds", "shape": 7, "type": "CLIP_VISION_OUTPUT", "link": 183}, {"localized_name": "end_latent", "name": "end_latent", "shape": 7, "type": "LATENT", "link": 184}, {"localized_name": "end_image_embeds", "name": "end_image_embeds", "shape": 7, "type": "CLIP_VISION_OUTPUT", "link": 173}, {"localized_name": "initial_samples", "name": "initial_samples", "shape": 7, "type": "LATENT", "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "use_teacache", "name": "use_teacache", "type": "BOOLEAN", "widget": {"name": "use_teacache"}, "link": null}, {"localized_name": "teacache_rel_l1_thresh", "name": "teacache_rel_l1_thresh", "type": "FLOAT", "widget": {"name": "teacache_rel_l1_thresh"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "guidance_scale", "name": "guidance_scale", "type": "FLOAT", "widget": {"name": "guidance_scale"}, "link": null}, {"localized_name": "shift", "name": "shift", "type": "FLOAT", "widget": {"name": "shift"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "latent_window_size", "name": "latent_window_size", "type": "INT", "widget": {"name": "latent_window_size"}, "link": null}, {"localized_name": "total_second_length", "name": "total_second_length", "type": "FLOAT", "widget": {"name": "total_second_length"}, "link": null}, {"localized_name": "gpu_memory_preservation", "name": "gpu_memory_preservation", "type": "FLOAT", "widget": {"name": "gpu_memory_preservation"}, "link": null}, {"localized_name": "sampler", "name": "sampler", "type": "COMBO", "widget": {"name": "sampler"}, "link": null}, {"localized_name": "embed_interpolation", "name": "embed_interpolation", "shape": 7, "type": "COMBO", "widget": {"name": "embed_interpolation"}, "link": null}, {"localized_name": "start_embed_strength", "name": "start_embed_strength", "shape": 7, "type": "FLOAT", "widget": {"name": "start_embed_strength"}, "link": null}, {"localized_name": "denoise_strength", "name": "denoise_strength", "shape": 7, "type": "FLOAT", "widget": {"name": "denoise_strength"}, "link": null}], "outputs": [{"localized_name": "samples", "name": "samples", "type": "LATENT", "links": [85]}], "properties": {"aux_id": "kijai/ComfyUI-FramePackWrapper", "ver": "8e5ec6b7f3acf88255c5d93d062079f18b43aa2b", "Node name for S&R": "FramePackSampler"}, "widgets_values": [30, true, 0.15, 1, 10, 0, 47, "fixed", 9, 15, 6, "unipc_bh1", "weighted_average", 0.5, 1], "shape": 1}, {"id": 13, "type": "DualCLIPLoader", "pos": [-1610.2825927734375, -267.4352111816406], "size": [456.9478759765625, 130], "flags": {}, "order": 20, "mode": 0, "inputs": [{"localized_name": "CLIP名称1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "CLIP名称2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": [214]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["clip_l.safetensors", "llava_llama3_fp16.safetensors", "hunyuan_video", "default"], "color": "#432", "bgcolor": "#653", "shape": 1}, {"id": 52, "type": "LoadFramePackModel", "pos": [-1621.76904296875, -605.2723999023438], "size": [480.7601013183594, 174], "flags": {}, "order": 24, "mode": 0, "inputs": [{"localized_name": "compile_args", "name": "compile_args", "shape": 7, "type": "FRAMEPACKCOMPILEARGS", "link": 198}, {"localized_name": "lora", "name": "lora", "shape": 7, "type": "FPLORA", "link": null}, {"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "base_precision", "name": "base_precision", "type": "COMBO", "widget": {"name": "base_precision"}, "link": null}, {"localized_name": "quantization", "name": "quantization", "type": "COMBO", "widget": {"name": "quantization"}, "link": null}, {"localized_name": "load_device", "name": "load_device", "type": "COMBO", "widget": {"name": "load_device"}, "link": null}, {"localized_name": "attention_mode", "name": "attention_mode", "shape": 7, "type": "COMBO", "widget": {"name": "attention_mode"}, "link": null}], "outputs": [{"localized_name": "model", "name": "model", "type": "FramePackMODEL", "links": [212]}], "properties": {"aux_id": "kijai/ComfyUI-FramePackWrapper", "ver": "49fe507eca8246cc9d08a8093892f40c1180e88f", "Node name for S&R": "LoadFramePackModel"}, "widgets_values": ["FramePackI2V_HY_fp8_e4m3fn.safetensors", "bf16", "fp8_e4m3fn", "offload_device", "sdpa"], "shape": 1}, {"id": 119, "type": "CR Text", "pos": [619.9072875976562, -222.68148803710938], "size": [400, 200], "flags": {}, "order": 21, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "text", "name": "text", "type": "*", "links": [218]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text"}, "widgets_values": ["You are an assistant that writes short, motion-focused prompts for animating images.\n\nWhen the user sends an image, respond with a single, concise prompt describing visual motion (such as human activity, moving objects, or camera movements). Focus only on how the scene could come alive and become dynamic using brief phrases.\n\nLarger and more dynamic motions (like dancing, jumping, running, etc.) are preferred over smaller or more subtle ones (like standing still, sitting, etc.).\n\nDescribe subject, then motion, then other things. For example: \"The girl dances gracefully, with clear movements, full of charm.\"\n\nIf there is something that can dance (like a man, girl, robot, etc.), then prefer to describe it as dancing.\n\nStay in a loop: one image in, one motion prompt out. Do not explain, ask questions, or generate multiple options.\n\nI will give you the motion or action that i want and you will use that motion in the final output you give m. ", [false, true]]}, {"id": 47, "type": "CLIPTextEncode", "pos": [685.849365234375, -879.690185546875], "size": [400, 200], "flags": {"collapsed": true}, "order": 39, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 216}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 211}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [114, 118]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["little robot walking towards the viewport", [false, true]], "color": "#232", "bgcolor": "#353", "shape": 1}, {"id": 19, "type": "LoadImage", "pos": [-1050.28271484375, -917.435302734375], "size": [315, 314], "flags": {}, "order": 22, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [122, 126, 219]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "title": "Load Image: Start", "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "LoadImage"}, "widgets_values": ["bkrIuBic_1746746547335.webp", "image"], "shape": 1}, {"id": 58, "type": "LoadImage", "pos": [-1060.28271484375, -277.43524169921875], "size": [315, 314], "flags": {}, "order": 23, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [138]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "title": "Load Image: End", "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "LoadImage"}, "widgets_values": ["orfazwOL_1746746555196.webp", "image"], "shape": 1}, {"id": 120, "type": "OllamaVision", "pos": [171.3689422607422, -772.1237182617188], "size": [400, 256], "flags": {}, "order": 30, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": 219}, {"localized_name": "query", "name": "query", "type": "STRING", "widget": {"name": "query"}, "link": 218}, {"localized_name": "debug", "name": "debug", "type": "COMBO", "widget": {"name": "debug"}, "link": null}, {"localized_name": "url", "name": "url", "type": "STRING", "widget": {"name": "url"}, "link": null}, {"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "keep_alive", "name": "keep_alive", "type": "INT", "widget": {"name": "keep_alive"}, "link": null}, {"localized_name": "format", "name": "format", "type": "COMBO", "widget": {"name": "format"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}], "outputs": [{"localized_name": "description", "name": "description", "type": "STRING", "links": [220]}], "properties": {"cnr_id": "comfyui-o<PERSON><PERSON>", "ver": "2.0.3", "Node name for S&R": "OllamaVision"}, "widgets_values": ["describe the image", "enable", "http://127.0.0.1:11434", "qwen2.5:32b", 5, "text", 1168825540, "randomize", [false, true]]}, {"id": 82, "type": "easy showAnything", "pos": [601.947998046875, -805.************], "size": [416.4720764160156, 495.4019470214844], "flags": {}, "order": 35, "mode": 0, "inputs": [{"localized_name": "输入任何", "name": "anything", "shape": 7, "type": "*", "link": 220}], "outputs": [{"localized_name": "输出", "name": "output", "type": "*", "links": [211]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.2.9", "Node name for S&R": "easy showAnything"}, "widgets_values": ["<think>\nOkay, I need to create prompts for animating images based on the user's guidelines. The user provided a specific response format where they describe an image with some motion.\n\nFirst, the example given is about a girl dancing, so it should include the subject, then the motion (dancing), followed by other descriptive elements like \"gracefully\" and \"charm.\"\n\nThe user wants me to focus on larger motions like running, jumping, or dancing. I need to avoid smaller movements unless necessary.\n\nI'll start by identifying the main subject in the image. Then add a strong verb that conveys movement, such as \"runs,\" \"flies,\" \"dances,\" etc. Finally, include some adjectives to enhance the description without making it too verbose.\n\nLet me think of an example: A robot running through a forest with trees and sun rays. The prompt would be something like \"The robot runs swiftly through the dense forest, with towering trees blocking its path and golden sun rays casting long shadows.\"\n\nWait, does that fit? It includes the subject (robot), the motion (runs), and some visual elements.\n\nAnother example: A kite flying in a blue sky. Motion could be \"soars upward.\" So the prompt is \"The kite soars upward through the vast blue sky with vibrant colors all around.\"\n\nI should make sure each prompt starts with the main subject, adds an active verb for motion, and includes additional descriptive phrases to set the scene.\n\nLet me try another one: A bird flying over mountains. Motion could be \"soars gracefully.\" Prompt: \"The bird soars gracefully over the snow-capped mountains, with white clouds framing the view.\"\n\nThis follows the structure: subject, motion, description.\n</think>\n\nThe robot runs swiftly through the dense forest, with towering trees blocking its path and golden sun rays casting long shadows."], "shape": 1}], "links": [[85, 39, 0, 33, 0, "LATENT"], [96, 33, 0, 44, 0, "IMAGE"], [97, 44, 0, 23, 0, "IMAGE"], [108, 15, 0, 39, 2, "CONDITIONING"], [114, 47, 0, 39, 1, "CONDITIONING"], [116, 48, 0, 17, 1, "IMAGE"], [118, 47, 0, 15, 0, "CONDITIONING"], [122, 19, 0, 50, 0, "IMAGE"], [125, 50, 0, 48, 0, "IMAGE"], [126, 19, 0, 51, 0, "IMAGE"], [127, 51, 1, 50, 2, "INT"], [128, 51, 0, 50, 1, "INT"], [136, 51, 0, 59, 1, "INT"], [137, 51, 1, 59, 2, "INT"], [138, 58, 0, 59, 0, "IMAGE"], [139, 59, 0, 60, 0, "IMAGE"], [148, 18, 0, 63, 0, "*"], [149, 64, 0, 17, 0, "CLIP_VISION"], [150, 65, 0, 57, 0, "CLIP_VISION"], [151, 60, 0, 57, 1, "IMAGE"], [152, 60, 0, 62, 0, "IMAGE"], [153, 12, 0, 66, 0, "*"], [154, 67, 0, 33, 1, "VAE"], [155, 68, 0, 20, 1, "VAE"], [156, 48, 0, 20, 0, "IMAGE"], [158, 69, 0, 62, 1, "VAE"], [173, 87, 0, 39, 6, "CLIP_VISION_OUTPUT"], [176, 17, 0, 89, 0, "*"], [178, 89, 0, 93, 0, "*"], [179, 62, 0, 91, 0, "*"], [180, 20, 0, 92, 0, "*"], [181, 91, 0, 94, 0, "*"], [182, 92, 0, 95, 0, "*"], [183, 93, 0, 39, 4, "CLIP_VISION_OUTPUT"], [184, 94, 0, 39, 5, "LATENT"], [185, 95, 0, 39, 3, "LATENT"], [191, 100, 0, 87, 0, "*"], [193, 57, 0, 100, 0, "*"], [198, 27, 0, 52, 0, "FRAMEPACKCOMPILEARGS"], [199, 27, 0, 54, 0, "FRAMEPACKCOMPILEARGS"], [211, 82, 0, 47, 1, "STRING"], [212, 52, 0, 115, 0, "*"], [213, 115, 0, 116, 0, "*"], [214, 13, 0, 117, 0, "*"], [215, 117, 0, 118, 0, "*"], [216, 118, 0, 47, 0, "CLIP"], [217, 116, 0, 39, 0, "FramePackMODEL"], [218, 119, 0, 120, 1, "STRING"], [219, 19, 0, 120, 0, "IMAGE"], [220, 120, 0, 82, 0, "*"]], "groups": [{"id": 11, "title": "FRAMEPACK V1 WORKFLOW", "bounding": [-2250.282958984375, -1167.4351806640625, 5080, 1460], "color": "#444", "font_size": 80, "flags": {}}, {"id": 1, "title": "End Image", "bounding": [-1090.2825927734375, -417.43524169921875, 1180, 630], "color": "#8A8", "font_size": 60, "flags": {}}, {"id": 2, "title": "Start Image", "bounding": [-1090.2825927734375, -1037.435302734375, 1180, 590], "color": "#8A8", "font_size": 60, "flags": {}}, {"id": 3, "title": "Models", "bounding": [-1650.2825927734375, -397.4352722167969, 540, 610], "color": "#3f789e", "font_size": 60, "flags": {}}, {"id": 4, "title": "CONDITIONING", "bounding": [599.7173461914062, -1037.435302734375, 450, 1260], "color": "#3f789e", "font_size": 60, "flags": {}}, {"id": 5, "title": "FramePack Official", "bounding": [-1650.2825927734375, -1037.435302734375, 540, 290], "color": "#A88", "font_size": 60, "flags": {}}, {"id": 6, "title": "FramePack FP8", "bounding": [-1650.2825927734375, -717.435302734375, 540, 300], "color": "#A88", "font_size": 60, "flags": {}}, {"id": 7, "title": "TORCH COMPILE", "bounding": [-2230.282958984375, -1037.435302734375, 560, 400], "color": "#b58b2a", "font_size": 60, "flags": {}}, {"id": 8, "title": "LLM", "bounding": [109.71762084960938, -1037.435302734375, 470, 1260], "color": "#88A", "font_size": 60, "flags": {}}, {"id": 9, "title": "SAMPLING", "bounding": [1069.71728515625, -1037.435302734375, 410, 1260], "color": "#b06634", "font_size": 60, "flags": {}}, {"id": 10, "title": "DECODE AND SAVE", "bounding": [1499.717529296875, -1027.435302734375, 1290, 1250], "color": "#a1309b", "font_size": 60, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.6115909044841659, "offset": [3569.273983164498, 1590.7184493227705]}, "frontendVersion": "1.18.9", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true, "ue_links": []}, "version": 0.4}