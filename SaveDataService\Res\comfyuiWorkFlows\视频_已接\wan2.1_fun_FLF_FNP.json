{"id": "e7533930-2792-43a9-b4b5-ded4617d8a43", "revision": 0, "last_node_id": 110, "last_link_id": 198, "nodes": [{"id": 39, "type": "VAELoader", "pos": [30, 330], "size": [350, 60], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [76, 114]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAELoader", "models": [{"name": "wan_2.1_vae.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors?download=true", "directory": "vae"}]}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 49, "type": "CLIPVisionLoader", "pos": [30, 500], "size": [340, 60], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "clip名称", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}], "outputs": [{"localized_name": "CLIP视觉", "name": "CLIP_VISION", "type": "CLIP_VISION", "slot_index": 0, "links": [94]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPVisionLoader", "models": [{"name": "clip_vision_h.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/clip_vision/clip_vision_h.safetensors?download=true", "directory": "clip_vision"}]}, "widgets_values": ["clip_vision_h.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 67, "type": "ModelSamplingSD3", "pos": [1200, 80], "size": [240, 60], "flags": {"collapsed": false}, "order": 35, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 132}, {"localized_name": "移位", "name": "shift", "type": "FLOAT", "widget": {"name": "shift"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [133]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "ModelSamplingSD3", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": [5.000000000000001]}, {"id": 68, "type": "UNetTemporalAttentionMultiply", "pos": [1200, 190], "size": [243.60000610351562, 150], "flags": {"collapsed": false}, "order": 40, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 133}, {"localized_name": "自我结构", "name": "self_structural", "type": "FLOAT", "widget": {"name": "self_structural"}, "link": null}, {"localized_name": "自我时间", "name": "self_temporal", "type": "FLOAT", "widget": {"name": "self_temporal"}, "link": null}, {"localized_name": "交叉结构", "name": "cross_structural", "type": "FLOAT", "widget": {"name": "cross_structural"}, "link": null}, {"localized_name": "交叉时间", "name": "cross_temporal", "type": "FLOAT", "widget": {"name": "cross_temporal"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [131]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "UNetTemporalAttentionMultiply", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": [1, 1, 1.2, 1.3]}, {"id": 66, "type": "CFGZeroStar", "pos": [890, 310], "size": [230, 30], "flags": {"collapsed": false}, "order": 44, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 131}], "outputs": [{"localized_name": "patched_model", "name": "patched_model", "type": "MODEL", "links": [135]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CFGZeroStar", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": []}, {"id": 65, "type": "SkipLayerGuidanceDiT", "pos": [890, 80], "size": [230, 180], "flags": {"collapsed": false}, "order": 29, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 146}, {"localized_name": "双层", "name": "double_layers", "type": "STRING", "widget": {"name": "double_layers"}, "link": null}, {"localized_name": "单层", "name": "single_layers", "type": "STRING", "widget": {"name": "single_layers"}, "link": null}, {"localized_name": "缩放", "name": "scale", "type": "FLOAT", "widget": {"name": "scale"}, "link": null}, {"localized_name": "开始百分比", "name": "start_percent", "type": "FLOAT", "widget": {"name": "start_percent"}, "link": null}, {"localized_name": "结束百分比", "name": "end_percent", "type": "FLOAT", "widget": {"name": "end_percent"}, "link": null}, {"localized_name": "重新缩放比例", "name": "rescaling_scale", "type": "FLOAT", "widget": {"name": "rescaling_scale"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [132]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "SkipLayerGuidanceDiT", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["9,10", "9,10", 3, 0.01, 0.8000000000000002, 0]}, {"id": 7, "type": "CLIPTextEncode", "pos": [420, 260], "size": [420, 130], "flags": {}, "order": 28, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 75}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [113]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走，不合理的动作"], "color": "#323", "bgcolor": "#535"}, {"id": 8, "type": "VAEDecode", "pos": [910, 800], "size": [210, 266], "flags": {"collapsed": true}, "order": 49, "mode": 0, "inputs": [{"localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 35}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 76}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [56]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [1170, 500], "size": [280, 262], "flags": {}, "order": 47, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 135}, {"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 115}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 116}, {"localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 117}, {"localized_name": "种子", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [35]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [471941414299535, "randomize", 20, 6, "uni_pc", "simple", 1]}, {"id": 76, "type": "VAELoader", "pos": [1553.4520263671875, 329.4833679199219], "size": [350, 60], "flags": {}, "order": 2, "mode": 2, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [154, 157]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "VAELoader", "models": [{"name": "wan_2.1_vae.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors?download=true", "hash": "2fc39d31359a4b0a64f55876d8ff7fa8d780956ae2cb13463b0223e15148976b", "hash_type": "SHA256", "directory": "vae"}]}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 77, "type": "CLIPVisionEncode", "pos": [2083.453125, 569.4834594726562], "size": [253.60000610351562, 78], "flags": {"collapsed": true}, "order": 18, "mode": 2, "inputs": [{"localized_name": "clip视觉", "name": "clip_vision", "type": "CLIP_VISION", "link": 147}, {"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 148}, {"localized_name": "裁剪", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"localized_name": "CLIP视觉输出", "name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "slot_index": 0, "links": [159]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["none"]}, {"id": 78, "type": "SaveAnimatedWEBP", "pos": [2323.453125, 489.4833679199219], "size": [530, 480], "flags": {}, "order": 45, "mode": 2, "inputs": [{"localized_name": "图片", "name": "images", "type": "IMAGE", "link": 149}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}, {"localized_name": "帧率", "name": "fps", "type": "FLOAT", "widget": {"name": "fps"}, "link": null}, {"localized_name": "无损", "name": "lossless", "type": "BOOLEAN", "widget": {"name": "lossless"}, "link": null}, {"localized_name": "质量", "name": "quality", "type": "INT", "widget": {"name": "quality"}, "link": null}, {"localized_name": "方法", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "SaveAnimatedWEBP"}, "widgets_values": ["ComfyUI", 16, false, 90, "default"]}, {"id": 79, "type": "CLIPVisionLoader", "pos": [1553.4520263671875, 509.4833984375], "size": [340, 60], "flags": {}, "order": 3, "mode": 2, "inputs": [{"localized_name": "clip名称", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}], "outputs": [{"localized_name": "CLIP视觉", "name": "CLIP_VISION", "type": "CLIP_VISION", "slot_index": 0, "links": [147, 150]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "CLIPVisionLoader", "models": [{"name": "clip_vision_h.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/clip_vision/clip_vision_h.safetensors?download=true", "hash": "64a7ef761bfccbadbaa3da77366aac4185a6c58fa5de5f589b42a65bcc21f161", "hash_type": "SHA256", "directory": "clip_vision"}]}, "widgets_values": ["clip_vision_h.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 80, "type": "CLIPVisionEncode", "pos": [2053.453125, 499.48333740234375], "size": [253.60000610351562, 78], "flags": {"collapsed": true}, "order": 19, "mode": 2, "inputs": [{"localized_name": "clip视觉", "name": "clip_vision", "type": "CLIP_VISION", "link": 150}, {"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 151}, {"localized_name": "裁剪", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"localized_name": "CLIP视觉输出", "name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "slot_index": 0, "links": [158]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["none"]}, {"id": 81, "type": "ModelSamplingSD3", "pos": [2333.453125, 69.4833984375], "size": [270, 60], "flags": {}, "order": 20, "mode": 2, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 152}, {"localized_name": "移位", "name": "shift", "type": "FLOAT", "widget": {"name": "shift"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [162]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "ModelSamplingSD3"}, "widgets_values": [8]}, {"id": 82, "type": "VAEDecode", "pos": [2643.453125, 389.4833679199219], "size": [210, 266], "flags": {"collapsed": true}, "order": 42, "mode": 2, "inputs": [{"localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 153}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 154}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [149]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 83, "type": "WanFirstLastFrameToVideo", "pos": [2333.453125, 169.48341369628906], "size": [270.3999938964844, 250], "flags": {}, "order": 32, "mode": 2, "inputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 155}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 156}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 157}, {"localized_name": "clip_vision_start_image", "name": "clip_vision_start_image", "shape": 7, "type": "CLIP_VISION_OUTPUT", "link": 158}, {"localized_name": "clip_vision_end_image", "name": "clip_vision_end_image", "shape": 7, "type": "CLIP_VISION_OUTPUT", "link": 159}, {"localized_name": "start_image", "name": "start_image", "shape": 7, "type": "IMAGE", "link": 160}, {"localized_name": "end_image", "name": "end_image", "shape": 7, "type": "IMAGE", "link": 161}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "length", "name": "length", "type": "INT", "widget": {"name": "length"}, "link": null}, {"localized_name": "batch_size", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}, "link": null}], "outputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "links": [163]}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "links": [164]}, {"localized_name": "latent", "name": "latent", "type": "LATENT", "links": [165]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "WanFirstLastFrameToVideo"}, "widgets_values": [720, 1280, 33, 1]}, {"id": 84, "type": "K<PERSON><PERSON><PERSON>", "pos": [2643.453125, 79.4833984375], "size": [315, 262], "flags": {}, "order": 38, "mode": 2, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 162}, {"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 163}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 164}, {"localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 165}, {"localized_name": "种子", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [153]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [531633372644222, "randomize", 20, 3, "uni_pc", "simple", 1]}, {"id": 85, "type": "CLIPTextEncode", "pos": [1943.4525146484375, 259.4833984375], "size": [340, 140], "flags": {}, "order": 21, "mode": 2, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 166}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [156]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走,过曝，"], "color": "#323", "bgcolor": "#535"}, {"id": 86, "type": "LoadImage", "pos": [1933.4525146484375, 629.4834594726562], "size": [320, 320], "flags": {}, "order": 4, "mode": 2, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [148, 161]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "title": "End_image", "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "LoadImage"}, "widgets_values": ["end_image.png", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 87, "type": "LoadImage", "pos": [1553.4520263671875, 623.4834594726562], "size": [340, 326], "flags": {}, "order": 5, "mode": 2, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [151, 160]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "slot_index": 1, "links": null}], "title": "Start_image", "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "LoadImage"}, "widgets_values": ["start_image.png", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 88, "type": "UNETLoader", "pos": [1553.4520263671875, 69.4833984375], "size": [346.7470703125, 82], "flags": {}, "order": 6, "mode": 2, "inputs": [{"localized_name": "UNet名称", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "数据类型", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [152]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "UNETLoader", "models": [{"name": "wan2.1_flf2v_720p_14B_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_flf2v_720p_14B_fp16.safetensors?download=true", "hash": "bf4ac25667d00f53f49df02c5771f5aa7801c1dcb9b3ccade1407687c426d030", "hash_type": "SHA256", "directory": "diffusion_models"}]}, "widgets_values": ["wan2.1_flf2v_720p_14B_fp16.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 89, "type": "CLIPLoader", "pos": [1553.4520263671875, 189.48341369628906], "size": [350, 106], "flags": {}, "order": 7, "mode": 2, "inputs": [{"localized_name": "CLIP名称", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [166, 167]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "CLIPLoader", "models": [{"name": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp8_e4m3fn_scaled.safetensors?download=true", "hash": "c3355d30191f1f066b26d93fba017ae9809dce6c627dda5f6a66eaa651204f68", "hash_type": "SHA256", "directory": "text_encoders"}]}, "widgets_values": ["umt5_xxl_fp8_e4m3fn_scaled.safetensors", "wan", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 90, "type": "CLIPTextEncode", "pos": [1943.4525146484375, 69.4833984375], "size": [330, 140], "flags": {}, "order": 22, "mode": 2, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 167}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [155]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["glass flower blossom"], "color": "#232", "bgcolor": "#353"}, {"id": 91, "type": "CLIPLoader", "pos": [3132.107666015625, 166.61770629882812], "size": [350, 106], "flags": {}, "order": 8, "mode": 2, "inputs": [{"localized_name": "CLIP名称", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [172, 174]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPLoader", "models": [{"name": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp8_e4m3fn_scaled.safetensors?download=true", "directory": "text_encoders"}]}, "widgets_values": ["umt5_xxl_fp8_e4m3fn_scaled.safetensors", "wan", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 92, "type": "CLIPVisionLoader", "pos": [3132.107666015625, 476.61749267578125], "size": [340, 60], "flags": {}, "order": 9, "mode": 2, "inputs": [{"localized_name": "clip名称", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}], "outputs": [{"localized_name": "CLIP视觉", "name": "CLIP_VISION", "type": "CLIP_VISION", "slot_index": 0, "links": [175]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPVisionLoader", "models": [{"name": "clip_vision_h.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/clip_vision/clip_vision_h.safetensors?download=true", "directory": "clip_vision"}]}, "widgets_values": ["clip_vision_h.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 93, "type": "ModelSamplingSD3", "pos": [4302.1083984375, 56.61779022216797], "size": [240, 60], "flags": {"collapsed": false}, "order": 34, "mode": 2, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 168}, {"localized_name": "移位", "name": "shift", "type": "FLOAT", "widget": {"name": "shift"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [169]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "ModelSamplingSD3", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": [5.000000000000001]}, {"id": 94, "type": "UNetTemporalAttentionMultiply", "pos": [4302.1083984375, 166.61770629882812], "size": [243.60000610351562, 150], "flags": {"collapsed": false}, "order": 39, "mode": 2, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 169}, {"localized_name": "自我结构", "name": "self_structural", "type": "FLOAT", "widget": {"name": "self_structural"}, "link": null}, {"localized_name": "自我时间", "name": "self_temporal", "type": "FLOAT", "widget": {"name": "self_temporal"}, "link": null}, {"localized_name": "交叉结构", "name": "cross_structural", "type": "FLOAT", "widget": {"name": "cross_structural"}, "link": null}, {"localized_name": "交叉时间", "name": "cross_temporal", "type": "FLOAT", "widget": {"name": "cross_temporal"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [170]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "UNetTemporalAttentionMultiply", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": [1, 1, 1.2, 1.3]}, {"id": 95, "type": "CFGZeroStar", "pos": [3992.107666015625, 286.61749267578125], "size": [230, 30], "flags": {"collapsed": false}, "order": 43, "mode": 2, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 170}], "outputs": [{"localized_name": "patched_model", "name": "patched_model", "type": "MODEL", "links": [177]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CFGZeroStar", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": []}, {"id": 96, "type": "SkipLayerGuidanceDiT", "pos": [3992.107666015625, 56.61779022216797], "size": [230, 180], "flags": {"collapsed": false}, "order": 26, "mode": 2, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 171}, {"localized_name": "双层", "name": "double_layers", "type": "STRING", "widget": {"name": "double_layers"}, "link": null}, {"localized_name": "单层", "name": "single_layers", "type": "STRING", "widget": {"name": "single_layers"}, "link": null}, {"localized_name": "缩放", "name": "scale", "type": "FLOAT", "widget": {"name": "scale"}, "link": null}, {"localized_name": "开始百分比", "name": "start_percent", "type": "FLOAT", "widget": {"name": "start_percent"}, "link": null}, {"localized_name": "结束百分比", "name": "end_percent", "type": "FLOAT", "widget": {"name": "end_percent"}, "link": null}, {"localized_name": "重新缩放比例", "name": "rescaling_scale", "type": "FLOAT", "widget": {"name": "rescaling_scale"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [168]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "SkipLayerGuidanceDiT", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["9,10", "9,10", 3, 0.01, 0.8000000000000002, 0]}, {"id": 97, "type": "CLIPTextEncode", "pos": [3522.107666015625, 236.6176300048828], "size": [420, 130], "flags": {}, "order": 23, "mode": 2, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 172}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [182]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走"], "color": "#323", "bgcolor": "#535"}, {"id": 98, "type": "VAELoader", "pos": [3132.107666015625, 306.6175231933594], "size": [350, 60], "flags": {}, "order": 10, "mode": 2, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [183, 188]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAELoader", "models": [{"name": "wan_2.1_vae.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors?download=true", "directory": "vae"}]}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 99, "type": "SaveAnimatedWEBP", "pos": [4602.10888671875, 16.61773681640625], "size": [470, 570], "flags": {}, "order": 50, "mode": 2, "inputs": [{"localized_name": "图片", "name": "images", "type": "IMAGE", "link": 173}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}, {"localized_name": "帧率", "name": "fps", "type": "FLOAT", "widget": {"name": "fps"}, "link": null}, {"localized_name": "无损", "name": "lossless", "type": "BOOLEAN", "widget": {"name": "lossless"}, "link": null}, {"localized_name": "质量", "name": "quality", "type": "INT", "widget": {"name": "quality"}, "link": null}, {"localized_name": "方法", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveAnimatedWEBP"}, "widgets_values": ["ComfyUI", 16, false, 90, "default"]}, {"id": 100, "type": "CLIPTextEncode", "pos": [3522.107666015625, 46.6178092956543], "size": [420, 140], "flags": {}, "order": 24, "mode": 2, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 174}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [181]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Fashion photography, a inflated man wearing Balenciaga clothes, fashion magazine, Balenciaga style, studio, photographer by <PERSON>, On-Camera Flash"], "color": "#232", "bgcolor": "#353"}, {"id": 101, "type": "LoadImage", "pos": [3132.107666015625, 576.6177978515625], "size": [340, 326], "flags": {}, "order": 11, "mode": 2, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [176, 185]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "slot_index": 1, "links": null}], "title": "Start_image", "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["fashion_photography_start.png", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 102, "type": "LoadImage", "pos": [3542.107666015625, 586.6177978515625], "size": [315, 314], "flags": {}, "order": 12, "mode": 2, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [186]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "title": "End_image", "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["fashion_photography_end.png", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 103, "type": "CLIPVisionEncode", "pos": [3542.107666015625, 476.61749267578125], "size": [253.60000610351562, 78], "flags": {}, "order": 25, "mode": 2, "inputs": [{"localized_name": "clip视觉", "name": "clip_vision", "type": "CLIP_VISION", "link": 175}, {"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 176}, {"localized_name": "裁剪", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"localized_name": "CLIP视觉输出", "name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "slot_index": 0, "links": [184]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["none"]}, {"id": 104, "type": "K<PERSON><PERSON><PERSON>", "pos": [4272.1083984375, 506.61773681640625], "size": [280, 262], "flags": {}, "order": 46, "mode": 2, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 177}, {"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 178}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 179}, {"localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 180}, {"localized_name": "种子", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [187]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [547000114184516, "randomize", 20, 6, "uni_pc", "simple", 1]}, {"id": 105, "type": "WanFunInpaintToVideo", "pos": [4002.107666015625, 526.6177978515625], "size": [240, 230], "flags": {}, "order": 33, "mode": 2, "inputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 181}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 182}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 183}, {"localized_name": "clip_vision_output", "name": "clip_vision_output", "shape": 7, "type": "CLIP_VISION_OUTPUT", "link": 184}, {"localized_name": "start_image", "name": "start_image", "shape": 7, "type": "IMAGE", "link": 185}, {"localized_name": "end_image", "name": "end_image", "shape": 7, "type": "IMAGE", "link": 186}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "length", "name": "length", "type": "INT", "widget": {"name": "length"}, "link": null}, {"localized_name": "batch_size", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}, "link": null}], "outputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "links": [178]}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "links": [179]}, {"localized_name": "latent", "name": "latent", "type": "LATENT", "links": [180]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "WanFunInpaintToVideo"}, "widgets_values": [480, 768, 81, 1]}, {"id": 106, "type": "VAEDecode", "pos": [4412.10888671875, 826.617919921875], "size": [210, 266], "flags": {"collapsed": true}, "order": 48, "mode": 2, "inputs": [{"localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 187}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 188}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [173]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 107, "type": "UNETLoader", "pos": [3132.107666015625, 46.6178092956543], "size": [346.7470703125, 82], "flags": {}, "order": 13, "mode": 2, "inputs": [{"localized_name": "UNet名称", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "数据类型", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [171]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "UNETLoader", "models": [{"name": "wan2.1_fun_inp_1.3B_bf16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_fun_inp_1.3B_bf16.safetensors?download=true", "directory": "diffusion_models"}]}, "widgets_values": ["wan2.1_fun_inp_1.3B_bf16.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 6, "type": "CLIPTextEncode", "pos": [420, 70], "size": [420, 140], "flags": {}, "order": 27, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 74}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [112]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["一个可爱的小女孩，微笑，挥手"], "color": "#232", "bgcolor": "#353"}, {"id": 38, "type": "CLIPLoader", "pos": [30, 190], "size": [350, 106], "flags": {}, "order": 14, "mode": 0, "inputs": [{"localized_name": "CLIP名称", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [74, 75]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPLoader", "models": [{"name": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp8_e4m3fn_scaled.safetensors?download=true", "directory": "text_encoders"}]}, "widgets_values": ["umt5_xxl_fp8_e4m3fn_scaled.safetensors", "wan", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 37, "type": "UNETLoader", "pos": [30, 70], "size": [346.7470703125, 82], "flags": {}, "order": 15, "mode": 0, "inputs": [{"localized_name": "UNet名称", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "数据类型", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [146]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "UNETLoader", "models": [{"name": "wan2.1_fun_control_1.3B_bf16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_fun_control_1.3B_bf16.safetensors?download=true", "directory": "diffusion_models"}]}, "widgets_values": ["comfy\\wan2.1_fun_control_1.3B_bf16.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 74, "type": "PreviewImage", "pos": [365.154541015625, 1118.5347900390625], "size": [429.3006286621094, 521.5380249023438], "flags": {}, "order": 37, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 193}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 110, "type": "LayerUtility: ImageScaleByAspectRatio V2", "pos": [450.7987365722656, 608.2947998046875], "size": [303.6851501464844, 330], "flags": {"collapsed": true}, "order": 30, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 194}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": null}, {"localized_name": "aspect_ratio", "name": "aspect_ratio", "type": "COMBO", "widget": {"name": "aspect_ratio"}, "link": null}, {"localized_name": "proportional_width", "name": "proportional_width", "type": "INT", "widget": {"name": "proportional_width"}, "link": null}, {"localized_name": "proportional_height", "name": "proportional_height", "type": "INT", "widget": {"name": "proportional_height"}, "link": null}, {"localized_name": "fit", "name": "fit", "type": "COMBO", "widget": {"name": "fit"}, "link": null}, {"localized_name": "method", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}, {"localized_name": "round_to_multiple", "name": "round_to_multiple", "type": "COMBO", "widget": {"name": "round_to_multiple"}, "link": null}, {"localized_name": "scale_to_side", "name": "scale_to_side", "type": "COMBO", "widget": {"name": "scale_to_side"}, "link": null}, {"localized_name": "scale_to_length", "name": "scale_to_length", "type": "INT", "widget": {"name": "scale_to_length"}, "link": null}, {"localized_name": "background_color", "name": "background_color", "type": "STRING", "widget": {"name": "background_color"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [197]}, {"localized_name": "mask", "name": "mask", "type": "MASK", "links": null}, {"localized_name": "original_size", "name": "original_size", "type": "BOX", "links": null}, {"localized_name": "width", "name": "width", "type": "INT", "links": [195]}, {"localized_name": "height", "name": "height", "type": "INT", "links": [198]}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerUtility: ImageScaleByAspectRatio V2"}, "widgets_values": ["original", 1, 1, "letterbox", "lanc<PERSON>s", "8", "None", 768, "#000000"], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 55, "type": "WanFunControlToVideo", "pos": [880, 500], "size": [250, 230], "flags": {}, "order": 41, "mode": 0, "inputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 112}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 113}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 114}, {"localized_name": "clip_vision_output", "name": "clip_vision_output", "shape": 7, "type": "CLIP_VISION_OUTPUT", "link": 119}, {"localized_name": "start_image", "name": "start_image", "shape": 7, "type": "IMAGE", "link": 118}, {"localized_name": "control_video", "name": "control_video", "shape": 7, "type": "IMAGE", "link": 191}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 195}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 198}, {"localized_name": "length", "name": "length", "type": "INT", "widget": {"name": "length"}, "link": null}, {"localized_name": "batch_size", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}, "link": null}], "outputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "links": [115]}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "links": [116]}, {"localized_name": "latent", "name": "latent", "type": "LATENT", "links": [117]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "WanFunControlToVideo"}, "widgets_values": [960, 544, 77, 1]}, {"id": 51, "type": "CLIPVisionEncode", "pos": [464.46270751953125, 797.7799682617188], "size": [253.60000610351562, 78], "flags": {}, "order": 36, "mode": 0, "inputs": [{"localized_name": "clip视觉", "name": "clip_vision", "type": "CLIP_VISION", "link": 94}, {"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 197}, {"localized_name": "裁剪", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"localized_name": "CLIP视觉输出", "name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "slot_index": 0, "links": [119]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["none"]}, {"id": 28, "type": "SaveAnimatedWEBP", "pos": [870, 900], "size": [600, 1022.923095703125], "flags": {}, "order": 51, "mode": 0, "inputs": [{"localized_name": "图片", "name": "images", "type": "IMAGE", "link": 56}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}, {"localized_name": "帧率", "name": "fps", "type": "FLOAT", "widget": {"name": "fps"}, "link": null}, {"localized_name": "无损", "name": "lossless", "type": "BOOLEAN", "widget": {"name": "lossless"}, "link": null}, {"localized_name": "质量", "name": "quality", "type": "INT", "widget": {"name": "quality"}, "link": null}, {"localized_name": "方法", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveAnimatedWEBP"}, "widgets_values": ["ComfyUI", 16, false, 90, "default"]}, {"id": 52, "type": "LoadImage", "pos": [30, 600], "size": [340, 326], "flags": {}, "order": 16, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [118, 194]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "slot_index": 1, "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["ComfyUI_temp_eimqy_00006_.png", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 108, "type": "VHS_LoadVideo", "pos": [61.201805114746094, 1008.8106079101562], "size": [255.4267120361328, 667.622802734375], "flags": {}, "order": 17, "mode": 0, "inputs": [{"localized_name": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"localized_name": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}, {"localized_name": "video", "name": "video", "type": "COMBO", "widget": {"name": "video"}, "link": null}, {"localized_name": "force_rate", "name": "force_rate", "type": "FLOAT", "widget": {"name": "force_rate"}, "link": null}, {"localized_name": "custom_width", "name": "custom_width", "type": "INT", "widget": {"name": "custom_width"}, "link": null}, {"localized_name": "custom_height", "name": "custom_height", "type": "INT", "widget": {"name": "custom_height"}, "link": null}, {"localized_name": "frame_load_cap", "name": "frame_load_cap", "type": "INT", "widget": {"name": "frame_load_cap"}, "link": null}, {"localized_name": "skip_first_frames", "name": "skip_first_frames", "type": "INT", "widget": {"name": "skip_first_frames"}, "link": null}, {"localized_name": "select_every_nth", "name": "select_every_nth", "type": "INT", "widget": {"name": "select_every_nth"}, "link": null}, {"localized_name": "format", "name": "format", "shape": 7, "type": "COMBO", "widget": {"name": "format"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [190]}, {"localized_name": "frame_count", "name": "frame_count", "type": "INT", "links": null}, {"localized_name": "audio", "name": "audio", "type": "AUDIO", "links": null}, {"localized_name": "video_info", "name": "video_info", "type": "VHS_VIDEOINFO", "links": null}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "a7ce59e381934733bfae03b1be029756d6ce936d", "Node name for S&R": "VHS_LoadVideo"}, "widgets_values": {"video": "高品质_一个动漫风的小女孩_可爱的表情_微笑_用手撩了下头发.mp4", "force_rate": 0, "custom_width": 0, "custom_height": 0, "frame_load_cap": 0, "skip_first_frames": 0, "select_every_nth": 1, "format": "AnimateDiff", "choose video to upload": "image", "videopreview": {"hidden": false, "paused": false, "params": {"force_rate": 0, "custom_width": 0, "custom_height": 0, "frame_load_cap": 0, "skip_first_frames": 0, "select_every_nth": 1, "filename": "高品质_一个动漫风的小女孩_可爱的表情_微笑_用手撩了下头发.mp4", "type": "input", "format": "video/mp4"}}}}, {"id": 109, "type": "DepthAnythingPreprocessor", "pos": [431.8452453613281, 974.4027709960938], "size": [270, 82], "flags": {}, "order": 31, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 190}, {"localized_name": "ckpt_name", "name": "ckpt_name", "shape": 7, "type": "COMBO", "widget": {"name": "ckpt_name"}, "link": null}, {"localized_name": "resolution", "name": "resolution", "shape": 7, "type": "INT", "widget": {"name": "resolution"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [191, 193]}], "properties": {"cnr_id": "comfyui_controlnet_aux", "ver": "1.0.7", "Node name for S&R": "DepthAnythingPreprocessor"}, "widgets_values": ["depth_anything_vitl14.pth", 512]}], "links": [[35, 3, 0, 8, 0, "LATENT"], [56, 8, 0, 28, 0, "IMAGE"], [74, 38, 0, 6, 0, "CLIP"], [75, 38, 0, 7, 0, "CLIP"], [76, 39, 0, 8, 1, "VAE"], [94, 49, 0, 51, 0, "CLIP_VISION"], [112, 6, 0, 55, 0, "CONDITIONING"], [113, 7, 0, 55, 1, "CONDITIONING"], [114, 39, 0, 55, 2, "VAE"], [115, 55, 0, 3, 1, "CONDITIONING"], [116, 55, 1, 3, 2, "CONDITIONING"], [117, 55, 2, 3, 3, "LATENT"], [118, 52, 0, 55, 4, "IMAGE"], [119, 51, 0, 55, 3, "CLIP_VISION_OUTPUT"], [131, 68, 0, 66, 0, "MODEL"], [132, 65, 0, 67, 0, "MODEL"], [133, 67, 0, 68, 0, "MODEL"], [135, 66, 0, 3, 0, "MODEL"], [146, 37, 0, 65, 0, "MODEL"], [147, 79, 0, 77, 0, "CLIP_VISION"], [148, 86, 0, 77, 1, "IMAGE"], [149, 82, 0, 78, 0, "IMAGE"], [150, 79, 0, 80, 0, "CLIP_VISION"], [151, 87, 0, 80, 1, "IMAGE"], [152, 88, 0, 81, 0, "MODEL"], [153, 84, 0, 82, 0, "LATENT"], [154, 76, 0, 82, 1, "VAE"], [155, 90, 0, 83, 0, "CONDITIONING"], [156, 85, 0, 83, 1, "CONDITIONING"], [157, 76, 0, 83, 2, "VAE"], [158, 80, 0, 83, 3, "CLIP_VISION_OUTPUT"], [159, 77, 0, 83, 4, "CLIP_VISION_OUTPUT"], [160, 87, 0, 83, 5, "IMAGE"], [161, 86, 0, 83, 6, "IMAGE"], [162, 81, 0, 84, 0, "MODEL"], [163, 83, 0, 84, 1, "CONDITIONING"], [164, 83, 1, 84, 2, "CONDITIONING"], [165, 83, 2, 84, 3, "LATENT"], [166, 89, 0, 85, 0, "CLIP"], [167, 89, 0, 90, 0, "CLIP"], [168, 96, 0, 93, 0, "MODEL"], [169, 93, 0, 94, 0, "MODEL"], [170, 94, 0, 95, 0, "MODEL"], [171, 107, 0, 96, 0, "MODEL"], [172, 91, 0, 97, 0, "CLIP"], [173, 106, 0, 99, 0, "IMAGE"], [174, 91, 0, 100, 0, "CLIP"], [175, 92, 0, 103, 0, "CLIP_VISION"], [176, 101, 0, 103, 1, "IMAGE"], [177, 95, 0, 104, 0, "MODEL"], [178, 105, 0, 104, 1, "CONDITIONING"], [179, 105, 1, 104, 2, "CONDITIONING"], [180, 105, 2, 104, 3, "LATENT"], [181, 100, 0, 105, 0, "CONDITIONING"], [182, 97, 0, 105, 1, "CONDITIONING"], [183, 98, 0, 105, 2, "VAE"], [184, 103, 0, 105, 3, "CLIP_VISION_OUTPUT"], [185, 101, 0, 105, 4, "IMAGE"], [186, 102, 0, 105, 5, "IMAGE"], [187, 104, 0, 106, 0, "LATENT"], [188, 98, 0, 106, 1, "VAE"], [190, 108, 0, 109, 0, "IMAGE"], [191, 109, 0, 55, 5, "IMAGE"], [193, 109, 0, 74, 0, "IMAGE"], [194, 52, 0, 110, 0, "IMAGE"], [195, 110, 3, 55, 6, "INT"], [197, 110, 0, 51, 1, "IMAGE"], [198, 110, 4, 55, 7, "INT"]], "groups": [{"id": 1, "title": "Load Models", "bounding": [20, 0, 370, 410], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Start Frame & CLIP Vision", "bounding": [20, 430, 830, 510], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "Attention Booster", "bounding": [870, 0, 600, 410], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "Prompt", "bounding": [410, 0, 440, 410], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "Video and Preprocessor", "bounding": [20, 960, 830, 480], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 8, "title": "Sampling & Decode", "bounding": [870, 430, 600, 400], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 13, "title": "Group", "bounding": [1533.4520263671875, -44.116600036621094, 1490.00048828125, 1023.5999755859375], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 9, "title": "Load Models Here", "bounding": [1543.4520263671875, -0.5165953636169434, 370, 410], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 10, "title": "Start & End frames", "bounding": [1543.4520263671875, 429.4833679199219, 750, 540], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 11, "title": "Prompt", "bounding": [1933.4525146484375, -0.5165953636169434, 360, 410], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 12, "title": "Sampling & Video Generation", "bounding": [2313.453125, -0.5165953636169434, 700, 440], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 14, "title": "Group", "bounding": [10, -43.599998474121094, 1470, 1493.5999755859375], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 20, "title": "Group", "bounding": [3112.107666015625, -66.98226165771484, 1970.001220703125, 993.5997924804688], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 15, "title": "Load Models", "bounding": [3122.107666015625, -23.382259368896484, 370, 410], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 16, "title": "Start & End Images", "bounding": [3122.107666015625, 406.6175231933594, 830, 510], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 17, "title": "Attention Booster", "bounding": [3972.107666015625, -23.382259368896484, 600, 410], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 18, "title": "Prompt", "bounding": [3512.107666015625, -23.382259368896484, 440, 410], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 19, "title": "Sampling & Decode", "bounding": [3972.107666015625, 406.6175231933594, 600, 510], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.8264462809917358, "offset": [1835.197333449355, 243.3122255974306]}, "frontendVersion": "1.18.9", "node_versions": {"comfy-core": "0.3.27"}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}