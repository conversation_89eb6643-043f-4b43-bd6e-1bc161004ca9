{"id": "0c258e12-f212-48d3-b71e-820714cad75f", "revision": 0, "last_node_id": 59, "last_link_id": 111, "nodes": [{"id": 7, "type": "CLIPTextEncode", "pos": [165, 267], "size": [425.27801513671875, 180.6060791015625], "flags": {"collapsed": true}, "order": 6, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 63}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [81]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CLIPTextEncode", "widget_ue_connectable": {"text": true}}, "widgets_values": ["", [false, true]], "color": "#322", "bgcolor": "#533"}, {"id": 34, "type": "DualCLIPLoader", "pos": [-237, 76], "size": [315, 130], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "CLIP名称1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "CLIP名称2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": [62, 63]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "DualCLIPLoader", "widget_ue_connectable": {"clip_name1": true, "clip_name2": true, "type": true, "device": true}}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp16.safetensors", "flux", "default"]}, {"id": 59, "type": "NunchakuFluxDiTLoader", "pos": [960.4320068359375, -229.00271606445312], "size": [315, 202], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "model_path", "name": "model_path", "type": "COMBO", "widget": {"name": "model_path"}, "link": null}, {"localized_name": "cache_threshold", "name": "cache_threshold", "type": "FLOAT", "widget": {"name": "cache_threshold"}, "link": null}, {"localized_name": "attention", "name": "attention", "type": "COMBO", "widget": {"name": "attention"}, "link": null}, {"localized_name": "cpu_offload", "name": "cpu_offload", "type": "COMBO", "widget": {"name": "cpu_offload"}, "link": null}, {"localized_name": "device_id", "name": "device_id", "type": "INT", "widget": {"name": "device_id"}, "link": null}, {"localized_name": "data_type", "name": "data_type", "type": "COMBO", "widget": {"name": "data_type"}, "link": null}, {"localized_name": "i2f_mode", "name": "i2f_mode", "shape": 7, "type": "COMBO", "widget": {"name": "i2f_mode"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [109]}], "properties": {"cnr_id": "comfyui-nunchaku", "ver": "0.2.0", "Node name for S&R": "NunchakuFluxDiTLoader", "widget_ue_connectable": {"model_path": true, "cache_threshold": true, "attention": true, "cpu_offload": true, "device_id": true, "data_type": true, "i2f_mode": true}}, "widgets_values": ["svdq-int4-flux.1-fill-dev", 0, "nunchaku-fp16", "auto", 0, "bfloat16", "enabled"]}, {"id": 48, "type": "Note", "pos": [470.02825927734375, 350.8596496582031], "size": [266.4635925292969, 132.3040771484375], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": "", "widget_ue_connectable": {}}, "widgets_values": ["To add mask for fill inpainting, right click on the uploaded image and select \"Open in MaskEditor\". Use the brush tool to add masking and click save to continue."], "color": "#432", "bgcolor": "#653"}, {"id": 17, "type": "LoadImage", "pos": [-225.73123168945312, 316.9361267089844], "size": [423.5578308105469, 437.250732421875], "flags": {"collapsed": false}, "order": 4, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [110]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "slot_index": 1, "links": [111]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "LoadImage", "widget_ue_connectable": {"image": true, "upload": true}}, "widgets_values": ["clipspace/clipspace-mask-39187.09999999963.png [input]", "image"]}, {"id": 23, "type": "CLIPTextEncode", "pos": [144, -7], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 62}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [41]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CLIPTextEncode", "widget_ue_connectable": {"text": true}}, "widgets_values": ["blue sky", [false, true]], "color": "#232", "bgcolor": "#353"}, {"id": 26, "type": "FluxGuidance", "pos": [583.************, -162.9958953857422], "size": [317.4000244140625, 58], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 41}, {"localized_name": "引导", "name": "guidance", "type": "FLOAT", "widget": {"name": "guidance"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [80]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "FluxGuidance", "widget_ue_connectable": {"guidance": true}}, "widgets_values": [30]}, {"id": 38, "type": "InpaintModelConditioning", "pos": [689.8001098632812, 64.75758361816406], "size": [302.4000244140625, 138], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 80}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 81}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 82}, {"localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 110}, {"localized_name": "遮罩", "name": "mask", "type": "MASK", "link": 111}, {"localized_name": "噪波遮罩", "name": "noise_mask", "type": "BOOLEAN", "widget": {"name": "noise_mask"}, "link": null}], "outputs": [{"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [77]}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [78]}, {"localized_name": "Latent", "name": "latent", "type": "LATENT", "slot_index": 2, "links": [88]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "InpaintModelConditioning", "widget_ue_connectable": {"noise_mask": true}}, "widgets_values": [false]}, {"id": 32, "type": "VAELoader", "pos": [702.2703247070312, 259.36688232421875], "size": [315, 58], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [60, 82]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "VAELoader", "widget_ue_connectable": {"vae_name": true}}, "widgets_values": ["ae.safetensors"]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [1077.832275390625, 54.09292984008789], "size": [315, 262], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 109}, {"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 77}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 78}, {"localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 88}, {"localized_name": "种子", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [7]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "K<PERSON><PERSON><PERSON>", "widget_ue_connectable": {"seed": true, "steps": true, "cfg": true, "sampler_name": true, "scheduler": true, "denoise": true}}, "widgets_values": [580738224928908, "randomize", 20, 1, "euler", "normal", 1]}, {"id": 8, "type": "VAEDecode", "pos": [1326.9005126953125, -153.6060028076172], "size": [210, 46], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 7}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 60}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [95]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "VAEDecode", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [1449.2701416015625, 11.609597206115723], "size": [828.9535522460938, 893.8475341796875], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "图片", "name": "images", "type": "IMAGE", "link": 95}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "SaveImage", "widget_ue_connectable": {"filename_prefix": true}}, "widgets_values": ["ComfyUI"]}], "links": [[7, 3, 0, 8, 0, "LATENT"], [41, 23, 0, 26, 0, "CONDITIONING"], [60, 32, 0, 8, 1, "VAE"], [62, 34, 0, 23, 0, "CLIP"], [63, 34, 0, 7, 0, "CLIP"], [77, 38, 0, 3, 1, "CONDITIONING"], [78, 38, 1, 3, 2, "CONDITIONING"], [80, 26, 0, 38, 0, "CONDITIONING"], [81, 7, 0, 38, 1, "CONDITIONING"], [82, 32, 0, 38, 2, "VAE"], [88, 38, 2, 3, 3, "LATENT"], [95, 8, 0, 9, 0, "IMAGE"], [109, 59, 0, 3, 0, "MODEL"], [110, 17, 0, 38, 3, "IMAGE"], [111, 17, 1, 38, 4, "MASK"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1.01525597994771, "offset": [197.65979278493168, 346.80193755919976]}, "node_versions": {"comfy-core": "0.3.24", "comfyui-inpainteasy": "1.0.2"}, "ue_links": [], "links_added_by_ue": [], "frontendVersion": "1.18.10", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}