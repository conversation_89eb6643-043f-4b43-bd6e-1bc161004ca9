{"id": "ce2cb810-7775-4564-8928-dd5bed1053cd", "revision": 0, "last_node_id": 69, "last_link_id": 158, "nodes": [{"id": 17, "type": "CLIPVisionEncode", "pos": [1545.9541015625, 359.1331481933594], "size": [380.4000244140625, 78], "flags": {}, "order": 23, "mode": 0, "inputs": [{"localized_name": "clip视觉", "name": "clip_vision", "type": "CLIP_VISION", "link": 149}, {"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 116}, {"localized_name": "裁剪", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"localized_name": "CLIP视觉输出", "name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "links": [141]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["center"], "color": "#233", "bgcolor": "#355"}, {"id": 64, "type": "GetNode", "pos": [1554.2071533203125, 486.79547119140625], "size": [210, 60], "flags": {"collapsed": true}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP_VISION", "type": "CLIP_VISION", "links": [149]}], "title": "Get_ClipVisionModle", "properties": {}, "widgets_values": ["ClipVisionModle"], "color": "#233", "bgcolor": "#355"}, {"id": 48, "type": "GetImageSizeAndCount", "pos": [1259.2060546875, 626.8657836914062], "size": [277.20001220703125, 86], "flags": {}, "order": 21, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 125}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [116, 156]}, {"label": "608 width", "localized_name": "width", "name": "width", "type": "INT", "links": null}, {"label": "640 height", "localized_name": "height", "name": "height", "type": "INT", "links": null}, {"label": "1 count", "localized_name": "count", "name": "count", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "8ecf5cd05e0a1012087b0da90eea9a13674668db", "Node name for S&R": "GetImageSizeAndCount"}, "widgets_values": []}, {"id": 60, "type": "GetImageSizeAndCount", "pos": [1279.781494140625, 1060.245361328125], "size": [277.20001220703125, 86], "flags": {}, "order": 22, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 139}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [151, 152]}, {"label": "608 width", "localized_name": "width", "name": "width", "type": "INT", "links": null}, {"label": "640 height", "localized_name": "height", "name": "height", "type": "INT", "links": null}, {"label": "1 count", "localized_name": "count", "name": "count", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "8ecf5cd05e0a1012087b0da90eea9a13674668db", "Node name for S&R": "GetImageSizeAndCount"}, "widgets_values": []}, {"id": 20, "type": "VAEEncode", "pos": [1733.111083984375, 633.30419921875], "size": [210, 46], "flags": {}, "order": 24, "mode": 0, "inputs": [{"localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 156}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 155}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "links": [86]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "VAEEncode"}, "widgets_values": [], "color": "#322", "bgcolor": "#533"}, {"id": 68, "type": "GetNode", "pos": [1729.60693359375, 734.5352172851562], "size": [210, 34], "flags": {"collapsed": true}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [155]}], "title": "Get_VAE", "properties": {}, "widgets_values": ["VAE"], "color": "#322", "bgcolor": "#533"}, {"id": 62, "type": "VAEEncode", "pos": [1612.563232421875, 1048.6236572265625], "size": [210, 46], "flags": {}, "order": 26, "mode": 0, "inputs": [{"localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 152}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 158}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "links": [147]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "VAEEncode"}, "widgets_values": [], "color": "#322", "bgcolor": "#533"}, {"id": 57, "type": "CLIPVisionEncode", "pos": [1600.4202880859375, 1181.36767578125], "size": [380.4000244140625, 78], "flags": {}, "order": 25, "mode": 0, "inputs": [{"localized_name": "clip视觉", "name": "clip_vision", "type": "CLIP_VISION", "link": 150}, {"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 151}, {"localized_name": "裁剪", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"localized_name": "CLIP视觉输出", "name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "links": [132]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["center"], "color": "#233", "bgcolor": "#355"}, {"id": 69, "type": "GetNode", "pos": [1619.6104736328125, 1137.854736328125], "size": [210, 34], "flags": {"collapsed": true}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [158]}], "title": "Get_VAE", "properties": {}, "widgets_values": ["VAE"], "color": "#322", "bgcolor": "#533"}, {"id": 65, "type": "GetNode", "pos": [1604.746337890625, 1306.3175048828125], "size": [210, 34], "flags": {"collapsed": true}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP_VISION", "type": "CLIP_VISION", "links": [150]}], "title": "Get_ClipVisionModle", "properties": {}, "widgets_values": ["ClipVisionModle"], "color": "#233", "bgcolor": "#355"}, {"id": 59, "type": "ImageResize+", "pos": [908.9832763671875, 1062.01123046875], "size": [315, 218], "flags": {}, "order": 19, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 138}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 136}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 137}, {"localized_name": "interpolation", "name": "interpolation", "type": "COMBO", "widget": {"name": "interpolation"}, "link": null}, {"localized_name": "method", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}, {"localized_name": "condition", "name": "condition", "type": "COMBO", "widget": {"name": "condition"}, "link": null}, {"localized_name": "multiple_of", "name": "multiple_of", "type": "INT", "widget": {"name": "multiple_of"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [139]}, {"localized_name": "width", "name": "width", "type": "INT", "links": null}, {"localized_name": "height", "name": "height", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_essentials", "ver": "76e9d1e4399bd025ce8b12c290753d58f9f53e93", "Node name for S&R": "ImageResize+", "aux_id": "kijai/ComfyUI_essentials"}, "widgets_values": [512, 512, "lanc<PERSON>s", "stretch", "always", 0]}, {"id": 50, "type": "ImageResize+", "pos": [907.2653198242188, 593.743896484375], "size": [315, 218], "flags": {}, "order": 18, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 122}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 128}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 127}, {"localized_name": "interpolation", "name": "interpolation", "type": "COMBO", "widget": {"name": "interpolation"}, "link": null}, {"localized_name": "method", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}, {"localized_name": "condition", "name": "condition", "type": "COMBO", "widget": {"name": "condition"}, "link": null}, {"localized_name": "multiple_of", "name": "multiple_of", "type": "INT", "widget": {"name": "multiple_of"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [125]}, {"localized_name": "width", "name": "width", "type": "INT", "links": null}, {"localized_name": "height", "name": "height", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_essentials", "ver": "76e9d1e4399bd025ce8b12c290753d58f9f53e93", "Node name for S&R": "ImageResize+", "aux_id": "kijai/ComfyUI_essentials"}, "widgets_values": [512, 512, "lanc<PERSON>s", "stretch", "always", 0]}, {"id": 51, "type": "FramePackFindNearestBucket", "pos": [550.0997314453125, 887.411376953125], "size": [315, 78], "flags": {}, "order": 14, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 126}, {"localized_name": "base_resolution", "name": "base_resolution", "type": "INT", "widget": {"name": "base_resolution"}, "link": null}], "outputs": [{"localized_name": "width", "name": "width", "type": "INT", "links": [128, 136]}, {"localized_name": "height", "name": "height", "type": "INT", "links": [127, 137]}], "properties": {"aux_id": "kijai/ComfyUI-FramePackWrapper", "ver": "4f9030a9f4c0bd67d86adf3d3dc07e37118c40bd", "Node name for S&R": "FramePackFindNearestBucket"}, "widgets_values": [640]}, {"id": 58, "type": "LoadImage", "pos": [190.07057189941406, 1060.399169921875], "size": [315, 314], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [138]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "title": "Load Image: End", "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "LoadImage"}, "widgets_values": ["企业微信截图_17259673058516.png", "image"]}, {"id": 19, "type": "LoadImage", "pos": [184.2612762451172, 591.6886596679688], "size": [315, 314], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [122, 126]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "title": "Load Image: Start", "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "LoadImage"}, "widgets_values": ["企业微信截图_17259675406238.png", "image"]}, {"id": 67, "type": "GetNode", "pos": [1616.636962890625, 290.5858154296875], "size": [210, 60], "flags": {"collapsed": true}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [154]}], "title": "Get_VAE", "properties": {}, "widgets_values": ["VAE"], "color": "#322", "bgcolor": "#533"}, {"id": 15, "type": "ConditioningZeroOut", "pos": [1351.0010986328125, 501.5396728515625], "size": [317.4000244140625, 26], "flags": {"collapsed": true}, "order": 20, "mode": 0, "inputs": [{"localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 118}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [108]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "ConditioningZeroOut"}, "widgets_values": [], "color": "#332922", "bgcolor": "#593930"}, {"id": 47, "type": "CLIPTextEncode", "pos": [851.2548217773438, 317.73626708984375], "size": [400, 200], "flags": {}, "order": 15, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 102}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [114, 118]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["一个发光的球泡，里面的鱼变成了小熊"], "color": "#232", "bgcolor": "#353"}, {"id": 13, "type": "DualCLIPLoader", "pos": [435.6518249511719, 320.8006286621094], "size": [340.2243957519531, 130], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "CLIP名称1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "CLIP名称2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": [102]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["clip_l.safetensors", "llava_llama3_fp16.safetensors", "hunyuan_video", "default"], "color": "#432", "bgcolor": "#653"}, {"id": 18, "type": "CLIPVisionLoader", "pos": [21.683969497680664, 396.22802734375], "size": [388.87139892578125, 58], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "clip名称", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}], "outputs": [{"localized_name": "CLIP视觉", "name": "CLIP_VISION", "type": "CLIP_VISION", "links": [148]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPVisionLoader"}, "widgets_values": ["sigclip_vision_patch14_384.safetensors"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 63, "type": "SetNode", "pos": [189.8065643310547, 333.4837341308594], "size": [210, 60], "flags": {"collapsed": true}, "order": 16, "mode": 0, "inputs": [{"name": "CLIP_VISION", "type": "CLIP_VISION", "link": 148}], "outputs": [{"name": "*", "type": "*", "links": null}], "title": "Set_ClipVisionModle", "properties": {"previousName": "ClipVisionModle"}, "widgets_values": ["ClipVisionModle"], "color": "#233", "bgcolor": "#355"}, {"id": 27, "type": "FramePackTorchCompileSettings", "pos": [516.8995971679688, 34.318092346191406], "size": [531.5999755859375, 202], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "backend", "name": "backend", "type": "COMBO", "widget": {"name": "backend"}, "link": null}, {"localized_name": "fullgraph", "name": "fullgraph", "type": "BOOLEAN", "widget": {"name": "fullgraph"}, "link": null}, {"localized_name": "mode", "name": "mode", "type": "COMBO", "widget": {"name": "mode"}, "link": null}, {"localized_name": "dynamic", "name": "dynamic", "type": "BOOLEAN", "widget": {"name": "dynamic"}, "link": null}, {"localized_name": "dynamo_cache_size_limit", "name": "dynamo_cache_size_limit", "type": "INT", "widget": {"name": "dynamo_cache_size_limit"}, "link": null}, {"localized_name": "compile_single_blocks", "name": "compile_single_blocks", "type": "BOOLEAN", "widget": {"name": "compile_single_blocks"}, "link": null}, {"localized_name": "compile_double_blocks", "name": "compile_double_blocks", "type": "BOOLEAN", "widget": {"name": "compile_double_blocks"}, "link": null}], "outputs": [{"localized_name": "torch_compile_args", "name": "torch_compile_args", "type": "FRAMEPACKCOMPILEARGS", "links": []}], "properties": {"aux_id": "lllyasviel/FramePack", "ver": "0e5fe5d7ca13c76fb8e13708f4b92e7c7a34f20c", "Node name for S&R": "FramePackTorchCompileSettings"}, "widgets_values": ["inductor", false, "default", false, 64, true, true]}, {"id": 52, "type": "LoadFramePackModel", "pos": [1109.428466796875, 47.374874114990234], "size": [480.7601013183594, 174], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "compile_args", "name": "compile_args", "shape": 7, "type": "FRAMEPACKCOMPILEARGS", "link": null}, {"localized_name": "lora", "name": "lora", "shape": 7, "type": "FPLORA", "link": null}, {"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "base_precision", "name": "base_precision", "type": "COMBO", "widget": {"name": "base_precision"}, "link": null}, {"localized_name": "quantization", "name": "quantization", "type": "COMBO", "widget": {"name": "quantization"}, "link": null}, {"localized_name": "load_device", "name": "load_device", "type": "COMBO", "widget": {"name": "load_device"}, "link": null}, {"localized_name": "attention_mode", "name": "attention_mode", "shape": 7, "type": "COMBO", "widget": {"name": "attention_mode"}, "link": null}], "outputs": [{"localized_name": "model", "name": "model", "type": "FramePackMODEL", "links": [129]}], "properties": {"aux_id": "kijai/ComfyUI-FramePackWrapper", "ver": "49fe507eca8246cc9d08a8093892f40c1180e88f", "Node name for S&R": "LoadFramePackModel"}, "widgets_values": ["FramePackI2V_HY_fp8_e4m3fn.safetensors", "bf16", "fp8_e4m3fn", "offload_device", "sdpa"]}, {"id": 54, "type": "DownloadAndLoadFramePackModel", "pos": [1633.2513427734375, 79.60786437988281], "size": [315, 130], "flags": {}, "order": 11, "mode": 4, "inputs": [{"localized_name": "compile_args", "name": "compile_args", "shape": 7, "type": "FRAMEPACKCOMPILEARGS", "link": null}, {"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "base_precision", "name": "base_precision", "type": "COMBO", "widget": {"name": "base_precision"}, "link": null}, {"localized_name": "quantization", "name": "quantization", "type": "COMBO", "widget": {"name": "quantization"}, "link": null}, {"localized_name": "attention_mode", "name": "attention_mode", "shape": 7, "type": "COMBO", "widget": {"name": "attention_mode"}, "link": null}], "outputs": [{"localized_name": "model", "name": "model", "type": "FramePackMODEL", "links": null}], "properties": {"aux_id": "kijai/ComfyUI-FramePackWrapper", "ver": "49fe507eca8246cc9d08a8093892f40c1180e88f", "Node name for S&R": "DownloadAndLoadFramePackModel"}, "widgets_values": ["lllyasviel/FramePackI2V_HY", "bf16", "disabled", "sdpa"]}, {"id": 12, "type": "VAELoader", "pos": [1963.2821044921875, 258.72247314453125], "size": [469.0488586425781, 58], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": [153]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "VAELoader"}, "widgets_values": ["hunyuan_video_vae_bf16.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 66, "type": "SetNode", "pos": [2058.245361328125, 221.6692352294922], "size": [210, 60], "flags": {"collapsed": true}, "order": 17, "mode": 0, "inputs": [{"name": "VAE", "type": "VAE", "link": 153}], "outputs": [{"name": "*", "type": "*", "links": null}], "title": "Set_VAE", "properties": {"previousName": "VAE"}, "widgets_values": ["VAE"], "color": "#322", "bgcolor": "#533"}, {"id": 55, "type": "<PERSON>downNote", "pos": [17.528087615966797, -37.01456832885742], "size": [459.8609619140625, 285.9714660644531], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Model links:\n\n[https://huggingface.co/Kijai/HunyuanVideo_comfy/blob/main/FramePackI2V_HY_fp8_e4m3fn.safetensors](https://huggingface.co/Kijai/HunyuanVideo_comfy/blob/main/FramePackI2V_HY_fp8_e4m3fn.safetensors)\n\n[https://huggingface.co/Kijai/HunyuanVideo_comfy/blob/main/FramePackI2V_HY_bf16.safetensors](https://huggingface.co/Kijai/HunyuanVideo_comfy/blob/main/FramePackI2V_HY_bf16.safetensors)\n\nsigclip:\n\n[https://huggingface.co/Comfy-Org/sigclip_vision_384/tree/main](https://huggingface.co/Comfy-Org/sigclip_vision_384/tree/main)\n\ntext encoder and VAE:\n\n[https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged/tree/main/split_files](https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged/tree/main/split_files)"], "color": "#432", "bgcolor": "#653"}, {"id": 39, "type": "FramePackSampler", "pos": [2082.30859375, 577.6467895507812], "size": [365.07305908203125, 814.6473388671875], "flags": {}, "order": 27, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "FramePackMODEL", "link": 129}, {"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 114}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 108}, {"localized_name": "start_latent", "name": "start_latent", "type": "LATENT", "link": 86}, {"localized_name": "image_embeds", "name": "image_embeds", "shape": 7, "type": "CLIP_VISION_OUTPUT", "link": 141}, {"localized_name": "end_latent", "name": "end_latent", "shape": 7, "type": "LATENT", "link": 147}, {"localized_name": "end_image_embeds", "name": "end_image_embeds", "shape": 7, "type": "CLIP_VISION_OUTPUT", "link": 132}, {"localized_name": "initial_samples", "name": "initial_samples", "shape": 7, "type": "LATENT", "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "use_teacache", "name": "use_teacache", "type": "BOOLEAN", "widget": {"name": "use_teacache"}, "link": null}, {"localized_name": "teacache_rel_l1_thresh", "name": "teacache_rel_l1_thresh", "type": "FLOAT", "widget": {"name": "teacache_rel_l1_thresh"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "guidance_scale", "name": "guidance_scale", "type": "FLOAT", "widget": {"name": "guidance_scale"}, "link": null}, {"localized_name": "shift", "name": "shift", "type": "FLOAT", "widget": {"name": "shift"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "latent_window_size", "name": "latent_window_size", "type": "INT", "widget": {"name": "latent_window_size"}, "link": null}, {"localized_name": "total_second_length", "name": "total_second_length", "type": "FLOAT", "widget": {"name": "total_second_length"}, "link": null}, {"localized_name": "gpu_memory_preservation", "name": "gpu_memory_preservation", "type": "FLOAT", "widget": {"name": "gpu_memory_preservation"}, "link": null}, {"localized_name": "sampler", "name": "sampler", "type": "COMBO", "widget": {"name": "sampler"}, "link": null}, {"localized_name": "embed_interpolation", "name": "embed_interpolation", "shape": 7, "type": "COMBO", "widget": {"name": "embed_interpolation"}, "link": null}, {"localized_name": "start_embed_strength", "name": "start_embed_strength", "shape": 7, "type": "FLOAT", "widget": {"name": "start_embed_strength"}, "link": null}, {"localized_name": "denoise_strength", "name": "denoise_strength", "shape": 7, "type": "FLOAT", "widget": {"name": "denoise_strength"}, "link": null}], "outputs": [{"localized_name": "samples", "name": "samples", "type": "LATENT", "links": [85]}], "properties": {"aux_id": "kijai/ComfyUI-FramePackWrapper", "ver": "8e5ec6b7f3acf88255c5d93d062079f18b43aa2b", "Node name for S&R": "FramePackSampler"}, "widgets_values": [30, true, 0.15, 1, 10, 0, 47, "fixed", 9, 5, 6, "unipc_bh1", "weighted_average", 0.5, 1]}, {"id": 33, "type": "VAEDecodeTiled", "pos": [2105.48828125, 370.41510009765625], "size": [315, 150], "flags": {}, "order": 28, "mode": 0, "inputs": [{"localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 85}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 154}, {"localized_name": "分块尺寸", "name": "tile_size", "type": "INT", "widget": {"name": "tile_size"}, "link": null}, {"localized_name": "重叠", "name": "overlap", "type": "INT", "widget": {"name": "overlap"}, "link": null}, {"localized_name": "时间尺寸", "name": "temporal_size", "type": "INT", "widget": {"name": "temporal_size"}, "link": null}, {"localized_name": "时间重叠", "name": "temporal_overlap", "type": "INT", "widget": {"name": "temporal_overlap"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [96]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "VAEDecodeTiled"}, "widgets_values": [256, 64, 64, 8], "color": "#322", "bgcolor": "#533"}, {"id": 23, "type": "VHS_VideoCombine", "pos": [2456.193359375, 580.3538208007812], "size": [908.428955078125, 1283.1883544921875], "flags": {}, "order": 30, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": 97}, {"localized_name": "audio", "name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"localized_name": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"localized_name": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}, {"localized_name": "frame_rate", "name": "frame_rate", "type": "FLOAT", "widget": {"name": "frame_rate"}, "link": null}, {"localized_name": "loop_count", "name": "loop_count", "type": "INT", "widget": {"name": "loop_count"}, "link": null}, {"localized_name": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}, {"localized_name": "format", "name": "format", "type": "COMBO", "widget": {"name": "format"}, "link": null}, {"localized_name": "pingpong", "name": "pingpong", "type": "BOOLEAN", "widget": {"name": "pingpong"}, "link": null}, {"localized_name": "save_output", "name": "save_output", "type": "BOOLEAN", "widget": {"name": "save_output"}, "link": null}], "outputs": [{"localized_name": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 30, "loop_count": 0, "filename_prefix": "FramePack", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "FramePack_00001.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 30, "workflow": "FramePack_00001.png", "fullpath": "H:\\ComfyUI_WAN2.1\\ComfyUI\\temp\\FramePack_00001.mp4"}}}}, {"id": 44, "type": "GetImageSizeAndCount", "pos": [2456.080322265625, 445.97235107421875], "size": [277.20001220703125, 86], "flags": {}, "order": 29, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 96}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [97]}, {"label": "608 width", "localized_name": "width", "name": "width", "type": "INT", "links": null}, {"label": "640 height", "localized_name": "height", "name": "height", "type": "INT", "links": null}, {"label": "145 count", "localized_name": "count", "name": "count", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "8ecf5cd05e0a1012087b0da90eea9a13674668db", "Node name for S&R": "GetImageSizeAndCount"}, "widgets_values": []}], "links": [[85, 39, 0, 33, 0, "LATENT"], [86, 20, 0, 39, 3, "LATENT"], [96, 33, 0, 44, 0, "IMAGE"], [97, 44, 0, 23, 0, "IMAGE"], [102, 13, 0, 47, 0, "CLIP"], [108, 15, 0, 39, 2, "CONDITIONING"], [114, 47, 0, 39, 1, "CONDITIONING"], [116, 48, 0, 17, 1, "IMAGE"], [118, 47, 0, 15, 0, "CONDITIONING"], [122, 19, 0, 50, 0, "IMAGE"], [125, 50, 0, 48, 0, "IMAGE"], [126, 19, 0, 51, 0, "IMAGE"], [127, 51, 1, 50, 2, "INT"], [128, 51, 0, 50, 1, "INT"], [129, 52, 0, 39, 0, "FramePackMODEL"], [132, 57, 0, 39, 6, "CLIP_VISION_OUTPUT"], [136, 51, 0, 59, 1, "INT"], [137, 51, 1, 59, 2, "INT"], [138, 58, 0, 59, 0, "IMAGE"], [139, 59, 0, 60, 0, "IMAGE"], [141, 17, 0, 39, 4, "CLIP_VISION_OUTPUT"], [147, 62, 0, 39, 5, "LATENT"], [148, 18, 0, 63, 0, "*"], [149, 64, 0, 17, 0, "CLIP_VISION"], [150, 65, 0, 57, 0, "CLIP_VISION"], [151, 60, 0, 57, 1, "IMAGE"], [152, 60, 0, 62, 0, "IMAGE"], [153, 12, 0, 66, 0, "*"], [154, 67, 0, 33, 1, "VAE"], [155, 68, 0, 20, 1, "VAE"], [156, 48, 0, 20, 0, "IMAGE"], [158, 69, 0, 62, 1, "VAE"]], "groups": [{"id": 1, "title": "End Image", "bounding": [12.77297592163086, 999.1203002929688, 2038.674560546875, 412.9618225097656], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Start Image", "bounding": [11.781991958618164, 531.3884887695312, 2032.7288818359375, 442.6904602050781], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.6115909044841659, "offset": [-449.24619019655483, -516.7810093276173]}, "frontendVersion": "1.18.9", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}