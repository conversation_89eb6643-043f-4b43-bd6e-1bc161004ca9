{"id": "4fcbe3d6-0fbd-4a1e-87f7-d9cfeb6a49f1", "revision": 0, "last_node_id": 59, "last_link_id": 122, "nodes": [{"id": 13, "type": "EmptyLatentImage", "pos": [833.3805541992188, 864.947265625], "size": [272.08251953125, 106], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "宽度", "localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 15}, {"label": "高度", "localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 16}, {"localized_name": "批量大小", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}, "link": null}], "outputs": [{"label": "Latent", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "EmptyLatentImage"}, "widgets_values": [512, 512, 1]}, {"id": 14, "type": "GetImageSize+", "pos": [833.3805541992188, 984.947265625], "size": [263.6844787597656, 66], "flags": {}, "order": 8, "mode": 0, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 19}], "outputs": [{"label": "宽度", "localized_name": "width", "name": "width", "type": "INT", "links": [15]}, {"label": "高度", "localized_name": "height", "name": "height", "type": "INT", "links": [16]}, {"localized_name": "count", "name": "count", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 17, "type": "VAEDecode", "pos": [833.3805541992188, 1084.947265625], "size": [259.9089050292969, 46], "flags": {}, "order": 22, "mode": 0, "inputs": [{"label": "Latent", "localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 21}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 100}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [22]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 52, "type": "VAEEncode", "pos": [575.1078491210938, 600.5816650390625], "size": [140, 46], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 102}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 103}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "links": [104]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEEncode"}, "widgets_values": []}, {"id": 6, "type": "PrepImageForClipVision", "pos": [-132.15957641601562, 537.3126220703125], "size": [315, 106], "flags": {"collapsed": false}, "order": 7, "mode": 0, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 18}, {"localized_name": "interpolation", "name": "interpolation", "type": "COMBO", "widget": {"name": "interpolation"}, "link": null}, {"localized_name": "crop_position", "name": "crop_position", "type": "COMBO", "widget": {"name": "crop_position"}, "link": null}, {"localized_name": "sharpening", "name": "sharpening", "type": "FLOAT", "widget": {"name": "sharpening"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": []}], "properties": {"cnr_id": "comfyui_ipadapter_plus", "ver": "2.0.0", "Node name for S&R": "PrepImageForClipVision"}, "widgets_values": ["LANCZOS", "top", 0]}, {"id": 5, "type": "PrepImageForClipVision", "pos": [-130.40249633789062, 696.14306640625], "size": [315, 106], "flags": {"collapsed": false}, "order": 0, "mode": 0, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "link": null}, {"localized_name": "interpolation", "name": "interpolation", "type": "COMBO", "widget": {"name": "interpolation"}, "link": null}, {"localized_name": "crop_position", "name": "crop_position", "type": "COMBO", "widget": {"name": "crop_position"}, "link": null}, {"localized_name": "sharpening", "name": "sharpening", "type": "FLOAT", "widget": {"name": "sharpening"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": []}], "properties": {"cnr_id": "comfyui_ipadapter_plus", "ver": "2.0.0", "Node name for S&R": "PrepImageForClipVision"}, "widgets_values": ["LANCZOS", "top", 0]}, {"id": 16, "type": "LoadImage", "pos": [1171.9066162109375, 390.4427490234375], "size": [364.8806457519531, 661.5585327148438], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [18, 19, 59, 102, 108]}, {"label": "遮罩", "localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["ComfyUI_temp_fdsxd_00034_.png", "image"]}, {"id": 54, "type": "Florence2ModelLoader", "pos": [-709.3903198242188, 579.2507934570312], "size": [270, 106], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "lora", "name": "lora", "shape": 7, "type": "PEFTLORA", "link": null}, {"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "precision", "name": "precision", "type": "COMBO", "widget": {"name": "precision"}, "link": null}, {"localized_name": "attention", "name": "attention", "type": "COMBO", "widget": {"name": "attention"}, "link": null}], "outputs": [{"localized_name": "florence2_model", "name": "florence2_model", "type": "FL2MODEL", "links": []}], "properties": {"cnr_id": "comfyui-florence2", "ver": "1.0.3", "Node name for S&R": "Florence2ModelLoader"}, "widgets_values": ["Florence-2-Flux-Large", "fp16", "sdpa"]}, {"id": 50, "type": "ControlNetLoader", "pos": [517.6375732421875, 925.078369140625], "size": [270, 58], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "ControlNet名称", "name": "control_net_name", "type": "COMBO", "widget": {"name": "control_net_name"}, "link": null}], "outputs": [{"localized_name": "ControlNet", "name": "CONTROL_NET", "type": "CONTROL_NET", "links": [90]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ControlNetLoader"}, "widgets_values": ["XL\\CN-anytest_v4-marged.safetensors"]}, {"id": 9, "type": "ControlNetApplyAdvanced", "pos": [466.6900329589844, 1033.141845703125], "size": [315, 186], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "正面条件", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 8}, {"label": "负面条件", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 9}, {"label": "ControlNet", "localized_name": "ControlNet", "name": "control_net", "type": "CONTROL_NET", "link": 90}, {"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 60}, {"localized_name": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": 101}, {"localized_name": "强度", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}, "link": null}, {"localized_name": "开始百分比", "name": "start_percent", "type": "FLOAT", "widget": {"name": "start_percent"}, "link": null}, {"localized_name": "结束百分比", "name": "end_percent", "type": "FLOAT", "widget": {"name": "end_percent"}, "link": null}], "outputs": [{"label": "正面条件", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [96]}, {"label": "负面条件", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [97]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ControlNetApplyAdvanced"}, "widgets_values": [0.6000000000000001, 0, 1]}, {"id": 35, "type": "ShowText|pysssss", "pos": [210.81101989746094, 907.2203369140625], "size": [210, 174.59814453125], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "文本", "localized_name": "text", "name": "text", "type": "STRING", "link": 58}], "outputs": [{"label": "字符串", "localized_name": "字符串", "name": "STRING", "shape": 6, "type": "STRING", "links": [111]}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["pixel-art The image is an illustration of a young girl with long white hair and red eyes. She is standing in front of a plain white background. The girl is wearing a yellow long-sleeved top with a white collar and a pink bow on the neckline. The top has a small white bunny embroidered on the right side of the chest. She has a brown teddy bear with a yellow bow on its head. Her hair is styled in loose waves and falls over her shoulders. The overall mood of the image is peaceful and serene. . low-res, blocky, pixel art style, 8-bit graphics"]}, {"id": 53, "type": "Florence2Run", "pos": [-828.466796875, 769.370849609375], "size": [400, 364], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 108}, {"localized_name": "florence2_model", "name": "florence2_model", "type": "FL2MODEL", "link": 122}, {"localized_name": "text_input", "name": "text_input", "type": "STRING", "widget": {"name": "text_input"}, "link": null}, {"localized_name": "task", "name": "task", "type": "COMBO", "widget": {"name": "task"}, "link": null}, {"localized_name": "fill_mask", "name": "fill_mask", "type": "BOOLEAN", "widget": {"name": "fill_mask"}, "link": null}, {"localized_name": "keep_model_loaded", "name": "keep_model_loaded", "shape": 7, "type": "BOOLEAN", "widget": {"name": "keep_model_loaded"}, "link": null}, {"localized_name": "max_new_tokens", "name": "max_new_tokens", "shape": 7, "type": "INT", "widget": {"name": "max_new_tokens"}, "link": null}, {"localized_name": "num_beams", "name": "num_beams", "shape": 7, "type": "INT", "widget": {"name": "num_beams"}, "link": null}, {"localized_name": "do_sample", "name": "do_sample", "shape": 7, "type": "BOOLEAN", "widget": {"name": "do_sample"}, "link": null}, {"localized_name": "output_mask_select", "name": "output_mask_select", "shape": 7, "type": "STRING", "widget": {"name": "output_mask_select"}, "link": null}, {"localized_name": "seed", "name": "seed", "shape": 7, "type": "INT", "widget": {"name": "seed"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": null}, {"localized_name": "mask", "name": "mask", "type": "MASK", "links": null}, {"localized_name": "caption", "name": "caption", "type": "STRING", "links": [112]}, {"localized_name": "data", "name": "data", "type": "JSON", "links": null}], "properties": {"cnr_id": "comfyui-florence2", "ver": "1.0.3", "Node name for S&R": "Florence2Run"}, "widgets_values": ["", "more_detailed_caption", true, false, 1024, 3, true, "", 590211674111646, "randomize"]}, {"id": 58, "type": "Florence2ModelLoader", "pos": [-1141.846435546875, 773.7638549804688], "size": [270, 106], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "lora", "name": "lora", "shape": 7, "type": "PEFTLORA", "link": null}, {"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "precision", "name": "precision", "type": "COMBO", "widget": {"name": "precision"}, "link": null}, {"localized_name": "attention", "name": "attention", "type": "COMBO", "widget": {"name": "attention"}, "link": null}], "outputs": [{"localized_name": "florence2_model", "name": "florence2_model", "type": "FL2MODEL", "links": [122]}], "properties": {"cnr_id": "comfyui-florence2", "ver": "1.0.3", "Node name for S&R": "Florence2ModelLoader"}, "widgets_values": ["Florence-2-Flux-Large", "fp16", "sdpa"]}, {"id": 51, "type": "VAELoader", "pos": [264.86328125, 579.5194702148438], "size": [270, 58], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": [100, 101, 103]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAELoader"}, "widgets_values": ["sdxl_vae.safetensors"]}, {"id": 8, "type": "CLIPTextEncode", "pos": [6.41224479675293, 1354.7896728515625], "size": [423.19866943359375, 123.96846008300781], "flags": {}, "order": 19, "mode": 0, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 120}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 116}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [9]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["embedding:EasyNegative, "]}, {"id": 7, "type": "CLIPTextEncode", "pos": [56.986305236816406, 1168.358642578125], "size": [308.1636047363281, 128.8979949951172], "flags": {"collapsed": false}, "order": 18, "mode": 0, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 121}, {"label": "文本", "localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 111}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [8]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["1 cute girl, looking at camera, cinematic, colorful background, concept art, 8k, "]}, {"id": 12, "type": "K<PERSON><PERSON><PERSON>", "pos": [833.1412353515625, 333.559814453125], "size": [266.03814697265625, 474], "flags": {}, "order": 21, "mode": 0, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 119}, {"label": "正面条件", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 96}, {"label": "负面条件", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 97}, {"label": "Latent", "localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 104}, {"localized_name": "种子", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"label": "Latent", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [21]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [831902441355157, "randomize", 20, 7, "dpmpp_sde", "karras", 0.5000000000000001]}, {"id": 36, "type": "CannyEdgePreprocessor", "pos": [468.21051025390625, 1274.2103271484375], "size": [315, 106], "flags": {}, "order": 9, "mode": 0, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 59}, {"localized_name": "low_threshold", "name": "low_threshold", "shape": 7, "type": "INT", "widget": {"name": "low_threshold"}, "link": null}, {"localized_name": "high_threshold", "name": "high_threshold", "shape": 7, "type": "INT", "widget": {"name": "high_threshold"}, "link": null}, {"localized_name": "resolution", "name": "resolution", "shape": 7, "type": "INT", "widget": {"name": "resolution"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [60]}], "properties": {"cnr_id": "comfyui_controlnet_aux", "ver": "1.0.7", "Node name for S&R": "CannyEdgePreprocessor"}, "widgets_values": [100, 200, 1024]}, {"id": 1, "type": "CheckpointLoaderSimple", "pos": [-397.0455627441406, 837.7036743164062], "size": [315, 98], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "Checkpoint名称", "name": "ckpt_name", "type": "COMBO", "widget": {"name": "ckpt_name"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [117]}, {"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [118]}, {"label": "VAE", "localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 2, "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["juggernautXL_ragnarokBy.safetensors"]}, {"id": 55, "type": "ShowText|pysssss", "pos": [-351.6003112792969, 608.8276977539062], "size": [210, 174.59814453125], "flags": {}, "order": 14, "mode": 0, "inputs": [{"label": "文本", "localized_name": "text", "name": "text", "type": "STRING", "link": 112}], "outputs": [{"label": "字符串", "localized_name": "字符串", "name": "STRING", "shape": 6, "type": "STRING", "links": [113]}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["The image is an illustration of a young girl with long white hair and red eyes. She is standing in front of a plain white background. The girl is wearing a yellow long-sleeved top with a white collar and a pink bow on the neckline. The top has a small white bunny embroidered on the right side of the chest. She has a brown teddy bear with a yellow bow on its head. Her hair is styled in loose waves and falls over her shoulders. The overall mood of the image is peaceful and serene."]}, {"id": 57, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-406.3061828613281, 974.5595703125], "size": [270, 126], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 117}, {"localized_name": "CLIPCLIP", "name": "clip", "type": "CLIP", "link": 118}, {"localized_name": "LoRA名称", "name": "lora_name", "type": "COMBO", "widget": {"name": "lora_name"}, "link": null}, {"localized_name": "模型强度", "name": "strength_model", "type": "FLOAT", "widget": {"name": "strength_model"}, "link": null}, {"localized_name": "CLIP强度", "name": "strength_clip", "type": "FLOAT", "widget": {"name": "strength_clip"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [119]}, {"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": [120, 121]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["pixel-art-xl-v1.1.safetensors", 1, 1]}, {"id": 18, "type": "PreviewImage", "pos": [1552.0936279296875, 421.0853576660156], "size": [540.6390380859375, 615.7543334960938], "flags": {}, "order": 23, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 22}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 34, "type": "SDXLPromptStyler", "pos": [-27.999919891357422, 869.4348754882812], "size": [215.72227478027344, 258], "flags": {}, "order": 15, "mode": 0, "inputs": [{"localized_name": "text_positive", "name": "text_positive", "type": "STRING", "widget": {"name": "text_positive"}, "link": 113}, {"localized_name": "text_negative", "name": "text_negative", "type": "STRING", "widget": {"name": "text_negative"}, "link": null}, {"localized_name": "style", "name": "style", "type": "COMBO", "widget": {"name": "style"}, "link": null}, {"localized_name": "log_prompt", "name": "log_prompt", "type": "BOOLEAN", "widget": {"name": "log_prompt"}, "link": null}, {"localized_name": "style_positive", "name": "style_positive", "type": "BOOLEAN", "widget": {"name": "style_positive"}, "link": null}, {"localized_name": "style_negative", "name": "style_negative", "type": "BOOLEAN", "widget": {"name": "style_negative"}, "link": null}], "outputs": [{"label": "正面条件", "localized_name": "text_positive", "name": "text_positive", "type": "STRING", "slot_index": 0, "links": [58]}, {"label": "负面条件", "localized_name": "text_negative", "name": "text_negative", "type": "STRING", "links": [115]}], "properties": {"cnr_id": "sdxl_prompt_styler", "ver": "51068179927f79dce14f38c6b1984390ab242be2", "Node name for S&R": "SDXLPromptStyler"}, "widgets_values": ["1 girl", "", "sai-pixel art", true, true, true]}, {"id": 56, "type": "ShowText|pysssss", "pos": [-223.18301391601562, 1176.71337890625], "size": [210, 174.59814453125], "flags": {}, "order": 17, "mode": 0, "inputs": [{"label": "文本", "localized_name": "text", "name": "text", "type": "STRING", "link": 115}], "outputs": [{"label": "字符串", "localized_name": "字符串", "name": "STRING", "shape": 6, "type": "STRING", "links": [116]}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["sloppy, messy, blurry, noisy, highly detailed, ultra textured, photo, realistic"]}], "links": [[8, 7, 0, 9, 0, "CONDITIONING"], [9, 8, 0, 9, 1, "CONDITIONING"], [15, 14, 0, 13, 0, "INT"], [16, 14, 1, 13, 1, "INT"], [18, 16, 0, 6, 0, "IMAGE"], [19, 16, 0, 14, 0, "IMAGE"], [21, 12, 0, 17, 0, "LATENT"], [22, 17, 0, 18, 0, "IMAGE"], [58, 34, 0, 35, 0, "STRING"], [59, 16, 0, 36, 0, "IMAGE"], [60, 36, 0, 9, 3, "IMAGE"], [90, 50, 0, 9, 2, "CONTROL_NET"], [96, 9, 0, 12, 1, "CONDITIONING"], [97, 9, 1, 12, 2, "CONDITIONING"], [100, 51, 0, 17, 1, "VAE"], [101, 51, 0, 9, 4, "VAE"], [102, 16, 0, 52, 0, "IMAGE"], [103, 51, 0, 52, 1, "VAE"], [104, 52, 0, 12, 3, "LATENT"], [108, 16, 0, 53, 0, "IMAGE"], [111, 35, 0, 7, 1, "STRING"], [112, 53, 2, 55, 0, "STRING"], [113, 55, 0, 34, 0, "STRING"], [115, 34, 1, 56, 0, "STRING"], [116, 56, 0, 8, 1, "STRING"], [117, 1, 0, 57, 0, "MODEL"], [118, 1, 1, 57, 1, "CLIP"], [119, 57, 0, 12, 0, "MODEL"], [120, 57, 1, 8, 0, "CLIP"], [121, 57, 1, 7, 0, "CLIP"], [122, 58, 0, 53, 1, "FL2MODEL"]], "groups": [{"id": 1, "title": "识别面部特征", "bounding": [230.76307678222656, 498.4912414550781, 541.7193603515625, 288.39459228515625], "color": "#A88", "font_size": 24, "flags": {}}, {"id": 3, "title": "图像提示词和风格", "bounding": [-37, 823, 460, 488], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "模型加载 VX：AIGC-zsjh", "bounding": [-141, 404, 337, 406], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "图像生成", "bounding": [813, 271, 302, 870], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "线条控制", "bounding": [447, 875, 347, 540], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.3073560549142854, "offset": [107.8159275787388, -258.22012522514876]}, "frontendVersion": "1.18.9", "workspace_info": {"id": "fb779c72-35f5-4566-8d50-70e6b8debc62"}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}