{"id": "4016bf47-4c36-400d-bb48-0237fd656be3", "revision": 0, "last_node_id": 22, "last_link_id": 29, "nodes": [{"id": 8, "type": "VAEDecode", "pos": [1057, 42], "size": [210, 46], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 7}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 8}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [10]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 14, "type": "PreviewImage", "pos": [1443, 123], "size": [550, 937], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 10}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 5, "type": "EmptyLatentImage", "pos": [689, 15], "size": [315, 106], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 26}, {"localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 27}, {"localized_name": "批量大小", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}, "link": null}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [2]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "EmptyLatentImage"}, "widgets_values": [512, 512, 1]}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [244, -2], "size": [315, 98], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "Checkpoint名称", "name": "ckpt_name", "type": "COMBO", "widget": {"name": "ckpt_name"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [1]}, {"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [20, 22]}, {"localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 2, "links": [8]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["IL\\illustrij14.Oqel.safetensors"]}, {"id": 19, "type": "CLIPTextEncode", "pos": [693.7008666992188, 801.0685424804688], "size": [400, 200], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 22}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [23]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["", [false, true]]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [1117.133056640625, 360.06683349609375], "size": [315, 262], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 1}, {"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 13}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 23}, {"localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 2}, {"localized_name": "种子", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [7]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [46806059861263, "randomize", 20, 8, "euler", "normal", 1]}, {"id": 11, "type": "ControlNetApply", "pos": [702.5913696289062, 499.274658203125], "size": [317.4000244140625, 98], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 21}, {"localized_name": "ControlNet", "name": "control_net", "type": "CONTROL_NET", "link": 29}, {"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 15}, {"localized_name": "强度", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [13]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ControlNetApply"}, "widgets_values": [1]}, {"id": 18, "type": "CLIPTextEncode", "pos": [677.75146484375, 184.93992614746094], "size": [400, 200], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 20}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [21]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["1girl,anime", [false, true]]}, {"id": 20, "type": "PreviewImage", "pos": [289.4742126464844, 309.41961669921875], "size": [252, 368.6667175292969], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 24}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 15, "type": "PoseNode", "pos": [-31.1561279296875, 241.35549926757812], "size": [280.32025146484375, 447.59417724609375], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [15, 24, 25]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfyui_custom_nodes_alekpet", "ver": "1.0.66", "Node name for S&R": "PoseNode"}, "widgets_values": ["Pose_15.png", "add_pose", "reset_pose", {"undo_history": [{"keypoints": [[241, 77], [241, 120], [191, 118], [177, 183], [163, 252], [298, 118], [317, 182], [332, 245], [225, 241], [213, 359], [215, 454], [270, 240], [282, 360], [286, 456], [232, 59], [253, 60], [225, 70], [260, 72]]}, {"keypoints": [[241, 77], [241, 120], [191, 118], [177, 183], [163, 252], [298, 118], [317, 182], [332, 245], [225, 241], [213, 359], [215, 454], [270, 240], [282, 360], [286, 456], [232, 59], [253, 60], [225, 70], [260, 72]]}, {"keypoints": [[241, 77], [241, 120], [191, 118], [177, 183], [163, 252], [298, 118], [317, 182], [354, 311], [225, 241], [213, 359], [215, 454], [270, 240], [282, 360], [286, 456], [232, 59], [253, 60], [225, 70], [260, 72]]}, {"keypoints": [[241, 77], [209, 253], [191, 118], [177, 183], [163, 252], [298, 118], [317, 182], [354, 311], [225, 241], [213, 359], [215, 454], [270, 240], [282, 360], [286, 456], [232, 59], [253, 60], [225, 70], [260, 72]]}, {"keypoints": [[241, 77], [209, 253], [176, 224], [177, 183], [163, 252], [298, 118], [317, 182], [354, 311], [225, 241], [213, 359], [215, 454], [270, 240], [282, 360], [286, 456], [232, 59], [253, 60], [225, 70], [260, 72]]}, {"keypoints": [[241, 77], [209, 253], [176, 224], [140, 244], [163, 252], [298, 118], [317, 182], [354, 311], [225, 241], [213, 359], [215, 454], [270, 240], [282, 360], [286, 456], [232, 59], [253, 60], [225, 70], [260, 72]]}, {"keypoints": [[241, 77], [209, 253], [176, 224], [140, 244], [190, 465], [298, 118], [317, 182], [354, 311], [225, 241], [213, 359], [215, 454], [270, 240], [282, 360], [286, 456], [232, 59], [253, 60], [225, 70], [260, 72]]}, {"keypoints": [[241, 77], [209, 253], [176, 224], [131, 353], [190, 465], [298, 118], [317, 182], [354, 311], [225, 241], [213, 359], [215, 454], [270, 240], [282, 360], [286, 456], [232, 59], [253, 60], [225, 70], [260, 72]]}, {"keypoints": [[241, 77], [209, 253], [147, 254], [131, 353], [190, 465], [298, 118], [317, 182], [354, 311], [225, 241], [213, 359], [215, 454], [270, 240], [282, 360], [286, 456], [232, 59], [253, 60], [225, 70], [260, 72]]}, {"keypoints": [[241, 77], [209, 253], [147, 254], [131, 353], [190, 465], [321, 260], [317, 182], [354, 311], [225, 241], [213, 359], [215, 454], [270, 240], [282, 360], [286, 456], [232, 59], [253, 60], [225, 70], [260, 72]]}, {"keypoints": [[241, 77], [209, 253], [147, 254], [131, 353], [190, 465], [321, 260], [326, 370], [354, 311], [225, 241], [213, 359], [215, 454], [270, 240], [282, 360], [286, 456], [232, 59], [253, 60], [225, 70], [260, 72]]}, {"keypoints": [[241, 77], [209, 253], [147, 254], [131, 353], [190, 465], [321, 260], [326, 370], [245, 489], [225, 241], [213, 359], [215, 454], [270, 240], [282, 360], [286, 456], [232, 59], [253, 60], [225, 70], [260, 72]]}, {"keypoints": [[241, 77], [209, 253], [147, 254], [131, 353], [190, 465], [321, 260], [326, 370], [245, 489], [225, 241], [66, 423], [215, 454], [270, 240], [282, 360], [286, 456], [232, 59], [253, 60], [225, 70], [260, 72]]}, {"keypoints": [[241, 77], [209, 253], [147, 254], [131, 353], [190, 465], [321, 260], [326, 370], [245, 489], [225, 241], [66, 423], [142, 607], [270, 240], [282, 360], [286, 456], [232, 59], [253, 60], [225, 70], [260, 72]]}, {"keypoints": [[241, 77], [209, 253], [147, 254], [131, 353], [190, 465], [321, 260], [326, 370], [245, 489], [225, 241], [167, 508], [142, 607], [270, 240], [282, 360], [286, 456], [232, 59], [253, 60], [225, 70], [260, 72]]}, {"keypoints": [[241, 77], [209, 253], [147, 254], [131, 353], [190, 465], [321, 260], [326, 370], [245, 489], [224, 402], [167, 508], [142, 607], [270, 240], [282, 360], [286, 456], [232, 59], [253, 60], [225, 70], [260, 72]]}, {"keypoints": [[195, 200], [209, 253], [147, 254], [131, 353], [190, 465], [321, 260], [326, 370], [245, 489], [224, 402], [167, 508], [142, 607], [270, 240], [282, 360], [286, 456], [232, 59], [253, 60], [225, 70], [260, 72]]}, {"keypoints": [[195, 200], [209, 253], [147, 254], [131, 353], [190, 465], [321, 260], [326, 370], [245, 489], [224, 402], [167, 508], [142, 607], [270, 240], [282, 360], [286, 456], [232, 59], [253, 60], [225, 70], [330, 160]]}, {"keypoints": [[195, 200], [209, 253], [147, 254], [131, 353], [190, 465], [321, 260], [326, 370], [245, 489], [224, 402], [167, 508], [142, 607], [270, 240], [282, 360], [286, 456], [232, 59], [263, 154], [225, 70], [330, 160]]}, {"keypoints": [[195, 200], [209, 253], [147, 254], [131, 353], [190, 465], [321, 260], [326, 370], [245, 489], [224, 402], [167, 508], [142, 607], [270, 240], [282, 360], [286, 456], [232, 59], [263, 154], [86, 164], [330, 160]]}, {"keypoints": [[195, 200], [209, 253], [147, 254], [131, 353], [190, 465], [321, 260], [326, 370], [245, 489], [224, 402], [167, 508], [142, 607], [270, 240], [282, 360], [286, 456], [142, 153], [263, 154], [86, 164], [330, 160]]}, {"keypoints": [[195, 200], [209, 253], [147, 254], [131, 353], [190, 465], [321, 260], [326, 370], [245, 489], [224, 402], [167, 508], [142, 607], [269, 500], [282, 360], [286, 456], [142, 153], [263, 154], [86, 164], [330, 160]]}, {"keypoints": [[195, 200], [209, 253], [147, 254], [131, 353], [190, 465], [321, 260], [326, 370], [245, 489], [207, 505], [167, 508], [142, 607], [269, 500], [282, 360], [286, 456], [142, 153], [263, 154], [86, 164], [330, 160]]}, {"keypoints": [[195, 200], [209, 253], [147, 254], [131, 353], [190, 465], [321, 260], [326, 370], [245, 489], [207, 505], [196, 600], [142, 607], [269, 500], [282, 360], [286, 456], [142, 153], [263, 154], [86, 164], [330, 160]]}, {"keypoints": [[195, 200], [209, 253], [147, 254], [131, 353], [190, 465], [321, 260], [326, 370], [245, 489], [207, 505], [196, 600], [142, 607], [269, 500], [320, 577], [286, 456], [142, 153], [263, 154], [86, 164], [330, 160]]}, {"keypoints": [[195, 200], [209, 253], [147, 254], [131, 353], [190, 465], [321, 260], [326, 370], [245, 489], [207, 505], [196, 600], [142, 607], [269, 500], [320, 577], [368, 616], [142, 153], [263, 154], [86, 164], [330, 160]]}, {"keypoints": [[195, 200], [209, 253], [147, 254], [131, 353], [190, 465], [321, 260], [326, 370], [245, 489], [207, 505], [196, 600], [142, 607], [425, 426], [320, 577], [368, 616], [142, 153], [263, 154], [86, 164], [330, 160]]}, {"keypoints": [[195, 200], [209, 253], [147, 254], [131, 353], [190, 465], [321, 260], [326, 370], [245, 489], [240, 512], [196, 600], [142, 607], [425, 426], [320, 577], [368, 616], [142, 153], [263, 154], [86, 164], [330, 160]]}, {"keypoints": [[195, 200], [209, 253], [147, 254], [131, 353], [190, 465], [321, 260], [326, 370], [245, 489], [240, 512], [196, 600], [142, 607], [426, 487], [320, 577], [368, 616], [142, 153], [263, 154], [86, 164], [330, 160]]}], "redo_history": [], "currentCanvasSize": {"width": 512, "height": 512}, "background": "/api/view?filename=ComfyUI_temp_eimqy_00006_.png&type=input&subfolder=&rand=0.1586942866868224"}]}, {"id": 21, "type": "GetImageSize+", "pos": [407.2521667480469, 162.89208984375], "size": [159.50155639648438, 66], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 25}], "outputs": [{"localized_name": "width", "name": "width", "type": "INT", "links": [26]}, {"localized_name": "height", "name": "height", "type": "INT", "links": [27]}, {"localized_name": "count", "name": "count", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "GetImageSize+"}}, {"id": 13, "type": "ControlNetLoader", "pos": [6.05487060546875, 827.0906372070312], "size": [315, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "ControlNet名称", "name": "control_net_name", "type": "COMBO", "widget": {"name": "control_net_name"}, "link": null}], "outputs": [{"localized_name": "ControlNet", "name": "CONTROL_NET", "type": "CONTROL_NET", "slot_index": 0, "links": [28]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ControlNetLoader"}, "widgets_values": ["XL\\diffusion_pytorch_model_promax.safetensors"]}, {"id": 22, "type": "SetUnionControlNetType", "pos": [360.9552001953125, 771.9780883789062], "size": [270, 58], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "ControlNet", "name": "control_net", "type": "CONTROL_NET", "link": 28}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}], "outputs": [{"localized_name": "ControlNet", "name": "CONTROL_NET", "type": "CONTROL_NET", "links": [29]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SetUnionControlNetType"}, "widgets_values": ["openpose"]}], "links": [[1, 4, 0, 3, 0, "MODEL"], [2, 5, 0, 3, 3, "LATENT"], [7, 3, 0, 8, 0, "LATENT"], [8, 4, 2, 8, 1, "VAE"], [10, 8, 0, 14, 0, "IMAGE"], [13, 11, 0, 3, 1, "CONDITIONING"], [15, 15, 0, 11, 2, "IMAGE"], [20, 4, 1, 18, 0, "CLIP"], [21, 18, 0, 11, 0, "CONDITIONING"], [22, 4, 1, 19, 0, "CLIP"], [23, 19, 0, 3, 2, "CONDITIONING"], [24, 15, 0, 20, 0, "IMAGE"], [25, 15, 0, 21, 0, "IMAGE"], [26, 21, 0, 5, 0, "INT"], [27, 21, 1, 5, 1, "INT"], [28, 13, 0, 22, 0, "CONTROL_NET"], [29, 22, 0, 11, 1, "CONTROL_NET"]], "groups": [], "config": {}, "extra": {"frontendVersion": "1.18.9", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}