{"id": "e8050e78-d921-44ad-99c4-5d0a20f0b9d0", "revision": 0, "last_node_id": 11, "last_link_id": 15, "nodes": [{"id": 1, "type": "SaveImage", "pos": [2209.14501953125, -288.2581481933594], "size": [2999.517822265625, 2066.38525390625], "flags": {"collapsed": true}, "order": 10, "mode": 0, "inputs": [{"localized_name": "图片", "name": "images", "type": "IMAGE", "link": 1}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "SaveImage", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI"]}, {"id": 2, "type": "CheckpointLoaderSimple", "pos": [620.2515258789062, -343.4947814941406], "size": [315, 98], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "Checkpoint名称", "name": "ckpt_name", "type": "COMBO", "widget": {"name": "ckpt_name"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [11]}, {"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [13]}, {"localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 2, "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "CheckpointLoaderSimple", "widget_ue_connectable": {}}, "widgets_values": ["XL\\juggernautXL_ragnarokBy.safetensors"]}, {"id": 3, "type": "UNETLoader", "pos": [620.2299194335938, -470.985595703125], "size": [270, 82], "flags": {}, "order": 1, "mode": 4, "inputs": [{"localized_name": "UNet名称", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "数据类型", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "UNETLoader", "widget_ue_connectable": {}}, "widgets_values": ["flux1-dev-fp8.safetensors", "default"]}, {"id": 4, "type": "VAEDecode", "pos": [1661.00634765625, -287.30926513671875], "size": [285.03509521484375, 46], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 2}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 3}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [1]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "VAEDecode", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 5, "type": "StoryDiffusion_KSampler", "pos": [1708.641845703125, -152.57395935058594], "size": [315, 330], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 4}, {"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 5}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 6}, {"localized_name": "info", "name": "info", "type": "DIFFINFO", "link": 7}, {"localized_name": "latent_image", "name": "latent_image", "type": "LATENT", "link": 8}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "sa32_degree", "name": "sa32_degree", "type": "FLOAT", "widget": {"name": "sa32_degree"}, "link": null}, {"localized_name": "sa64_degree", "name": "sa64_degree", "type": "FLOAT", "widget": {"name": "sa64_degree"}, "link": null}, {"localized_name": "denoise", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [2]}], "properties": {"cnr_id": "comfyui_storydiffusion", "ver": "97537e9e3ee90a67e6d114e6c1e90b5d2b0d11e5", "Node name for S&R": "StoryDiffusion_KSampler", "widget_ue_connectable": {}, "aux_id": "smthemex/ComfyUI_StoryDiffusion"}, "widgets_values": [1191831418, "randomize", 20, 8, "euler", "normal", 0.5, 0.5, 1]}, {"id": 6, "type": "EmptyLatentImage", "pos": [1733.32177734375, 283.3782958984375], "size": [315, 106], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 9}, {"localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 10}, {"localized_name": "批量大小", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}, "link": null}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "links": [8]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "EmptyLatentImage", "widget_ue_connectable": {"width": true, "height": true}}, "widgets_values": [512, 512, 1]}, {"id": 8, "type": "VAELoader", "pos": [1329.2152099609375, -474.8248596191406], "size": [270, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": [3, 12]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "VAELoader", "widget_ue_connectable": {}}, "widgets_values": ["sdxl_vae.safetensors"]}, {"id": 10, "type": "Text Multiline", "pos": [590.9220581054688, -176.65602111816406], "size": [570.720703125, 704.5016479492188], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "字符串", "name": "STRING", "type": "STRING", "links": [15]}], "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "1.0.2", "Node name for S&R": "Text Multiline", "widget_ue_connectable": {}}, "widgets_values": ["[<PERSON>] Wide Shot – A determined young girl with a backpack stands at the base of a massive ancient pyramid under a golden sunset, looking up with awe ;\n[<PERSON>] Medium Shot – She carefully steps into the dark entrance of the pyramid, holding a flickering torch, shadows dancing on the stone walls;\n[<PERSON>] Her hand brushes against ancient hieroglyphs carved into the wall, glowing faintly with an eerie light;\n[<PERSON>] Low <PERSON> – She walks down a narrow stone corridor, towering pillars on either side, dust floating in the air;\n[<PERSON>] Over-the-Shoulder Shot – She peeks around a corner, spotting a hidden chamber filled with golden treasures;\n[<PERSON>] <PERSON> Shot (Action) – Suddenly, the floor tiles shift, and she leaps back as a trap door swings open beneath her;\n[<PERSON>] Dutch <PERSON> – She balances precariously on a narrow ledge inside a deep pit, looking down into darkness;\n[<PERSON>] Medium Shot – She pulls out an old map from her backpack, studying it under torchlight;\n[<PERSON>] Extreme Close-Up – Her eyes widen as she hears a distant echoing growl from deeper inside the pyramid;\n[<PERSON>] <PERSON> (Mystery) – She enters a grand chamber with a massive sarcophagus in the center, bathed in an otherworldly glow;\n[<PERSON>]<PERSON> (Threat) – A giant stone guardian statue comes to life, its eyes glowing red as it looms over her;\n[<PERSON>] Action Shot – She sprints down a collapsing hallway, debris falling behind her;\n[<PERSON>] Close-Up (Emotion) – She clutches a golden artifact tightly, breathing heavily, realizing she must escape;\n[<PERSON>] <PERSON> (Escape) – She slides down a steep ramp just as the pyramid entrance seals shut behind her;\n[<PERSON>] inal <PERSON> – She stands outside the pyramid at dawn, holding the artifact, looking back with a mix of relief and wonder;\n[<PERSON>cun] is working.", [false, true]]}, {"id": 9, "type": "EasyFunction_Lite", "pos": [979.9843139648438, -447.9402770996094], "size": [315, 266], "flags": {}, "order": 3, "mode": 4, "inputs": [{"localized_name": "extra_repo", "name": "extra_repo", "type": "STRING", "widget": {"name": "extra_repo"}, "link": null}, {"localized_name": "checkpoints", "name": "checkpoints", "type": "COMBO", "widget": {"name": "checkpoints"}, "link": null}, {"localized_name": "clip", "name": "clip", "type": "COMBO", "widget": {"name": "clip"}, "link": null}, {"localized_name": "clip_vision", "name": "clip_vision", "type": "COMBO", "widget": {"name": "clip_vision"}, "link": null}, {"localized_name": "lora", "name": "lora", "type": "COMBO", "widget": {"name": "lora"}, "link": null}, {"localized_name": "function_mode", "name": "function_mode", "type": "COMBO", "widget": {"name": "function_mode"}, "link": null}, {"localized_name": "select_method", "name": "select_method", "type": "STRING", "widget": {"name": "select_method"}, "link": null}, {"localized_name": "temperature", "name": "temperature", "type": "FLOAT", "widget": {"name": "temperature"}, "link": null}], "outputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "links": []}, {"localized_name": "clip", "name": "clip", "type": "CLIP", "links": []}, {"localized_name": "add_function", "name": "add_function", "type": "STORY_CONDITIONING", "links": []}], "properties": {"cnr_id": "comfyui_storydiffusion", "ver": "97537e9e3ee90a67e6d114e6c1e90b5d2b0d11e5", "Node name for S&R": "EasyFunction_Lite", "widget_ue_connectable": {}, "aux_id": "smthemex/ComfyUI_StoryDiffusion"}, "widgets_values": ["H:\\ComfyUI_124_251\\ComfyUI\\models\\diffusers\\Kolors", "none", "none", "none", "none", "clip", "", 0.7]}, {"id": 7, "type": "StoryDiffusion_Apply", "pos": [1307.26123046875, -349.6278381347656], "size": [315, 218], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 11}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 12}, {"localized_name": "CLIP_VISION", "name": "CLIP_VISION", "shape": 7, "type": "CLIP_VISION", "link": null}, {"localized_name": "infer_mode", "name": "infer_mode", "type": "COMBO", "widget": {"name": "infer_mode"}, "link": null}, {"localized_name": "photomake_ckpt", "name": "photomake_ckpt", "type": "COMBO", "widget": {"name": "photomake_ckpt"}, "link": null}, {"localized_name": "ipadapter_ckpt", "name": "ipadapter_ckpt", "type": "COMBO", "widget": {"name": "ipadapter_ckpt"}, "link": null}, {"localized_name": "quantize_mode", "name": "quantize_mode", "type": "COMBO", "widget": {"name": "quantize_mode"}, "link": null}, {"localized_name": "lora_scale", "name": "lora_scale", "type": "FLOAT", "widget": {"name": "lora_scale"}, "link": null}, {"localized_name": "extra_function", "name": "extra_function", "type": "STRING", "widget": {"name": "extra_function"}, "link": null}], "outputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "slot_index": 0, "links": [4]}, {"localized_name": "switch", "name": "switch", "type": "DIFFCONDI", "links": [14]}], "properties": {"cnr_id": "comfyui_storydiffusion", "ver": "97537e9e3ee90a67e6d114e6c1e90b5d2b0d11e5", "Node name for S&R": "StoryDiffusion_Apply", "widget_ue_connectable": {}, "aux_id": "smthemex/ComfyUI_StoryDiffusion"}, "widgets_values": ["msdiffusion", "none", "ms_adapter.bin", "fp8", 0.8, ""]}, {"id": 11, "type": "StoryDiffusion_CLIPTextEncode", "pos": [1249.1466064453125, -71.57704162597656], "size": [416.2575988769531, 585.0397338867188], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 13}, {"localized_name": "switch", "name": "switch", "type": "DIFFCONDI", "link": 14}, {"localized_name": "add_function", "name": "add_function", "shape": 7, "type": "STORY_CONDITIONING", "link": null}, {"localized_name": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "control_image", "name": "control_image", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "role_text", "name": "role_text", "type": "STRING", "widget": {"name": "role_text"}, "link": null}, {"localized_name": "scene_text", "name": "scene_text", "type": "STRING", "widget": {"name": "scene_text"}, "link": 15}, {"localized_name": "pos_text", "name": "pos_text", "type": "STRING", "widget": {"name": "pos_text"}, "link": null}, {"localized_name": "neg_text", "name": "neg_text", "type": "STRING", "widget": {"name": "neg_text"}, "link": null}, {"localized_name": "lora_trigger_words", "name": "lora_trigger_words", "type": "STRING", "widget": {"name": "lora_trigger_words"}, "link": null}, {"localized_name": "add_style", "name": "add_style", "type": "COMBO", "widget": {"name": "add_style"}, "link": null}, {"localized_name": "mask_threshold", "name": "mask_threshold", "type": "FLOAT", "widget": {"name": "mask_threshold"}, "link": null}, {"localized_name": "extra_param", "name": "extra_param", "type": "STRING", "widget": {"name": "extra_param"}, "link": null}, {"localized_name": "guidance_list", "name": "guidance_list", "type": "STRING", "widget": {"name": "guidance_list"}, "link": null}], "outputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [5]}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "links": [6]}, {"localized_name": "info", "name": "info", "type": "DIFFINFO", "links": [7]}, {"localized_name": "width", "name": "width", "type": "INT", "slot_index": 3, "links": [9]}, {"localized_name": "height", "name": "height", "type": "INT", "slot_index": 4, "links": [10]}], "properties": {"cnr_id": "comfyui_storydiffusion", "ver": "97537e9e3ee90a67e6d114e6c1e90b5d2b0d11e5", "Node name for S&R": "StoryDiffusion_CLIPTextEncode", "widget_ue_connectable": {}, "aux_id": "smthemex/ComfyUI_StoryDiffusion"}, "widgets_values": [1280, 768, "[<PERSON>] (girl), wearing a white T-shirt, blue short hair.\n[<PERSON><PERSON><PERSON>] (boy),wearing a suit,black hair.", "[<PERSON>] Wide Shot – A determined young girl with a backpack stands at the base of a massive ancient pyramid under a golden sunset, looking up with awe ;\n[<PERSON>] Medium Shot – She carefully steps into the dark entrance of the pyramid, holding a flickering torch, shadows dancing on the stone walls;\n[<PERSON>] Her hand brushes against ancient hieroglyphs carved into the wall, glowing faintly with an eerie light;\n[<PERSON>] Her hand brushes against ancient hieroglyphs carved into the wall, glowing faintly with an eerie light;\n[<PERSON><PERSON><PERSON>] is working.", "best", "bad anatomy, bad hands, missing fingers, extra fingers,three hands, three legs, bad arms, missing legs, missing arms, poorly drawn face, bad face, fused face, cloned face, three crus, fused feet, fused thigh, extra crus, ugly fingers, horn,amputation, disconnected limbs", "best quality", "Japanese_Anime", 0.5, "", "0., 0.25, 0.4, 0.75;0.6, 0.25, 1., 0.75", [false, true], [false, true], [false, true], [false, true], [false, true]]}], "links": [[1, 4, 0, 1, 0, "IMAGE"], [2, 5, 0, 4, 0, "LATENT"], [3, 8, 0, 4, 1, "VAE"], [4, 7, 0, 5, 0, "MODEL"], [5, 11, 0, 5, 1, "CONDITIONING"], [6, 11, 1, 5, 2, "CONDITIONING"], [7, 11, 2, 5, 3, "DIFFINFO"], [8, 6, 0, 5, 4, "LATENT"], [9, 11, 3, 6, 0, "INT"], [10, 11, 4, 6, 1, "INT"], [11, 2, 0, 7, 0, "MODEL"], [12, 8, 0, 7, 1, "VAE"], [13, 2, 1, 11, 0, "CLIP"], [14, 7, 1, 11, 1, "DIFFCONDI"], [15, 10, 0, 11, 8, "STRING"]], "groups": [{"id": 1, "title": "story_text2img", "bounding": [535.3018798828125, -496.6520080566406, 1599.7913818359375, 1022.123046875], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ue_links": []}, "version": 0.4}