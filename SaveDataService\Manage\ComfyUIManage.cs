using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Net.Http;
using Newtonsoft.Json;
using SaveDataService;
using GameServer.ORM;
using ExcelToData;

namespace SaveDataService.Manage
{
    /// <summary>
    /// ComfyUI管理类 - 管理多台ComfyUI服务器和任务
    /// </summary>
    public class ComfyUIManage : RESTfulAPIBase
    {
        private static ComfyUIManage _instance;
        private static readonly object _lock = new object();
        private readonly HttpClient _httpClient;

        /// <summary>
        /// 单例实例
        /// </summary>
        public static ComfyUIManage Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new ComfyUIManage();
                        }
                    }
                }
                return _instance;
            }
        }

        private ComfyUIManage()
        {
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromMinutes(5);
        }

        /// <summary>
        /// 添加ComfyUI服务器
        /// </summary>
        /// <param name="serverName">服务器名称</param>
        /// <param name="serverUrl">服务器地址</param>
        /// <param name="port">端口</param>
        /// <param name="maxConcurrentTasks">最大并发任务数</param>
        /// <param name="description">描述</param>
        /// <returns>服务器ID</returns>
        public string AddServer(string serverName, string serverUrl, int port, int maxConcurrentTasks = 5, string description = "")
        {
            try
            {
                var serverId = Guid.NewGuid().ToString();
                var server = new ComfyUIServer
                {
                    id = serverId,
                    serverName = serverName,
                    serverUrl = serverUrl,
                    port = port,
                    status = 0, // 离线
                    maxConcurrentTasks = maxConcurrentTasks,
                    currentTasks = 0,
                    supportedWorkflows = "[]",
                    description = description,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                };

                var db = ORMTables.Instance;
                db.ComfyUIServers.Add(server);
                db.SaveChanges();

                Console.WriteLine($"成功添加ComfyUI服务器: {serverName} ({serverUrl}:{port})");
                return serverId;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"添加服务器失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex.StackTrace}");
                return "";
            }
        }

        /// <summary>
        /// 删除ComfyUI服务器
        /// </summary>
        /// <param name="serverId">服务器ID</param>
        /// <returns>是否成功</returns>
        public bool RemoveServer(string serverId)
        {
            try
            {
                var db = ORMTables.Instance;
                var server = db.ComfyUIServers.FirstOrDefault(s => s.id == serverId);
                if (server != null)
                {
                    db.ComfyUIServers.Remove(server);
                    db.SaveChanges();
                    Console.WriteLine($"成功删除服务器: {serverId}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除服务器失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取所有服务器列表
        /// </summary>
        /// <returns>服务器列表</returns>
        public List<ComfyUIServer> GetAllServers()
        {
            try
            {
                var db = ORMTables.Instance;
                return db.ComfyUIServers.ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取服务器列表失败: {ex.Message}");
                return new List<ComfyUIServer>();
            }
        }

        /// <summary>
        /// 获取在线服务器列表
        /// </summary>
        /// <returns>在线服务器列表</returns>
        public List<ComfyUIServer> GetOnlineServers()
        {
            try
            {
                var db = ORMTables.Instance;
                return db.ComfyUIServers.Where(s => s.status == 1).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取在线服务器列表失败: {ex.Message}");
                return new List<ComfyUIServer>();
            }
        }

        /// <summary>
        /// 更新服务器信息
        /// </summary>
        /// <param name="serverId">服务器ID</param>
        /// <param name="serverName">服务器名称</param>
        /// <param name="host">主机地址</param>
        /// <param name="port">端口</param>
        /// <param name="maxConcurrentTasks">最大并发任务数</param>
        /// <param name="description">描述</param>
        /// <returns>是否成功</returns>
        public bool UpdateServer(string serverId, string serverName, string host, int port, int maxConcurrentTasks, string description = "")
        {
            try
            {
                var db = ORMTables.Instance;
                var server = db.ComfyUIServers.FirstOrDefault(s => s.id == serverId);
                if (server != null)
                {
                    server.serverName = serverName;
                    server.serverUrl = host;
                    server.port = port;
                    server.maxConcurrentTasks = maxConcurrentTasks;
                    server.description = description;
                    server.UpdateTime = DateTime.Now;

                    db.SaveChanges();
                    Console.WriteLine($"成功更新服务器: {serverName}");
                    return true;
                }
                else
                {
                    Console.WriteLine($"服务器不存在: {serverId}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新服务器失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 更新服务器状态
        /// </summary>
        /// <param name="serverId">服务器ID</param>
        /// <param name="status">状态 (0:离线, 1:在线, 2:忙碌, 3:维护)</param>
        /// <param name="currentTasks">当前任务数</param>
        /// <returns>是否成功</returns>
        public bool UpdateServerStatus(string serverId, int status, int currentTasks = -1)
        {
            try
            {
                var db = ORMTables.Instance;
                var server = db.ComfyUIServers.FirstOrDefault(s => s.id == serverId);
                if (server != null)
                {
                    server.status = status;
                    if (currentTasks >= 0)
                    {
                        server.currentTasks = currentTasks;
                    }
                    server.lastHeartbeat = DateTime.Now;
                    server.UpdateTime = DateTime.Now;
                    db.SaveChanges();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新服务器状态失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 添加工作流配置
        /// </summary>
        /// <param name="workflowName">工作流名称</param>
        /// <param name="workflowJson">工作流JSON配置</param>
        /// <param name="workflowType">工作流类型</param>
        /// <param name="description">描述</param>
        /// <param name="creator">创建者</param>
        /// <returns>工作流ID</returns>
        public string AddWorkflow(string workflowName, string workflowJson, string workflowType, string description = "", string creator = "")
        {
            try
            {
                var workflowId = Guid.NewGuid().ToString();
                var workflow = new ComfyUIWorkflow
                {
                    id = workflowId,
                    workflowName = workflowName,
                    workflowJson = workflowJson,
                    workflowType = workflowType,
                    description = description,
                    workflowVersion = "1.0",
                    creator = creator,
                    isEnabled = true,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                };

                var db = ORMTables.Instance;
                db.ComfyUIWorkflows.Add(workflow);
                db.SaveChanges();

                Console.WriteLine($"成功添加工作流: {workflowName}");
                return workflowId;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"添加工作流失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex.StackTrace}");
                return "";
            }
        }

        /// <summary>
        /// 更新工作流信息
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="workflowName">工作流名称</param>
        /// <param name="workflowJson">工作流JSON配置</param>
        /// <param name="creator">创建者</param>
        /// <param name="workflowType">工作流类型</param>
        /// <param name="description">描述</param>
        /// <returns>是否成功</returns>
        public bool UpdateWorkflow(string workflowId, string workflowName, string workflowJson, string creator, string workflowType, string description = "")
        {
            try
            {
                var db = ORMTables.Instance;
                var workflow = db.ComfyUIWorkflows.FirstOrDefault(w => w.id == workflowId);
                if (workflow != null)
                {
                    workflow.workflowName = workflowName;
                    workflow.workflowJson = workflowJson;
                    workflow.workflowType = workflowType;
                    workflow.description = description;
                    workflow.creator = creator;
                    workflow.UpdateTime = DateTime.Now;

                    db.SaveChanges();
                    Console.WriteLine($"成功更新工作流: {workflowName}");
                    return true;
                }
                else
                {
                    Console.WriteLine($"工作流不存在: {workflowId}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新工作流失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取所有工作流
        /// </summary>
        /// <returns>工作流列表</returns>
        public List<ComfyUIWorkflow> GetAllWorkflows()
        {
            try
            {
                var db = ORMTables.Instance;
                return db.ComfyUIWorkflows.Where(w => w.isEnabled).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取工作流列表失败: {ex.Message}");
                return new List<ComfyUIWorkflow>();
            }
        }

        /// <summary>
        /// 根据类型获取工作流
        /// </summary>
        /// <param name="workflowType">工作流类型</param>
        /// <returns>工作流列表</returns>
        public List<ComfyUIWorkflow> GetWorkflowsByType(string workflowType)
        {
            try
            {
                var db = ORMTables.Instance;
                return db.ComfyUIWorkflows.Where(w => w.isEnabled && w.workflowType == workflowType).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取工作流列表失败: {ex.Message}");
                return new List<ComfyUIWorkflow>();
            }
        }

        /// <summary>
        /// 创建新任务
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="taskName">任务名称</param>
        /// <param name="creator">创建者</param>
        /// <param name="priority">优先级</param>
        /// <returns>任务ID</returns>
        public string CreateTask(string workflowId, string taskName, string creator = "", int priority = 5)
        {
            try
            {
                // 首先检查是否有服务器
                var allServers = GetAllServers();
                Console.WriteLine($"数据库中共有 {allServers.Count} 个服务器");

                // 选择最佳服务器
                var serverId = SelectBestServer();
                if (string.IsNullOrEmpty(serverId))
                {
                    // 如果没有在线服务器，使用第一个服务器
                    if (allServers.Count > 0)
                    {
                        serverId = allServers[0].id;
                        Console.WriteLine($"没有在线服务器，使用第一个服务器: {serverId}");
                    }
                    else
                    {
                        Console.WriteLine("没有可用的服务器");
                        return "";
                    }
                }

                var taskId = Guid.NewGuid().ToString();
                var task = new ComfyUITask
                {
                    id = taskId,
                    serverId = serverId,
                    workflowId = workflowId,
                    taskName = taskName,
                    status = 0, // 等待
                    progress = 0,
                    queuePosition = GetNextQueuePosition(),
                    priority = priority,
                    creator = creator,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                };

                var db = ORMTables.Instance;
                db.ComfyUITasks.Add(task);
                db.SaveChanges();

                Console.WriteLine($"成功创建任务: {taskName} (ID: {taskId})");
                return taskId;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建任务失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex.StackTrace}");
                return "";
            }
        }

        /// <summary>
        /// 选择最佳服务器
        /// </summary>
        /// <returns>服务器ID</returns>
        private string SelectBestServer()
        {
            try
            {
                var db = ORMTables.Instance;
                var availableServers = db.ComfyUIServers
                    .Where(s => s.status == 1 && s.currentTasks < s.maxConcurrentTasks)
                    .OrderBy(s => s.currentTasks)
                    .ToList();

                return availableServers.FirstOrDefault()?.id ?? "";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"选择服务器失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 获取下一个队列位置
        /// </summary>
        /// <returns>队列位置</returns>
        private int GetNextQueuePosition()
        {
            try
            {
                var db = ORMTables.Instance;
                var maxPosition = db.ComfyUITasks
                    .Where(t => t.status == 0) // 等待状态
                    .Max(t => (int?)t.queuePosition) ?? 0;
                return maxPosition + 1;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取队列位置失败: {ex.Message}");
                return 1;
            }
        }

        /// <summary>
        /// 获取任务列表
        /// </summary>
        /// <param name="status">状态过滤 (-1:全部, 0:等待, 1:运行中, 2:完成, 3:失败, 4:取消)</param>
        /// <param name="serverId">服务器ID过滤</param>
        /// <returns>任务列表</returns>
        public List<ComfyUITask> GetTasks(int status = -1, string serverId = "")
        {
            try
            {
                var db = ORMTables.Instance;
                var query = db.ComfyUITasks.AsQueryable();

                if (status >= 0)
                {
                    query = query.Where(t => t.status == status);
                }

                if (!string.IsNullOrEmpty(serverId))
                {
                    query = query.Where(t => t.serverId == serverId);
                }

                return query.OrderBy(t => t.CreateTime).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取任务列表失败: {ex.Message}");
                return new List<ComfyUITask>();
            }
        }

        /// <summary>
        /// 根据ID获取服务器
        /// </summary>
        /// <param name="serverId">服务器ID</param>
        /// <returns>服务器信息</returns>
        public ComfyUIServer GetServerById(string serverId)
        {
            try
            {
                var db = ORMTables.Instance;
                return db.ComfyUIServers.FirstOrDefault(s => s.id == serverId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取服务器失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 根据ID获取工作流
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>工作流信息</returns>
        public ComfyUIWorkflow GetWorkflowById(string workflowId)
        {
            try
            {
                var db = ORMTables.Instance;
                return db.ComfyUIWorkflows.FirstOrDefault(w => w.id == workflowId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取工作流失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 根据ID获取任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务信息</returns>
        public ComfyUITask GetTaskById(string taskId)
        {
            try
            {
                var db = ORMTables.Instance;
                return db.ComfyUITasks.FirstOrDefault(t => t.id == taskId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取任务失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 根据状态获取任务列表
        /// </summary>
        /// <param name="status">任务状态</param>
        /// <returns>任务列表</returns>
        public List<ComfyUITask> GetTasksByStatus(int status)
        {
            try
            {
                var db = ORMTables.Instance;
                return db.ComfyUITasks.Where(t => t.status == status).OrderBy(t => t.CreateTime).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"根据状态获取任务失败: {ex.Message}");
                return new List<ComfyUITask>();
            }
        }



        /// <summary>
        /// 根据工作流ID获取任务列表
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>任务列表</returns>
        public List<ComfyUITask> GetTasksByWorkflowId(string workflowId)
        {
            try
            {
                var db = ORMTables.Instance;
                return db.ComfyUITasks.Where(t => t.workflowId == workflowId).OrderBy(t => t.CreateTime).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"根据工作流ID获取任务失败: {ex.Message}");
                return new List<ComfyUITask>();
            }
        }

        /// <summary>
        /// 获取队列中的任务
        /// </summary>
        /// <returns>队列任务列表</returns>
        public List<ComfyUITask> GetQueueTasks()
        {
            return GetTasks(0).OrderBy(t => t.queuePosition).ToList();
        }

        /// <summary>
        /// 获取正在运行的任务
        /// </summary>
        /// <returns>运行中任务列表</returns>
        public List<ComfyUITask> GetRunningTasks()
        {
            return GetTasks(1);
        }

        /// <summary>
        /// 更新任务状态
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="status">新状态</param>
        /// <param name="progress">进度</param>
        /// <param name="currentNode">当前节点</param>
        /// <param name="currentNodeName">当前节点名称</param>
        /// <param name="errorMessage">错误信息</param>
        /// <returns>是否成功</returns>
        public bool UpdateTaskStatus(string taskId, int status, int progress = -1, string currentNode = "", string currentNodeName = "", string errorMessage = "")
        {
            try
            {
                var db = ORMTables.Instance;
                var task = db.ComfyUITasks.FirstOrDefault(t => t.id == taskId);
                if (task != null)
                {
                    task.status = status;
                    if (progress >= 0)
                    {
                        task.progress = progress;
                    }
                    if (!string.IsNullOrEmpty(currentNode))
                    {
                        task.currentNode = currentNode;
                    }
                    if (!string.IsNullOrEmpty(currentNodeName))
                    {
                        task.currentNodeName = currentNodeName;
                    }
                    if (!string.IsNullOrEmpty(errorMessage))
                    {
                        task.errorMessage = errorMessage;
                    }

                    // 设置开始和结束时间
                    if (status == 1 && task.startTime == null) // 开始运行
                    {
                        task.startTime = DateTime.Now;
                    }
                    else if ((status == 2 || status == 3 || status == 4) && task.endTime == null) // 完成/失败/取消
                    {
                        task.endTime = DateTime.Now;
                    }
                         
                    task.UpdateTime = DateTime.Now;
                    db.SaveChanges();

                    // 记录日志
                    AddTaskLog(taskId, 1, $"任务状态更新: {GetStatusName(status)}", currentNode, currentNodeName);

                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新任务状态失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 添加任务日志
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="logLevel">日志级别</param>
        /// <param name="message">消息</param>
        /// <param name="nodeId">节点ID</param>
        /// <param name="nodeName">节点名称</param>
        /// <param name="details">详细信息</param>
        /// <returns>是否成功</returns>
        public bool AddTaskLog(string taskId, int logLevel, string message, string nodeId = "", string nodeName = "", string details = "")
        {
            try
            {
                var db = ORMTables.Instance;
                var task = db.ComfyUITasks.FirstOrDefault(t => t.id == taskId);
                if (task == null)
                {
                    Console.WriteLine($"任务不存在: {taskId}");
                    return false;
                }

                var logEntry = new
                {
                    id = Guid.NewGuid().ToString(),
                    logLevel = logLevel,
                    message = message,
                    nodeId = nodeId,
                    nodeName = nodeName,
                    details = details,
                    createTime = DateTime.Now
                };

                var logs = new List<object>();
                if (!string.IsNullOrEmpty(task.Logs))
                {
                    try
                    {
                        logs = System.Text.Json.JsonSerializer.Deserialize<List<object>>(task.Logs) ?? new List<object>();
                    }
                    catch
                    {
                        logs = new List<object>();
                    }
                }

                logs.Add(logEntry);
                task.Logs = System.Text.Json.JsonSerializer.Serialize(logs);
                task.UpdateTime = DateTime.Now;

                db.SaveChanges();

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"添加任务日志失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取任务日志
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>日志JSON字符串</returns>
        public string GetTaskLogs(string taskId)
        {
            try
            {
                var db = ORMTables.Instance;
                var task = db.ComfyUITasks.FirstOrDefault(t => t.id == taskId);
                return task?.Logs ?? "[]";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取任务日志失败: {ex.Message}");
                return "[]";
            }
        }

        /// <summary>
        /// 添加任务文件
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="fileType">文件类型</param>
        /// <param name="fileName">文件名</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="fileSize">文件大小</param>
        /// <param name="fileMd5">文件MD5</param>
        /// <param name="description">描述</param>
        /// <returns>文件ID</returns>
        public string AddTaskFile(string taskId, int fileType, string fileName, string filePath, long fileSize = 0, string fileMd5 = "", string description = "")
        {
            try
            {
                var fileId = Guid.NewGuid().ToString();

                var db = ORMTables.Instance;
                var task = db.ComfyUITasks.FirstOrDefault(t => t.id == taskId);
                if (task == null)
                {
                    Console.WriteLine($"任务不存在: {taskId}");
                    return "";
                }

                var fileEntry = new
                {
                    id = fileId,
                    fileType = fileType,
                    fileName = fileName,
                    filePath = filePath,
                    fileSize = fileSize,
                    fileMd5 = fileMd5,
                    description = description,
                    createTime = DateTime.Now
                };

                var files = new List<object>();
                if (!string.IsNullOrEmpty(task.Files))
                {
                    try
                    {
                        files = System.Text.Json.JsonSerializer.Deserialize<List<object>>(task.Files) ?? new List<object>();
                    }
                    catch
                    {
                        files = new List<object>();
                    }
                }

                files.Add(fileEntry);
                task.Files = System.Text.Json.JsonSerializer.Serialize(files);
                task.UpdateTime = DateTime.Now;

                db.SaveChanges();

                Console.WriteLine($"成功添加任务文件: {fileName}");
                return fileId;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"添加任务文件失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 获取任务文件
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="fileType">文件类型过滤 (-1:全部)</param>
        /// <returns>文件JSON字符串</returns>
        public string GetTaskFiles(string taskId, int fileType = -1)
        {
            try
            {
                var db = ORMTables.Instance;
                var task = db.ComfyUITasks.FirstOrDefault(t => t.id == taskId);
                if (task == null || string.IsNullOrEmpty(task.Files))
                {
                    return "[]";
                }

                if (fileType >= 0)
                {
                    try
                    {
                        var allFiles = System.Text.Json.JsonSerializer.Deserialize<List<System.Text.Json.JsonElement>>(task.Files);
                        var filteredFiles = allFiles?.Where(f =>
                            f.TryGetProperty("fileType", out var ft) && ft.GetInt32() == fileType).ToList();
                        return System.Text.Json.JsonSerializer.Serialize(filteredFiles ?? new List<System.Text.Json.JsonElement>());
                    }
                    catch
                    {
                        return "[]";
                    }
                }

                return task.Files;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取任务文件失败: {ex.Message}");
                return "[]";
            }
        }

        /// <summary>
        /// 获取状态名称
        /// </summary>
        /// <param name="status">状态码</param>
        /// <returns>状态名称</returns>
        private string GetStatusName(int status)
        {
            return status switch
            {
                0 => "等待",
                1 => "运行中",
                2 => "完成",
                3 => "失败",
                4 => "取消",
                _ => "未知"
            };
        }

        /// <summary>
        /// 取消任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>是否成功</returns>
        public bool CancelTask(string taskId)
        {
            return UpdateTaskStatus(taskId, 4, errorMessage: "用户取消任务");
        }

        /// <summary>
        /// 删除任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>是否成功</returns>
        public bool DeleteTask(string taskId)
        {
            try
            {
                var db = ORMTables.Instance;
                var task = db.ComfyUITasks.FirstOrDefault(t => t.id == taskId);
                if (task != null)
                {
                    db.ComfyUITasks.Remove(task);
                    db.SaveChanges();
                    Console.WriteLine($"成功删除任务: {taskId}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除任务失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 删除工作流
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>是否成功</returns>
        public bool DeleteWorkflow(string workflowId)
        {
            try
            {
                var db = ORMTables.Instance;
                var workflow = db.ComfyUIWorkflows.FirstOrDefault(w => w.id == workflowId);
                if (workflow != null)
                {
                    db.ComfyUIWorkflows.Remove(workflow);
                    db.SaveChanges();
                    Console.WriteLine($"成功删除工作流: {workflowId}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除工作流失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取服务器统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public object GetServerStatistics()
        {
            try
            {
                var db = ORMTables.Instance;
                var servers = db.ComfyUIServers.ToList();
                var tasks = db.ComfyUITasks.ToList();

                return new
                {
                    TotalServers = servers.Count,
                    OnlineServers = servers.Count(s => s.status == 1),
                    BusyServers = servers.Count(s => s.status == 2),
                    OfflineServers = servers.Count(s => s.status == 0),
                    TotalTasks = tasks.Count,
                    WaitingTasks = tasks.Count(t => t.status == 0),
                    RunningTasks = tasks.Count(t => t.status == 1),
                    CompletedTasks = tasks.Count(t => t.status == 2),
                    FailedTasks = tasks.Count(t => t.status == 3),
                    CancelledTasks = tasks.Count(t => t.status == 4)
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取统计信息失败: {ex.Message}");
                return new { Error = ex.Message };
            }
        }

        /// <summary>
        /// 测试服务器连接
        /// </summary>
        /// <param name="serverId">服务器ID</param>
        /// <returns>是否连接成功</returns>
        public async Task<bool> TestServerConnection(string serverId)
        {
            try
            {
                var db = ORMTables.Instance;
                var server = db.ComfyUIServers.FirstOrDefault(s => s.id == serverId);
                if (server == null)
                {
                    Console.WriteLine($"服务器不存在: {serverId}");
                    return false;
                }

                var url = $"http://{server.serverUrl}:{server.port}/";
                Console.WriteLine($"测试连接到: {url}");

                try
                {
                    var response = await _httpClient.GetAsync(url);
                    var isSuccess = response.IsSuccessStatusCode;

                    // 更新服务器状态
                    UpdateServerStatus(serverId, isSuccess ? 1 : 0);

                    Console.WriteLine($"服务器连接测试 {(isSuccess ? "成功" : "失败")}: {server.serverName}");
                    return isSuccess;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"连接服务器失败: {ex.Message}");
                    UpdateServerStatus(serverId, 0); // 离线
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试服务器连接失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试所有服务器连接
        /// </summary>
        /// <returns>在线服务器数量</returns>
        public async Task<int> TestAllServerConnections()
        {
            try
            {
                var servers = GetAllServers();
                int onlineCount = 0;

                Console.WriteLine($"开始测试 {servers.Count} 台服务器的连接...");

                foreach (var server in servers)
                {
                    var isOnline = await TestServerConnection(server.id);
                    if (isOnline)
                    {
                        onlineCount++;
                    }
                }

                Console.WriteLine($"连接测试完成，{onlineCount}/{servers.Count} 台服务器在线");
                return onlineCount;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试所有服务器连接失败: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 向ComfyUI服务器发送工作流（带详细日志监控）
        /// </summary>
        /// <param name="serverId">服务器ID</param>
        /// <param name="workflowJson">工作流JSON</param>
        /// <returns>任务ID和提交结果</returns>
        public async Task<(string taskId, string result)> SubmitWorkflowToServerWithMonitoring(string serverId, string workflowJson)
        {
            var logger = ComfyUILogger.Instance;
            var monitor = ComfyUIMonitor.Instance;
            var taskId = Guid.NewGuid().ToString();

            try
            {
                var db = ORMTables.Instance;
                var server = db.ComfyUIServers.FirstOrDefault(s => s.id == serverId);
                if (server == null)
                {
                    logger.LogError($"服务器不存在: {serverId}");
                    return (taskId, "服务器不存在");
                }

                var serverUrl = $"http://{server.serverUrl}:{server.port}";
                var url = $"{serverUrl}/prompt";

                // 记录工作流开始
                logger.LogWorkflowStart(taskId, workflowJson, serverId);

                var requestData = new
                {
                    prompt = JsonConvert.DeserializeObject(workflowJson),
                    client_id = Guid.NewGuid().ToString()
                };

                var json = JsonConvert.SerializeObject(requestData);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

                logger.LogInfo($"向服务器提交工作流: {server.serverName} ({serverUrl})");

                var response = await _httpClient.PostAsync(url, content);
                var responseText = await response.Content.ReadAsStringAsync();

                // 记录提交结果
                logger.LogWorkflowSubmission(taskId, response.IsSuccessStatusCode, responseText);

                if (response.IsSuccessStatusCode)
                {
                    // 解析响应获取prompt_id并启动监控
                    try
                    {
                        var responseObj = JsonConvert.DeserializeObject<dynamic>(responseText);
                        if (responseObj.prompt_id != null)
                        {
                            var promptId = responseObj.prompt_id.ToString();
                            logger.LogInfo($"获得Prompt ID: {promptId}，启动详细监控...");

                            // 启动监控（不等待完成）
                            _ = Task.Run(() => monitor.StartMonitoring(taskId, promptId, serverUrl, workflowJson));
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogWarning($"启动监控失败: {ex.Message}");
                    }

                    return (taskId, responseText);
                }
                else
                {
                    logger.LogError($"工作流提交失败: {response.StatusCode} - {responseText}");
                    return (taskId, $"提交失败: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                logger.LogError($"提交工作流到服务器失败: {ex.Message}");
                return (taskId, $"提交失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 向ComfyUI服务器发送工作流（原有方法，保持兼容性）
        /// </summary>
        /// <param name="serverId">服务器ID</param>
        /// <param name="workflowJson">工作流JSON</param>
        /// <returns>是否成功</returns>
        public async Task<string> SubmitWorkflowToServer(string serverId, string workflowJson)
        {
            var result = await SubmitWorkflowToServerWithMonitoring(serverId, workflowJson);
            return result.result;
        }
    }
}
