{"id": "206247b6-9fec-4ed2-8927-e4f388c674d4", "revision": 0, "last_node_id": 139, "last_link_id": 216, "nodes": [{"id": 111, "type": "WanVideoTorchCompileSettings", "pos": [-1303.0140380859375, -1115.121826171875], "size": [421.6000061035156, 203.9819793701172], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "backend", "name": "backend", "type": "COMBO", "widget": {"name": "backend"}, "link": null}, {"localized_name": "fullgraph", "name": "fullgraph", "type": "BOOLEAN", "widget": {"name": "fullgraph"}, "link": null}, {"localized_name": "mode", "name": "mode", "type": "COMBO", "widget": {"name": "mode"}, "link": null}, {"localized_name": "dynamic", "name": "dynamic", "type": "BOOLEAN", "widget": {"name": "dynamic"}, "link": null}, {"localized_name": "dynamo_cache_size_limit", "name": "dynamo_cache_size_limit", "type": "INT", "widget": {"name": "dynamo_cache_size_limit"}, "link": null}, {"localized_name": "compile_transformer_blocks_only", "name": "compile_transformer_blocks_only", "type": "BOOLEAN", "widget": {"name": "compile_transformer_blocks_only"}, "link": null}, {"localized_name": "dynamo_recompile_limit", "name": "dynamo_recompile_limit", "shape": 7, "type": "INT", "widget": {"name": "dynamo_recompile_limit"}, "link": null}], "outputs": [{"label": "torch_compile_args", "localized_name": "torch_compile_args", "name": "torch_compile_args", "type": "WANCOMPILEARGS", "slot_index": 0, "links": [165]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoTorchCompileSettings"}, "widgets_values": ["inductor", false, "default", false, 64, true, 128], "color": "#223", "bgcolor": "#335"}, {"id": 113, "type": "WanVideoBlockSwap", "pos": [-1217.8199462890625, -852.3065795898438], "size": [315, 154], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "blocks_to_swap", "name": "blocks_to_swap", "type": "INT", "widget": {"name": "blocks_to_swap"}, "link": null}, {"localized_name": "offload_img_emb", "name": "offload_img_emb", "type": "BOOLEAN", "widget": {"name": "offload_img_emb"}, "link": null}, {"localized_name": "offload_txt_emb", "name": "offload_txt_emb", "type": "BOOLEAN", "widget": {"name": "offload_txt_emb"}, "link": null}, {"localized_name": "use_non_blocking", "name": "use_non_blocking", "shape": 7, "type": "BOOLEAN", "widget": {"name": "use_non_blocking"}, "link": null}, {"localized_name": "vace_blocks_to_swap", "name": "vace_blocks_to_swap", "shape": 7, "type": "INT", "widget": {"name": "vace_blocks_to_swap"}, "link": null}], "outputs": [{"label": "block_swap_args", "localized_name": "block_swap_args", "name": "block_swap_args", "type": "BLOCKSWAPARGS", "slot_index": 0, "links": [166]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoBlockSwap"}, "widgets_values": [35, false, false, true, 0], "color": "#223", "bgcolor": "#335"}, {"id": 81, "type": "WanVideoEmptyEmbeds", "pos": [39.9141731262207, -353.2147216796875], "size": [228.39999389648438, 106], "flags": {}, "order": 32, "mode": 0, "inputs": [{"label": "control_embeds", "localized_name": "control_embeds", "name": "control_embeds", "shape": 7, "type": "WANVIDIMAGE_EMBEDS"}, {"label": "width", "localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 141}, {"label": "height", "localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 142}, {"label": "num_frames", "localized_name": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": 143}], "outputs": [{"label": "image_embeds", "localized_name": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "links": [175]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "e3ea2bf392213d16c2ba56f7584354e819b72575", "Node name for S&R": "WanVideoEmptyEmbeds"}, "widgets_values": [832, 480, 69]}, {"id": 86, "type": "SetNode", "pos": [-519.72607421875, -705.3973999023438], "size": [210, 60], "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "WANVIDEOMODEL", "type": "WANVIDEOMODEL", "link": 167}], "outputs": [{"label": "输出", "name": "*", "type": "*"}], "title": "Set_WANVIDEOMODEL", "properties": {"previousName": "wan_model"}, "widgets_values": ["wan_model"]}, {"id": 85, "type": "WanVideoSLG", "pos": [386.0672912597656, -324.5872802734375], "size": [315, 106], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "blocks", "name": "blocks", "type": "STRING", "widget": {"name": "blocks"}, "link": null}, {"localized_name": "start_percent", "name": "start_percent", "type": "FLOAT", "widget": {"name": "start_percent"}, "link": null}, {"localized_name": "end_percent", "name": "end_percent", "type": "FLOAT", "widget": {"name": "end_percent"}, "link": null}], "outputs": [{"label": "slg_args", "localized_name": "slg_args", "name": "slg_args", "type": "SLGARGS", "links": [130]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "6099ad393b071728032fd481e96d77d2900eee2c", "Node name for S&R": "WanVideoSLG"}, "widgets_values": ["10", 0.1, 1]}, {"id": 118, "type": "easy clearCacheAll", "pos": [97.52848052978516, -1104.237060546875], "size": [210, 26], "flags": {}, "order": 26, "mode": 0, "inputs": [{"label": "输入任何", "localized_name": "输入任何", "name": "anything", "type": "*", "link": 177}], "outputs": [{"localized_name": "output", "name": "output", "type": "*", "links": [178]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy clearCacheAll"}, "widgets_values": [], "color": "#322", "bgcolor": "#533"}, {"id": 89, "type": "GetNode", "pos": [-187.14471435546875, -1289.8699951171875], "size": [210, 60], "flags": {"collapsed": false}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "WANVIDEOMODEL", "type": "WANVIDEOMODEL", "links": [170, 179]}], "title": "Get_wan_model", "properties": {}, "widgets_values": ["wan_model"], "color": "#223", "bgcolor": "#335"}, {"id": 119, "type": "easy clearCacheAll", "pos": [-186.3602294921875, -1182.740478515625], "size": [210, 26], "flags": {}, "order": 25, "mode": 0, "inputs": [{"label": "输入任何", "localized_name": "输入任何", "name": "anything", "type": "*", "link": 179}], "outputs": [{"localized_name": "output", "name": "output", "type": "*", "links": [180]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy clearCacheAll"}, "widgets_values": [], "color": "#322", "bgcolor": "#533"}, {"id": 91, "type": "GetNode", "pos": [-184.4212646484375, -1103.847412109375], "size": [210, 58], "flags": {"collapsed": false}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "WANTEXTENCODER", "type": "WANTEXTENCODER", "links": [177]}], "title": "Get_txt_encoder", "properties": {}, "widgets_values": ["txt_encoder"], "color": "#232", "bgcolor": "#353"}, {"id": 115, "type": "easy clearCacheAll", "pos": [382.95361328125, -1107.643310546875], "size": [210, 26], "flags": {}, "order": 39, "mode": 0, "inputs": [{"label": "输入任何", "localized_name": "输入任何", "name": "anything", "type": "*", "link": 173}], "outputs": [{"localized_name": "output", "name": "output", "type": "*", "links": [174]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy clearCacheAll"}, "widgets_values": [], "color": "#322", "bgcolor": "#533"}, {"id": 116, "type": "easy clearCacheAll", "pos": [384.48956298828125, -428.7554931640625], "size": [210, 26], "flags": {}, "order": 37, "mode": 0, "inputs": [{"label": "输入任何", "localized_name": "输入任何", "name": "anything", "type": "*", "link": 175}], "outputs": [{"localized_name": "output", "name": "output", "type": "*", "links": [176]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy clearCacheAll"}, "widgets_values": [], "color": "#322", "bgcolor": "#533"}, {"id": 45, "type": "WanVideoVRAMManagement", "pos": [-1216.1455078125, -645.9152221679688], "size": [315, 58], "flags": {}, "order": 5, "mode": 4, "inputs": [{"localized_name": "offload_percent", "name": "offload_percent", "type": "FLOAT", "widget": {"name": "offload_percent"}, "link": null}], "outputs": [{"label": "vram_management_args", "localized_name": "vram_management_args", "name": "vram_management_args", "type": "VRAM_MANAGEMENTARGS", "links": []}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoVRAMManagement"}, "widgets_values": [1], "color": "#223", "bgcolor": "#335"}, {"id": 42, "type": "Note", "pos": [-1699.264404296875, -872.96044921875], "size": [314.96246337890625, 152.77333068847656], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["另一种方案是采用DiffSynt-Studios引入的显存管理技术。这种方式通常速度较慢，但与块交换(BlockSwap)相比能节省更多显存。"], "color": "#432", "bgcolor": "#653"}, {"id": 44, "type": "Note", "pos": [-1699.06591796875, -1081.4583740234375], "size": [303.0501403808594, 88], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["若已安装 Triton，启用该功能可提升约 30% 的运行速度。"], "color": "#432", "bgcolor": "#653"}, {"id": 33, "type": "Note", "pos": [-1296.9434814453125, -1268.2232666015625], "size": [359.0753479003906, 88], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["模型下载链接（需魔法）：\nhttps://huggingface.co/Kijai/WanVideo_comfy/tree/main"], "color": "#432", "bgcolor": "#653"}, {"id": 36, "type": "Note", "pos": [-752.5341796875, -1238.614013671875], "size": [374.3061828613281, 171.9547576904297], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["fp8_fast 似乎会导致质量大幅下降\n\nfp16_fast 启用了最新 PyTorch Nightly 版本中的 \"全 FP16 累加（FP16 GEMM）\" 功能，可带来约 20% 的速度提升\n\n若已安装 SageAttn，使用后推理速度几乎可翻倍"], "color": "#432", "bgcolor": "#653"}, {"id": 28, "type": "WanVideoDecode", "pos": [1245.44189453125, -1262.2374267578125], "size": [315, 174], "flags": {}, "order": 41, "mode": 0, "inputs": [{"label": "vae", "localized_name": "vae", "name": "vae", "type": "WANVAE", "link": 136}, {"label": "samples", "localized_name": "samples", "name": "samples", "type": "LATENT", "link": 128}, {"localized_name": "enable_vae_tiling", "name": "enable_vae_tiling", "type": "BOOLEAN", "widget": {"name": "enable_vae_tiling"}, "link": null}, {"localized_name": "tile_x", "name": "tile_x", "type": "INT", "widget": {"name": "tile_x"}, "link": null}, {"localized_name": "tile_y", "name": "tile_y", "type": "INT", "widget": {"name": "tile_y"}, "link": null}, {"localized_name": "tile_stride_x", "name": "tile_stride_x", "type": "INT", "widget": {"name": "tile_stride_x"}, "link": null}, {"localized_name": "tile_stride_y", "name": "tile_stride_y", "type": "INT", "widget": {"name": "tile_stride_y"}, "link": null}], "outputs": [{"label": "images", "localized_name": "images", "name": "images", "type": "IMAGE", "slot_index": 0, "links": [36, 184]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoDecode"}, "widgets_values": [true, 272, 272, 144, 128], "color": "#322", "bgcolor": "#533"}, {"id": 87, "type": "SetNode", "pos": [-696.9507446289062, -496.5005187988281], "size": [210, 58], "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "WANTEXTENCODER", "type": "WANTEXTENCODER", "link": 157}], "outputs": [{"label": "输出", "name": "*", "type": "*"}], "title": "Set_WANTEXTENCODER", "properties": {"previousName": "txt_encoder"}, "widgets_values": ["txt_encoder"], "color": "#232", "bgcolor": "#353"}, {"id": 128, "type": "easy clearCacheAll", "pos": [-939.9291381835938, -316.4044189453125], "size": [210, 26], "flags": {}, "order": 31, "mode": 0, "inputs": [{"label": "输入任何", "localized_name": "输入任何", "name": "anything", "type": "*", "link": 186}], "outputs": [{"localized_name": "output", "name": "output", "type": "*", "links": [187]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy clearCacheAll"}, "widgets_values": [], "color": "#322", "bgcolor": "#533"}, {"id": 88, "type": "SetNode", "pos": [-676.0443725585938, -316.0399475097656], "size": [210, 58], "flags": {}, "order": 36, "mode": 0, "inputs": [{"name": "*", "type": "*", "link": 187}], "outputs": [{"label": "输出", "name": "*", "type": "*"}], "title": "Set_*", "properties": {"previousName": "wan_vae"}, "widgets_values": ["wan_vae"], "color": "#323", "bgcolor": "#535"}, {"id": 134, "type": "WanVideoEncode", "pos": [-728.6834106445312, 149.83303833007812], "size": [330, 242], "flags": {}, "order": 38, "mode": 0, "inputs": [{"label": "vae", "localized_name": "vae", "name": "vae", "type": "WANVAE", "link": 194}, {"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 216}, {"label": "mask", "localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK"}, {"localized_name": "enable_vae_tiling", "name": "enable_vae_tiling", "type": "BOOLEAN", "widget": {"name": "enable_vae_tiling"}, "link": null}, {"localized_name": "tile_x", "name": "tile_x", "type": "INT", "widget": {"name": "tile_x"}, "link": null}, {"localized_name": "tile_y", "name": "tile_y", "type": "INT", "widget": {"name": "tile_y"}, "link": null}, {"localized_name": "tile_stride_x", "name": "tile_stride_x", "type": "INT", "widget": {"name": "tile_stride_x"}, "link": null}, {"localized_name": "tile_stride_y", "name": "tile_stride_y", "type": "INT", "widget": {"name": "tile_stride_y"}, "link": null}, {"localized_name": "noise_aug_strength", "name": "noise_aug_strength", "shape": 7, "type": "FLOAT", "widget": {"name": "noise_aug_strength"}, "link": null}, {"localized_name": "latent_strength", "name": "latent_strength", "shape": 7, "type": "FLOAT", "widget": {"name": "latent_strength"}, "link": null}], "outputs": [{"label": "samples", "localized_name": "samples", "name": "samples", "type": "LATENT", "links": [195]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "e5a326c9811514f2c08c89bccea9a7c731d9a503", "Node name for S&R": "WanVideoEncode"}, "widgets_values": [false, 272, 272, 144, 128, 0, 1], "color": "#322", "bgcolor": "#533"}, {"id": 11, "type": "LoadWanVideoT5TextEncoder", "pos": [-1377.884521484375, -497.15765380859375], "size": [377.1661376953125, 130], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "model_name", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}, {"localized_name": "precision", "name": "precision", "type": "COMBO", "widget": {"name": "precision"}, "link": null}, {"localized_name": "load_device", "name": "load_device", "shape": 7, "type": "COMBO", "widget": {"name": "load_device"}, "link": null}, {"localized_name": "quantization", "name": "quantization", "shape": 7, "type": "COMBO", "widget": {"name": "quantization"}, "link": null}], "outputs": [{"label": "wan_t5_model", "localized_name": "wan_t5_model", "name": "wan_t5_model", "type": "WANTEXTENCODER", "slot_index": 0, "links": [157]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "LoadWanVideoT5TextEncoder"}, "widgets_values": ["umt5-xxl-enc-bf16.safetensors", "bf16", "offload_device", "disabled"], "color": "#332922", "bgcolor": "#593930"}, {"id": 114, "type": "easy clearCacheAll", "pos": [97.41193389892578, -1186.3348388671875], "size": [210, 26], "flags": {}, "order": 24, "mode": 0, "inputs": [{"label": "输入任何", "localized_name": "输入任何", "name": "anything", "type": "*", "link": 170}], "outputs": [{"localized_name": "output", "name": "output", "type": "*", "links": [171]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy clearCacheAll"}, "widgets_values": [], "color": "#322", "bgcolor": "#533"}, {"id": 52, "type": "WanVideoTeaCache", "pos": [390.7341613769531, -1025.7696533203125], "size": [315, 178], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "rel_l1_thresh", "name": "rel_l1_thresh", "type": "FLOAT", "widget": {"name": "rel_l1_thresh"}, "link": null}, {"localized_name": "start_step", "name": "start_step", "type": "INT", "widget": {"name": "start_step"}, "link": null}, {"localized_name": "end_step", "name": "end_step", "type": "INT", "widget": {"name": "end_step"}, "link": null}, {"localized_name": "cache_device", "name": "cache_device", "type": "COMBO", "widget": {"name": "cache_device"}, "link": null}, {"localized_name": "use_coefficients", "name": "use_coefficients", "type": "BOOLEAN", "widget": {"name": "use_coefficients"}, "link": null}, {"localized_name": "mode", "name": "mode", "shape": 7, "type": "COMBO", "widget": {"name": "mode"}, "link": null}], "outputs": [{"label": "teacache_args", "localized_name": "teacache_args", "name": "teacache_args", "type": "TEACACHEARGS", "links": [127]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoTeaCache"}, "widgets_values": [0.25, 1, -1, "offload_device", "true", "e"]}, {"id": 126, "type": "SetNode", "pos": [1318.1585693359375, -1033.410888671875], "size": [210, 60], "flags": {}, "order": 43, "mode": 0, "inputs": [{"name": "IMAGE", "type": "IMAGE", "link": 184}], "outputs": [{"name": "*", "type": "*"}], "title": "Set_IMGSS", "properties": {"previousName": "IMGSS"}, "widgets_values": ["IMGSS"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 30, "type": "VHS_VideoCombine", "pos": [-151.23687744140625, 80.02503967285156], "size": [944.7930297851562, 881.534423828125], "flags": {}, "order": 42, "mode": 0, "inputs": [{"label": "图像", "localized_name": "images", "name": "images", "type": "IMAGE", "link": 36}, {"label": "音频", "localized_name": "audio", "name": "audio", "shape": 7, "type": "AUDIO"}, {"label": "批次管理", "localized_name": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager"}, {"localized_name": "vae", "name": "vae", "shape": 7, "type": "VAE"}, {"localized_name": "frame_rate", "name": "frame_rate", "type": "FLOAT", "widget": {"name": "frame_rate"}, "link": null}, {"localized_name": "loop_count", "name": "loop_count", "type": "INT", "widget": {"name": "loop_count"}, "link": null}, {"localized_name": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}, {"localized_name": "format", "name": "format", "type": "COMBO", "widget": {"name": "format"}, "link": null}, {"localized_name": "pingpong", "name": "pingpong", "type": "BOOLEAN", "widget": {"name": "pingpong"}, "link": null}, {"localized_name": "save_output", "name": "save_output", "type": "BOOLEAN", "widget": {"name": "save_output"}, "link": null}], "outputs": [{"label": "文件名", "localized_name": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES"}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "WanVideoWrapper_I2V", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": true, "videopreview": {"paused": false, "hidden": false, "params": {"filename": "WanVideoWrapper_I2V_00003.mp4", "workflow": "WanVideoWrapper_I2V_00003.png", "fullpath": "H:\\ComfyUI_WAN2.1\\ComfyUI\\output\\WanVideoWrapper_I2V_00003.mp4", "format": "video/h264-mp4", "subfolder": "", "type": "output", "frame_rate": 24}}}}, {"id": 90, "type": "GetNode", "pos": [1062.123779296875, -1260.634033203125], "size": [210, 58], "flags": {"collapsed": true}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"name": "*", "type": "*", "links": [136]}], "title": "Get_wan_vae", "properties": {}, "widgets_values": ["wan_vae"], "color": "#332922", "bgcolor": "#593930"}, {"id": 72, "type": "WanVideoExperimentalArgs", "pos": [392.49285888671875, -783.9866333007812], "size": [317.2294921875, 226], "flags": {}, "order": 14, "mode": 0, "inputs": [{"localized_name": "video_attention_split_steps", "name": "video_attention_split_steps", "type": "STRING", "widget": {"name": "video_attention_split_steps"}, "link": null}, {"localized_name": "cfg_zero_star", "name": "cfg_zero_star", "type": "BOOLEAN", "widget": {"name": "cfg_zero_star"}, "link": null}, {"localized_name": "use_zero_init", "name": "use_zero_init", "type": "BOOLEAN", "widget": {"name": "use_zero_init"}, "link": null}, {"localized_name": "zero_star_steps", "name": "zero_star_steps", "type": "INT", "widget": {"name": "zero_star_steps"}, "link": null}, {"localized_name": "use_fresca", "name": "use_fresca", "type": "BOOLEAN", "widget": {"name": "use_fresca"}, "link": null}, {"localized_name": "fresca_scale_low", "name": "fresca_scale_low", "type": "FLOAT", "widget": {"name": "fresca_scale_low"}, "link": null}, {"localized_name": "fresca_scale_high", "name": "fresca_scale_high", "type": "FLOAT", "widget": {"name": "fresca_scale_high"}, "link": null}, {"localized_name": "fresca_freq_cutoff", "name": "fresca_freq_cutoff", "type": "INT", "widget": {"name": "fresca_freq_cutoff"}, "link": null}], "outputs": [{"label": "exp_args", "localized_name": "exp_args", "name": "exp_args", "type": "EXPERIMENTALARGS", "links": [129]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "e3ea2bf392213d16c2ba56f7584354e819b72575", "Node name for S&R": "WanVideoExperimentalArgs"}, "widgets_values": ["", true, false, 0, false, 1, 1.2400000000000002, 20]}, {"id": 84, "type": "WanVideoDiffusionForcingSampler", "pos": [793.0948486328125, -1184.6763916015625], "size": [428.4000244140625, 458], "flags": {}, "order": 40, "mode": 0, "inputs": [{"label": "model", "localized_name": "model", "name": "model", "type": "WANVIDEOMODEL", "link": 171}, {"label": "text_embeds", "localized_name": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "link": 174}, {"label": "image_embeds", "localized_name": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "link": 176}, {"label": "samples", "localized_name": "samples", "name": "samples", "shape": 7, "type": "LATENT"}, {"label": "prefix_samples", "localized_name": "prefix_samples", "name": "prefix_samples", "shape": 7, "type": "LATENT", "link": 195}, {"label": "teacache_args", "localized_name": "teacache_args", "name": "teacache_args", "shape": 7, "type": "TEACACHEARGS", "link": 127}, {"label": "slg_args", "localized_name": "slg_args", "name": "slg_args", "shape": 7, "type": "SLGARGS", "link": 130}, {"label": "experimental_args", "localized_name": "experimental_args", "name": "experimental_args", "shape": 7, "type": "EXPERIMENTALARGS", "link": 129}, {"label": "unianimate_poses", "localized_name": "unianimate_poses", "name": "unianimate_poses", "shape": 7, "type": "UNIANIMATE_POSE"}, {"localized_name": "addnoise_condition", "name": "addnoise_condition", "type": "INT", "widget": {"name": "addnoise_condition"}, "link": null}, {"localized_name": "fps", "name": "fps", "type": "FLOAT", "widget": {"name": "fps"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "shift", "name": "shift", "type": "FLOAT", "widget": {"name": "shift"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "force_offload", "name": "force_offload", "type": "BOOLEAN", "widget": {"name": "force_offload"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "denoise_strength", "name": "denoise_strength", "shape": 7, "type": "FLOAT", "widget": {"name": "denoise_strength"}, "link": null}, {"localized_name": "rope_function", "name": "rope_function", "shape": 7, "type": "COMBO", "widget": {"name": "rope_function"}, "link": null}], "outputs": [{"label": "samples", "localized_name": "samples", "name": "samples", "type": "LATENT", "links": [128]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "6099ad393b071728032fd481e96d77d2900eee2c", "Node name for S&R": "WanVideoDiffusionForcingSampler"}, "widgets_values": [10, 24.000000000000004, 30, 6, 8, 777572049454712, "randomize", true, "unipc", 1, "comfy"], "color": "#233", "bgcolor": "#355"}, {"id": 16, "type": "WanVideoTextEncode", "pos": [-185.76177978515625, -981.4402465820312], "size": [522.875, 490.5695495605469], "flags": {}, "order": 35, "mode": 0, "inputs": [{"label": "t5", "localized_name": "t5", "name": "t5", "type": "WANTEXTENCODER", "link": 178}, {"label": "model_to_offload", "localized_name": "model_to_offload", "name": "model_to_offload", "shape": 7, "type": "WANVIDEOMODEL", "link": 180}, {"localized_name": "positive_prompt", "name": "positive_prompt", "type": "STRING", "widget": {"name": "positive_prompt"}, "link": null}, {"localized_name": "negative_prompt", "name": "negative_prompt", "type": "STRING", "widget": {"name": "negative_prompt"}, "link": null}, {"localized_name": "force_offload", "name": "force_offload", "shape": 7, "type": "BOOLEAN", "widget": {"name": "force_offload"}, "link": null}], "outputs": [{"label": "text_embeds", "localized_name": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "slot_index": 0, "links": [173]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoTextEncode"}, "widgets_values": ["大师杰作，丰富的画面细节，8K高清拍摄，一个非常漂亮的19岁中国女孩站在水中跳舞。", "色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走", true], "color": "#232", "bgcolor": "#353"}, {"id": 98, "type": "SetNode", "pos": [-1124.0628662109375, -1504.110595703125], "size": [210, 58], "flags": {"collapsed": true}, "order": 28, "mode": 0, "inputs": [{"name": "INT", "type": "INT", "link": 140}], "outputs": [{"label": "输出", "name": "*", "type": "*"}], "title": "Set_INT", "properties": {"previousName": "length"}, "widgets_values": ["length"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 97, "type": "INTConstant", "pos": [-1578.07275390625, -1534.140380859375], "size": [370.3948059082031, 58], "flags": {}, "order": 15, "mode": 0, "inputs": [{"localized_name": "value", "name": "value", "type": "INT", "widget": {"name": "value"}, "link": null}], "outputs": [{"label": "值", "localized_name": "value", "name": "value", "type": "INT", "links": [140]}], "title": "输入视频时长以帧计算", "properties": {"cnr_id": "comfyui-kjnodes", "ver": "2aa4da0f587f68c1c255b0152ed8f5c334ebe4b4", "Node name for S&R": "INTConstant"}, "widgets_values": [69], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 96, "type": "INTConstant", "pos": [-1580.3798828125, -1676.898681640625], "size": [370.3948059082031, 58], "flags": {}, "order": 16, "mode": 0, "inputs": [{"localized_name": "value", "name": "value", "type": "INT", "widget": {"name": "value"}, "link": null}], "outputs": [{"label": "值", "localized_name": "value", "name": "value", "type": "INT", "links": [203]}], "title": "输入视频", "properties": {"cnr_id": "comfyui-kjnodes", "ver": "2aa4da0f587f68c1c255b0152ed8f5c334ebe4b4", "Node name for S&R": "INTConstant"}, "widgets_values": [832], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 95, "type": "SetNode", "pos": [-1119.2371826171875, -1813.7530517578125], "size": [210, 58], "flags": {"collapsed": false}, "order": 30, "mode": 0, "inputs": [{"name": "INT", "type": "INT", "link": 204}], "outputs": [{"label": "输出", "name": "*", "type": "*"}], "title": "Set_INT", "properties": {"previousName": "height"}, "widgets_values": ["height"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 92, "type": "SetNode", "pos": [-1123.92236328125, -1667.7625732421875], "size": [210, 60], "flags": {"collapsed": false}, "order": 29, "mode": 0, "inputs": [{"name": "INT", "type": "INT", "link": 203}], "outputs": [{"label": "输出", "name": "*", "type": "*"}], "title": "Set_INT", "properties": {"previousName": "width"}, "widgets_values": ["width"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 94, "type": "INTConstant", "pos": [-1587.1566162109375, -1804.694580078125], "size": [380.07635498046875, 58], "flags": {}, "order": 17, "mode": 0, "inputs": [{"localized_name": "value", "name": "value", "type": "INT", "widget": {"name": "value"}, "link": null}], "outputs": [{"label": "值", "localized_name": "value", "name": "value", "type": "INT", "links": [204]}], "title": "输入视频", "properties": {"cnr_id": "comfyui-kjnodes", "ver": "2aa4da0f587f68c1c255b0152ed8f5c334ebe4b4", "Node name for S&R": "INTConstant"}, "widgets_values": [480], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 112, "type": "WanVideoModelLoader", "pos": [-826.5501708984375, -996.6250610351562], "size": [528.6734619140625, 254], "flags": {}, "order": 23, "mode": 0, "inputs": [{"label": "compile_args", "localized_name": "compile_args", "name": "compile_args", "shape": 7, "type": "WANCOMPILEARGS", "link": 165}, {"label": "block_swap_args", "localized_name": "block_swap_args", "name": "block_swap_args", "shape": 7, "type": "BLOCKSWAPARGS", "link": 166}, {"label": "lora", "localized_name": "lora", "name": "lora", "shape": 7, "type": "WANVIDLORA"}, {"label": "vram_management_args", "localized_name": "vram_management_args", "name": "vram_management_args", "shape": 7, "type": "VRAM_MANAGEMENTARGS"}, {"label": "vace_model", "localized_name": "vace_model", "name": "vace_model", "shape": 7, "type": "VACEPATH"}, {"localized_name": "fantasytalking_model", "name": "fantasytalking_model", "shape": 7, "type": "FANTASYTALKINGMODEL", "link": null}, {"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "base_precision", "name": "base_precision", "type": "COMBO", "widget": {"name": "base_precision"}, "link": null}, {"localized_name": "quantization", "name": "quantization", "type": "COMBO", "widget": {"name": "quantization"}, "link": null}, {"localized_name": "load_device", "name": "load_device", "type": "COMBO", "widget": {"name": "load_device"}, "link": null}, {"localized_name": "attention_mode", "name": "attention_mode", "shape": 7, "type": "COMBO", "widget": {"name": "attention_mode"}, "link": null}], "outputs": [{"label": "model", "localized_name": "model", "name": "model", "type": "WANVIDEOMODEL", "slot_index": 0, "links": [167]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoModelLoader"}, "widgets_values": ["Wan2_1-SkyReels-V2-DF-14B-540P_fp8_e4m3fn.safetensors", "fp16", "fp8_e4m3fn", "offload_device", "sdpa"], "color": "#223", "bgcolor": "#335"}, {"id": 38, "type": "WanVideoVAELoader", "pos": [-1365.739501953125, -316.304443359375], "size": [372.7727966308594, 82], "flags": {}, "order": 18, "mode": 0, "inputs": [{"localized_name": "model_name", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}, {"localized_name": "precision", "name": "precision", "shape": 7, "type": "COMBO", "widget": {"name": "precision"}, "link": null}], "outputs": [{"label": "vae", "localized_name": "vae", "name": "vae", "type": "WANVAE", "slot_index": 0, "links": [186]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoVAELoader"}, "widgets_values": ["Wan2_1_VAE_bf16.safetensors", "fp32"], "color": "#322", "bgcolor": "#533"}, {"id": 101, "type": "GetNode", "pos": [-194.35458374023438, -323.744384765625], "size": [210, 58], "flags": {"collapsed": false}, "order": 19, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [143]}], "title": "Get_length", "properties": {}, "widgets_values": ["length"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 130, "type": "LoadImage", "pos": [-1674.039794921875, 344.886474609375], "size": [425.9873046875, 415.4740295410156], "flags": {}, "order": 20, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [190]}, {"label": "遮罩", "localized_name": "遮罩", "name": "MASK", "type": "MASK"}], "title": "加载图像【上传参考图】", "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "LoadImage"}, "widgets_values": ["jimeng-2025-05-06-722-一个二次元开放世界的游戏的的游戏界面.jpeg", "image"], "color": "#233", "bgcolor": "#355"}, {"id": 99, "type": "GetNode", "pos": [-1562.8057861328125, 93.78955841064453], "size": [210, 60], "flags": {"collapsed": false}, "order": 21, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [141, 214]}], "title": "Get_width", "properties": {}, "widgets_values": ["width"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 100, "type": "GetNode", "pos": [-1562.42041015625, 209.4209747314453], "size": [210, 58], "flags": {"collapsed": false}, "order": 22, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [142, 215]}], "title": "Get_height", "properties": {}, "widgets_values": ["height"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 135, "type": "GetNode", "pos": [-1203.8583984375, 41.96058654785156], "size": [210, 60], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "*", "type": "*", "links": [194]}], "title": "Get_wan_vae", "properties": {}, "widgets_values": ["wan_vae"], "color": "#323", "bgcolor": "#535"}, {"id": 133, "type": "ImageResizeKJ", "pos": [-1118.3734130859375, 162.4150390625], "size": [315, 266], "flags": {}, "order": 33, "mode": 0, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 190}, {"label": "参考图像", "localized_name": "get_image_size", "name": "get_image_size", "shape": 7, "type": "IMAGE"}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 214}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 215}, {"localized_name": "upscale_method", "name": "upscale_method", "type": "COMBO", "widget": {"name": "upscale_method"}, "link": null}, {"localized_name": "keep_proportion", "name": "keep_proportion", "type": "BOOLEAN", "widget": {"name": "keep_proportion"}, "link": null}, {"localized_name": "divisible_by", "name": "divisible_by", "type": "INT", "widget": {"name": "divisible_by"}, "link": null}, {"localized_name": "crop", "name": "crop", "shape": 7, "type": "COMBO", "widget": {"name": "crop"}, "link": null}, {"label": "高度", "name": "height_input", "shape": 7, "type": "INT", "widget": {"name": "height_input"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [216]}, {"label": "宽度", "localized_name": "width", "name": "width", "type": "INT", "links": []}, {"label": "高度", "localized_name": "height", "name": "height", "type": "INT", "links": []}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.1.0", "Node name for S&R": "ImageResizeKJ"}, "widgets_values": [832, 480, "nearest-exact", false, 2, 0]}], "links": [[36, 28, 0, 30, 0, "IMAGE"], [127, 52, 0, 84, 5, "TEACACHEARGS"], [128, 84, 0, 28, 1, "LATENT"], [129, 72, 0, 84, 7, "EXPERIMENTALARGS"], [130, 85, 0, 84, 6, "SLGARGS"], [136, 90, 0, 28, 0, "WANVAE"], [140, 97, 0, 98, 0, "*"], [141, 99, 0, 81, 1, "INT"], [142, 100, 0, 81, 2, "INT"], [143, 101, 0, 81, 3, "INT"], [157, 11, 0, 87, 0, "*"], [165, 111, 0, 112, 0, "WANCOMPILEARGS"], [166, 113, 0, 112, 1, "BLOCKSWAPARGS"], [167, 112, 0, 86, 0, "WANVIDEOMODEL"], [170, 89, 0, 114, 0, "*"], [171, 114, 0, 84, 0, "WANVIDEOMODEL"], [173, 16, 0, 115, 0, "*"], [174, 115, 0, 84, 1, "WANVIDEOTEXTEMBEDS"], [175, 81, 0, 116, 0, "*"], [176, 116, 0, 84, 2, "WANVIDIMAGE_EMBEDS"], [177, 91, 0, 118, 0, "*"], [178, 118, 0, 16, 0, "WANTEXTENCODER"], [179, 89, 0, 119, 0, "*"], [180, 119, 0, 16, 1, "WANVIDEOMODEL"], [184, 28, 0, 126, 0, "*"], [186, 38, 0, 128, 0, "*"], [187, 128, 0, 88, 0, "WANVAE"], [190, 130, 0, 133, 0, "IMAGE"], [194, 135, 0, 134, 0, "WANVAE"], [195, 134, 0, 84, 4, "LATENT"], [203, 96, 0, 92, 0, "INT"], [204, 94, 0, 95, 0, "INT"], [214, 99, 0, 133, 2, "INT"], [215, 100, 0, 133, 3, "INT"], [216, 133, 0, 134, 1, "IMAGE"]], "groups": [{"id": 1, "title": "模型加载", "bounding": [-1729.4354248046875, -1395.8670654296875, 1439.829833984375, 1294.1380615234375], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "第一次采样", "bounding": [-231.70556640625, -1402.718017578125, 1821.0096435546875, 1270.4793701171875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "视频全局设置", "bounding": [-1722.41552734375, -1910.2427978515625, 1434.2935791015625, 485.6804504394531], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "上传参考图像", "bounding": [-1723.658203125, -33.73894500732422, 1448.6766357421875, 936.7042846679688], "color": "#3f789e", "font_size": 50, "flags": {}}, {"id": 5, "title": "视频输出", "bounding": [-208.6156768798828, -61.70418167114258, 1772.44677734375, 1075.4781494140625], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.8954302432552391, "offset": [1802.376592632106, 1888.261809338928]}, "frontendVersion": "1.18.9", "VHS_KeepIntermediate": true, "VHS_MetadataImage": true, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "VHS_latentpreview": false, "node_versions": {"ComfyUI-WanVideoWrapper": "5a2383621a05825d0d0437781afcb8552d9590fd", "ComfyUI-VideoHelperSuite": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "ComfyUI-KJNodes": "a5bd3c86c8ed6b83c55c2d0e7a59515b15a0137f", "comfy-core": "0.3.26"}}, "version": 0.4}