{"id": "dcd657ce-b35a-4957-a764-e766affbefcf", "revision": 0, "last_node_id": 490, "last_link_id": 931, "nodes": [{"id": 468, "type": "PreviewImage", "pos": [8050, 400], "size": [300, 440], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 931}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 480, "type": "CLIPTextEncode", "pos": [8020, 1130], "size": [310, 88], "flags": {"collapsed": false}, "order": 7, "mode": 0, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 906}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [904]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["hand"], "color": "#232", "bgcolor": "#353"}, {"id": 482, "type": "CLIPTextEncode", "pos": [8020, 1250], "size": [310, 88], "flags": {"collapsed": false}, "order": 8, "mode": 0, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 908}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [907]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""], "color": "#322", "bgcolor": "#533"}, {"id": 489, "type": "Note", "pos": [7440, 500], "size": [230, 88], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["邮件在遮罩编辑器中打开，手动涂抹手部。\n\n经尝试，一次修复一只手，效果比较好。如果图中有双手，可以依次修复。"], "color": "#432", "bgcolor": "#653"}, {"id": 490, "type": "Note", "pos": [7780, 1140], "size": [230, 88], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["这里也可以填入其他内容，进行局部重绘。\n与遮罩配合即可。\n如：想让人物佩戴项链，则手动绘制一个脖子颈部的遮罩，这里填入necklace。"], "color": "#432", "bgcolor": "#653"}, {"id": 488, "type": "LoadImage", "pos": [7670, 400], "size": [330, 500], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [929]}, {"label": "遮罩", "localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": [930]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "LoadImage"}, "widgets_values": ["clipspace/clipspace-mask-24385.599999999627.png [input]", "image"]}, {"id": 484, "type": "UNETLoader", "pos": [8020, 1010], "size": [315, 82], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "UNet名称", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "数据类型", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [913]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-fill-dev.safetensors", "default"]}, {"id": 11, "type": "DualCLIPLoader", "pos": [8020, 1370], "size": [320, 130], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "CLIP名称1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "CLIP名称2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [906, 908]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp16.safetensors", "flux", "default"]}, {"id": 10, "type": "VAELoader", "pos": [8020, 1510], "size": [320, 60], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"label": "VAE", "localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [909, 916]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 478, "type": "InpaintModelConditioning", "pos": [8350.80078125, 1200.206787109375], "size": [290, 138], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "正面条件", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 905}, {"label": "负面条件", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 907}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 909}, {"label": "图像", "localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 929}, {"label": "遮罩", "localized_name": "遮罩", "name": "mask", "type": "MASK", "link": 930}, {"localized_name": "噪波遮罩", "name": "noise_mask", "type": "BOOLEAN", "widget": {"name": "noise_mask"}, "link": null}], "outputs": [{"label": "正面条件", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [910]}, {"label": "负面条件", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [911]}, {"label": "Latent", "localized_name": "Latent", "name": "latent", "type": "LATENT", "slot_index": 2, "links": [912]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [false]}, {"id": 479, "type": "FluxGuidance", "pos": [8352.2734375, 1087.4656982421875], "size": [290, 60], "flags": {}, "order": 9, "mode": 0, "inputs": [{"label": "条件", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 904}, {"localized_name": "引导", "name": "guidance", "type": "FLOAT", "widget": {"name": "guidance"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [905]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "FluxGuidance"}, "widgets_values": [30]}, {"id": 485, "type": "DifferentialDiffusion", "pos": [8357.9384765625, 1012.8321533203125], "size": [290, 30], "flags": {}, "order": 6, "mode": 0, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 913}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [914]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 486, "type": "VAEDecode", "pos": [8359.85546875, 1379.8182373046875], "size": [340, 50], "flags": {"collapsed": false}, "order": 12, "mode": 0, "inputs": [{"label": "Latent", "localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 915}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 916}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [931]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 483, "type": "K<PERSON><PERSON><PERSON>", "pos": [8727.34765625, 1014.7876586914062], "size": [340, 620], "flags": {}, "order": 11, "mode": 0, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 914}, {"label": "正面条件", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 910}, {"label": "负面条件", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 911}, {"label": "Latent", "localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 912}, {"localized_name": "种子", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"label": "Latent", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [915]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [159558089067411, "randomize", 20, 1, "euler", "normal", 1]}], "links": [[904, 480, 0, 479, 0, "CONDITIONING"], [905, 479, 0, 478, 0, "CONDITIONING"], [906, 11, 0, 480, 0, "CLIP"], [907, 482, 0, 478, 1, "CONDITIONING"], [908, 11, 0, 482, 0, "CLIP"], [909, 10, 0, 478, 2, "VAE"], [910, 478, 0, 483, 1, "CONDITIONING"], [911, 478, 1, 483, 2, "CONDITIONING"], [912, 478, 2, 483, 3, "LATENT"], [913, 484, 0, 485, 0, "MODEL"], [914, 485, 0, 483, 0, "MODEL"], [915, 483, 0, 486, 0, "LATENT"], [916, 10, 0, 486, 1, "VAE"], [929, 488, 0, 478, 3, "IMAGE"], [930, 488, 1, 478, 4, "MASK"], [931, 486, 0, 468, 0, "IMAGE"]], "groups": [{"id": 1, "title": "修复手部", "bounding": [7690, 930, 1650, 830], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "输出", "bounding": [8030, 330, 320, 580], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "输入", "bounding": [7670, 330, 340, 580], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.45, "offset": [-6690.21665253737, -519.4742518197322]}, "0246.VERSION": [0, 0, 4], "workspace_info": {"id": "a0Ga0Mxd_-z0QZHMLipnJ", "saveLock": false, "cloudID": null, "coverMediaPath": null}, "ue_links": [], "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true, "frontendVersion": "1.18.9"}, "version": 0.4}