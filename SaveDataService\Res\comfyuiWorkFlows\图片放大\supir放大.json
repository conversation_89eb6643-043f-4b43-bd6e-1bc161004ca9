{"id": "6e2516bf-54da-4208-97e2-9f3683af667f", "revision": 0, "last_node_id": 78, "last_link_id": 148, "nodes": [{"id": 19, "type": "GetNode", "pos": [840.3587646484375, -715.0285034179688], "size": [210, 58], "flags": {"collapsed": true}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "SUPIRVAE", "name": "SUPIRVAE", "type": "SUPIRVAE", "links": [32]}], "title": "Get_SUPIRVAE", "properties": {}, "widgets_values": ["SUPIRVAE"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 17, "type": "SetNode", "pos": [-33.967437744140625, -770.2814331054688], "size": [210, 58], "flags": {"collapsed": true}, "order": 9, "mode": 0, "inputs": [{"label": "SUPIRVAE", "name": "SUPIRVAE", "type": "SUPIRVAE", "link": 45}], "outputs": [{"label": "SUPIRVAE", "name": "SUPIRVAE", "type": "SUPIRVAE", "links": [46]}], "title": "Set_SUPIRVAE", "properties": {"previousName": "SUPIRVAE"}, "widgets_values": ["SUPIRVAE"]}, {"id": 10, "type": "SUPIR_decode", "pos": [1231.75244140625, -385.0960693359375], "size": [258.0101318359375, 102], "flags": {}, "order": 14, "mode": 0, "inputs": [{"label": "SUPIR_VAE", "localized_name": "SUPIR_VAE", "name": "SUPIR_VAE", "type": "SUPIRVAE", "link": 32}, {"label": "latents", "localized_name": "latents", "name": "latents", "type": "LATENT", "link": 12}, {"localized_name": "use_tiled_vae", "name": "use_tiled_vae", "type": "BOOLEAN", "widget": {"name": "use_tiled_vae"}, "link": null}, {"label": "decoder_tile_size", "localized_name": "decoder_tile_size", "name": "decoder_tile_size", "type": "INT", "widget": {"name": "decoder_tile_size"}, "link": 75}], "outputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [26]}], "properties": {"cnr_id": "comfy<PERSON>-su<PERSON>r", "ver": "1.0.1", "Node name for S&R": "SUPIR_decode"}, "widgets_values": [false, 512]}, {"id": 11, "type": "SUPIR_encode", "pos": [516.8307495117188, -741.1287231445312], "size": [246.84620666503906, 155.03585815429688], "flags": {}, "order": 11, "mode": 0, "inputs": [{"label": "SUPIR_VAE", "localized_name": "SUPIR_VAE", "name": "SUPIR_VAE", "type": "SUPIRVAE", "link": 15}, {"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 16}, {"localized_name": "use_tiled_vae", "name": "use_tiled_vae", "type": "BOOLEAN", "widget": {"name": "use_tiled_vae"}, "link": null}, {"label": "encoder_tile_size", "localized_name": "encoder_tile_size", "name": "encoder_tile_size", "type": "INT", "widget": {"name": "encoder_tile_size"}, "link": 72}, {"localized_name": "encoder_dtype", "name": "encoder_dtype", "type": "COMBO", "widget": {"name": "encoder_dtype"}, "link": null}], "outputs": [{"label": "latent", "localized_name": "latent", "name": "latent", "type": "LATENT", "slot_index": 0, "links": [17]}], "properties": {"cnr_id": "comfy<PERSON>-su<PERSON>r", "ver": "1.0.1", "Node name for S&R": "SUPIR_encode"}, "widgets_values": [false, 512, "auto"]}, {"id": 12, "type": "Reroute", "pos": [143, -581], "size": [124, 26], "flags": {}, "order": 8, "mode": 0, "inputs": [{"label": "", "name": "", "type": "*", "link": 44}], "outputs": [{"label": "SUPIRMODEL", "name": "SUPIRMODEL", "type": "SUPIRMODEL", "slot_index": 0, "links": [22, 23]}], "properties": {"showOutputText": true, "horizontal": false}}, {"id": 36, "type": "ImpactInt", "pos": [-304, -394.4422607421875], "size": [254.5851287841797, 61.672119140625], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "value", "name": "value", "type": "INT", "widget": {"name": "value"}, "link": null}], "outputs": [{"label": "INT", "localized_name": "整数", "name": "INT", "type": "INT", "slot_index": 0, "links": [70, 71, 72, 73, 75]}], "title": "<PERSON><PERSON>", "properties": {"cnr_id": "comfyui-impact-pack", "ver": "8.14.2", "Node name for S&R": "ImpactInt"}, "widgets_values": [1024], "color": "#322", "bgcolor": "#533"}, {"id": 37, "type": "ImpactInt", "pos": [-290, -273.4422607421875], "size": [248.16917419433594, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "value", "name": "value", "type": "INT", "widget": {"name": "value"}, "link": null}], "outputs": [{"label": "INT", "localized_name": "整数", "name": "INT", "type": "INT", "slot_index": 0, "links": [74]}], "title": "Tile Stride", "properties": {"cnr_id": "comfyui-impact-pack", "ver": "8.14.2", "Node name for S&R": "ImpactInt"}, "widgets_values": [1024], "color": "#322", "bgcolor": "#533"}, {"id": 7, "type": "SUPIR_sample", "pos": [803.6784057617188, -573.3956909179688], "size": [337.0279541015625, 454], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "SUPIR_model", "localized_name": "SUPIR_model", "name": "SUPIR_model", "type": "SUPIRMODEL", "link": 23}, {"label": "latents", "localized_name": "latents", "name": "latents", "type": "LATENT", "link": 17}, {"label": "positive", "localized_name": "positive", "name": "positive", "type": "SUPIR_cond_pos", "link": 8}, {"label": "negative", "localized_name": "negative", "name": "negative", "type": "SUPIR_cond_neg", "link": 9}, {"label": "seed", "localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": 58}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg_scale_start", "name": "cfg_scale_start", "type": "FLOAT", "widget": {"name": "cfg_scale_start"}, "link": null}, {"localized_name": "cfg_scale_end", "name": "cfg_scale_end", "type": "FLOAT", "widget": {"name": "cfg_scale_end"}, "link": null}, {"localized_name": "EDM_s_churn", "name": "EDM_s_churn", "type": "INT", "widget": {"name": "EDM_s_churn"}, "link": null}, {"localized_name": "s_noise", "name": "s_noise", "type": "FLOAT", "widget": {"name": "s_noise"}, "link": null}, {"localized_name": "DPMPP_eta", "name": "DPMPP_eta", "type": "FLOAT", "widget": {"name": "DPMPP_eta"}, "link": null}, {"localized_name": "control_scale_start", "name": "control_scale_start", "type": "FLOAT", "widget": {"name": "control_scale_start"}, "link": null}, {"localized_name": "control_scale_end", "name": "control_scale_end", "type": "FLOAT", "widget": {"name": "control_scale_end"}, "link": null}, {"localized_name": "restore_cfg", "name": "restore_cfg", "type": "FLOAT", "widget": {"name": "restore_cfg"}, "link": null}, {"localized_name": "keep_model_loaded", "name": "keep_model_loaded", "type": "BOOLEAN", "widget": {"name": "keep_model_loaded"}, "link": null}, {"localized_name": "sampler", "name": "sampler", "type": "COMBO", "widget": {"name": "sampler"}, "link": null}, {"label": "sampler_tile_size", "localized_name": "sampler_tile_size", "name": "sampler_tile_size", "shape": 7, "type": "INT", "widget": {"name": "sampler_tile_size"}, "link": 73}, {"label": "sampler_tile_stride", "localized_name": "sampler_tile_stride", "name": "sampler_tile_stride", "shape": 7, "type": "INT", "widget": {"name": "sampler_tile_stride"}, "link": 74}], "outputs": [{"label": "latent", "localized_name": "latent", "name": "latent", "type": "LATENT", "slot_index": 0, "links": [12]}], "properties": {"cnr_id": "comfy<PERSON>-su<PERSON>r", "ver": "1.0.1", "Node name for S&R": "SUPIR_sample"}, "widgets_values": [174277455657960, "fixed", 10, 2, 1.5, 5, 1.004, 1, 1, 0.9, 1, true, "RestoreDPMPP2MSampler", 512, 256], "color": "#322", "bgcolor": "#533"}, {"id": 9, "type": "SUPIR_conditioner", "pos": [125.69755554199219, -434.50347900390625], "size": [286.7212219238281, 284.2606506347656], "flags": {}, "order": 12, "mode": 0, "inputs": [{"label": "SUPIR_model", "localized_name": "SUPIR_model", "name": "SUPIR_model", "type": "SUPIRMODEL", "link": 22}, {"label": "latents", "localized_name": "latents", "name": "latents", "type": "LATENT", "link": 20}, {"label": "captions", "localized_name": "captions", "name": "captions", "shape": 7, "type": "STRING"}, {"localized_name": "positive_prompt", "name": "positive_prompt", "type": "STRING", "widget": {"name": "positive_prompt"}, "link": null}, {"localized_name": "negative_prompt", "name": "negative_prompt", "type": "STRING", "widget": {"name": "negative_prompt"}, "link": null}], "outputs": [{"label": "positive", "localized_name": "positive", "name": "positive", "type": "SUPIR_cond_pos", "slot_index": 0, "links": [8]}, {"label": "negative", "localized_name": "negative", "name": "negative", "type": "SUPIR_cond_neg", "slot_index": 1, "links": [9]}], "properties": {"cnr_id": "comfy<PERSON>-su<PERSON>r", "ver": "1.0.1", "Node name for S&R": "SUPIR_conditioner"}, "widgets_values": ["1 chinese girl talking, high quality, detailed, photograph, beautiful face, beautiful eyes", "bad quality, blurry, messy"], "color": "#322", "bgcolor": "#533"}, {"id": 29, "type": "Seed (rgthree)", "pos": [470.8494873046875, -472.9048767089844], "size": [255.40321350097656, 130], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"dir": 4, "label": "SEED", "name": "SEED", "shape": 3, "type": "INT", "slot_index": 0, "links": [58]}], "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "Node name for S&R": "Seed (rgthree)"}, "widgets_values": [-1, null, null, null], "color": "#322", "bgcolor": "#533"}, {"id": 21, "type": "SUPIR_model_loader_v2", "pos": [-335.17779541015625, -743.9132690429688], "size": [289.0464172363281, 170], "flags": {}, "order": 6, "mode": 0, "inputs": [{"label": "model", "localized_name": "model", "name": "model", "type": "MODEL", "link": 35}, {"label": "clip", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 39}, {"label": "vae", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 40}, {"localized_name": "supir_model", "name": "supir_model", "type": "COMBO", "widget": {"name": "supir_model"}, "link": null}, {"localized_name": "fp8_unet", "name": "fp8_unet", "type": "BOOLEAN", "widget": {"name": "fp8_unet"}, "link": null}, {"localized_name": "diffusion_dtype", "name": "diffusion_dtype", "type": "COMBO", "widget": {"name": "diffusion_dtype"}, "link": null}, {"localized_name": "high_vram", "name": "high_vram", "shape": 7, "type": "BOOLEAN", "widget": {"name": "high_vram"}, "link": null}], "outputs": [{"label": "SUPIR_model", "localized_name": "SUPIR_model", "name": "SUPIR_model", "type": "SUPIRMODEL", "slot_index": 0, "links": [44]}, {"label": "SUPIR_VAE", "localized_name": "SUPIR_VAE", "name": "SUPIR_VAE", "type": "SUPIRVAE", "slot_index": 1, "links": [45]}], "properties": {"cnr_id": "comfy<PERSON>-su<PERSON>r", "ver": "1.0.1", "Node name for S&R": "SUPIR_model_loader_v2"}, "widgets_values": ["SUPIR-v0Q.ckpt", false, "auto", false]}, {"id": 14, "type": "ColorMatch", "pos": [1200.224609375, -734.91943359375], "size": [255.9095916748047, 102], "flags": {}, "order": 15, "mode": 0, "inputs": [{"label": "image_ref", "localized_name": "image_ref", "name": "image_ref", "type": "IMAGE", "link": 145}, {"label": "image_target", "localized_name": "image_target", "name": "image_target", "type": "IMAGE", "link": 26}, {"localized_name": "method", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}, {"localized_name": "strength", "name": "strength", "shape": 7, "type": "FLOAT", "widget": {"name": "strength"}, "link": null}], "outputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": []}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.1.0", "Node name for S&R": "ColorMatch"}, "widgets_values": ["mkl", 1]}, {"id": 5, "type": "SUPIR_first_stage", "pos": [80.01952362060547, -761.349365234375], "size": [297.7007751464844, 170], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "SUPIR_VAE", "localized_name": "SUPIR_VAE", "name": "SUPIR_VAE", "type": "SUPIRVAE", "link": 46}, {"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 148}, {"localized_name": "use_tiled_vae", "name": "use_tiled_vae", "type": "BOOLEAN", "widget": {"name": "use_tiled_vae"}, "link": null}, {"label": "encoder_tile_size", "localized_name": "encoder_tile_size", "name": "encoder_tile_size", "type": "INT", "widget": {"name": "encoder_tile_size"}, "link": 70}, {"label": "decoder_tile_size", "localized_name": "decoder_tile_size", "name": "decoder_tile_size", "type": "INT", "widget": {"name": "decoder_tile_size"}, "link": 71}, {"localized_name": "encoder_dtype", "name": "encoder_dtype", "type": "COMBO", "widget": {"name": "encoder_dtype"}, "link": null}], "outputs": [{"label": "SUPIR_VAE", "localized_name": "SUPIR_VAE", "name": "SUPIR_VAE", "type": "SUPIRVAE", "slot_index": 0, "links": [15]}, {"label": "denoised_image", "localized_name": "denoised_image", "name": "denoised_image", "type": "IMAGE", "slot_index": 1, "links": [16]}, {"label": "denoised_latents", "localized_name": "denoised_latents", "name": "denoised_latents", "type": "LATENT", "slot_index": 2, "links": [20]}], "properties": {"cnr_id": "comfy<PERSON>-su<PERSON>r", "ver": "1.0.1", "Node name for S&R": "SUPIR_first_stage"}, "widgets_values": [false, 512, 512, "auto"]}, {"id": 78, "type": "LayerUtility: ImageScaleByAspectRatio V2", "pos": [-788.8492431640625, -516.7731323242188], "size": [363.6851501464844, 330], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 147}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": null}, {"localized_name": "aspect_ratio", "name": "aspect_ratio", "type": "COMBO", "widget": {"name": "aspect_ratio"}, "link": null}, {"localized_name": "proportional_width", "name": "proportional_width", "type": "INT", "widget": {"name": "proportional_width"}, "link": null}, {"localized_name": "proportional_height", "name": "proportional_height", "type": "INT", "widget": {"name": "proportional_height"}, "link": null}, {"localized_name": "fit", "name": "fit", "type": "COMBO", "widget": {"name": "fit"}, "link": null}, {"localized_name": "method", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}, {"localized_name": "round_to_multiple", "name": "round_to_multiple", "type": "COMBO", "widget": {"name": "round_to_multiple"}, "link": null}, {"localized_name": "scale_to_side", "name": "scale_to_side", "type": "COMBO", "widget": {"name": "scale_to_side"}, "link": null}, {"localized_name": "scale_to_length", "name": "scale_to_length", "type": "INT", "widget": {"name": "scale_to_length"}, "link": null}, {"localized_name": "background_color", "name": "background_color", "type": "STRING", "widget": {"name": "background_color"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [148]}, {"localized_name": "mask", "name": "mask", "type": "MASK", "links": null}, {"localized_name": "original_size", "name": "original_size", "type": "BOX", "links": null}, {"localized_name": "width", "name": "width", "type": "INT", "links": null}, {"localized_name": "height", "name": "height", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerUtility: ImageScaleByAspectRatio V2"}, "widgets_values": ["original", 1, 1, "letterbox", "lanc<PERSON>s", "8", "None", 1024, "#000000"], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 22, "type": "CheckpointLoaderSimple", "pos": [-915.113037109375, -725.9596557617188], "size": [533.3355712890625, 117.2427978515625], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "Checkpoint名称", "name": "ckpt_name", "type": "COMBO", "widget": {"name": "ckpt_name"}, "link": null}], "outputs": [{"label": "MODEL", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [35]}, {"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [39]}, {"label": "VAE", "localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 2, "links": [40]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["juggernautXL_v9Rdphoto2Lightning.safetensors"]}, {"id": 77, "type": "LoadImage", "pos": [-1085.4168701171875, -494.23968505859375], "size": [270, 314], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [145, 147]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["02132-2759303082-cute, 1girl, close-up, wizard hat, pointing, (riding  mini white horse_1.3), white hair, red eyes, pantyhose, g (1).webp", "image"]}], "links": [[8, 9, 0, 7, 2, "SUPIR_cond_pos"], [9, 9, 1, 7, 3, "SUPIR_cond_neg"], [12, 7, 0, 10, 1, "LATENT"], [15, 5, 0, 11, 0, "SUPIRVAE"], [16, 5, 1, 11, 1, "IMAGE"], [17, 11, 0, 7, 1, "LATENT"], [20, 5, 2, 9, 1, "LATENT"], [22, 12, 0, 9, 0, "SUPIRMODEL"], [23, 12, 0, 7, 0, "SUPIRMODEL"], [26, 10, 0, 14, 1, "IMAGE"], [32, 19, 0, 10, 0, "SUPIRVAE"], [35, 22, 0, 21, 0, "MODEL"], [39, 22, 1, 21, 1, "CLIP"], [40, 22, 2, 21, 2, "VAE"], [44, 21, 0, 12, 0, "*"], [45, 21, 1, 17, 0, "SUPIRVAE"], [46, 17, 0, 5, 0, "SUPIRVAE"], [58, 29, 0, 7, 4, "INT"], [70, 36, 0, 5, 3, "INT"], [71, 36, 0, 5, 4, "INT"], [72, 36, 0, 11, 3, "INT"], [73, 36, 0, 7, 16, "INT"], [74, 37, 0, 7, 17, "INT"], [75, 36, 0, 10, 3, "INT"], [145, 77, 0, 14, 0, "IMAGE"], [147, 77, 0, 78, 0, "IMAGE"], [148, 78, 0, 5, 1, "IMAGE"]], "groups": [{"id": 1, "title": "su<PERSON>r 高清", "bounding": [-1152, -868, 2673, 743], "color": "#3f789e", "font_size": 36, "flags": {}}, {"id": 3, "title": "分块分辨率", "bounding": [-314, -468, 282, 263], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 8, "title": "提示词", "bounding": [116, -509, 307, 368], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.8769226950000042, "offset": [1093.0512083197193, 1114.6335318592228]}, "workspace_info": {"saveLock": false, "id": "nucKMKAHsaX8AA4h-sYlU"}, "0246.VERSION": [0, 0, 4]}, "version": 0.4}