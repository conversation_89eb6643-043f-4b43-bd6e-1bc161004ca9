{"id": "ce2cb810-7775-4564-8928-dd5bed1053cd", "revision": 0, "last_node_id": 55, "last_link_id": 130, "nodes": [{"id": 20, "type": "VAEEncode", "pos": [1329.880859375, 701.230224609375], "size": [210, 46], "flags": {}, "order": 14, "mode": 0, "inputs": [{"localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 117}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 22}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "links": [86]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "VAEEncode"}, "widgets_values": [], "color": "#322", "bgcolor": "#533"}, {"id": 17, "type": "CLIPVisionEncode", "pos": [1228.9832763671875, 525.7402954101562], "size": [380.4000244140625, 78], "flags": {}, "order": 13, "mode": 0, "inputs": [{"localized_name": "clip视觉", "name": "clip_vision", "type": "CLIP_VISION", "link": 18}, {"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 116}, {"localized_name": "裁剪", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"localized_name": "CLIP视觉输出", "name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "links": [83]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["center"], "color": "#233", "bgcolor": "#355"}, {"id": 18, "type": "CLIPVisionLoader", "pos": [511.98028564453125, 530.965576171875], "size": [388.87139892578125, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "clip名称", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}], "outputs": [{"localized_name": "CLIP视觉", "name": "CLIP_VISION", "type": "CLIP_VISION", "links": [18]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPVisionLoader"}, "widgets_values": ["sigclip_vision_patch14_384.safetensors"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 48, "type": "GetImageSizeAndCount", "pos": [1266.1427001953125, 844.8764038085938], "size": [277.20001220703125, 86], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 125}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [116, 117]}, {"label": "1328 width", "localized_name": "width", "name": "width", "type": "INT", "links": null}, {"label": "768 height", "localized_name": "height", "name": "height", "type": "INT", "links": null}, {"label": "1 count", "localized_name": "count", "name": "count", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "8ecf5cd05e0a1012087b0da90eea9a13674668db", "Node name for S&R": "GetImageSizeAndCount"}, "widgets_values": []}, {"id": 50, "type": "ImageResize+", "pos": [921.9315795898438, 701.9561767578125], "size": [315, 218], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 122}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 128}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 127}, {"localized_name": "interpolation", "name": "interpolation", "type": "COMBO", "widget": {"name": "interpolation"}, "link": null}, {"localized_name": "method", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}, {"localized_name": "condition", "name": "condition", "type": "COMBO", "widget": {"name": "condition"}, "link": null}, {"localized_name": "multiple_of", "name": "multiple_of", "type": "INT", "widget": {"name": "multiple_of"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [125]}, {"localized_name": "width", "name": "width", "type": "INT", "links": null}, {"localized_name": "height", "name": "height", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_essentials", "ver": "76e9d1e4399bd025ce8b12c290753d58f9f53e93", "Node name for S&R": "ImageResize+", "aux_id": "kijai/ComfyUI_essentials"}, "widgets_values": [512, 512, "lanc<PERSON>s", "stretch", "always", 0]}, {"id": 54, "type": "DownloadAndLoadFramePackModel", "pos": [1256.5235595703125, -277.76226806640625], "size": [315, 130], "flags": {}, "order": 1, "mode": 4, "inputs": [{"localized_name": "compile_args", "name": "compile_args", "shape": 7, "type": "FRAMEPACKCOMPILEARGS", "link": null}, {"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "base_precision", "name": "base_precision", "type": "COMBO", "widget": {"name": "base_precision"}, "link": null}, {"localized_name": "quantization", "name": "quantization", "type": "COMBO", "widget": {"name": "quantization"}, "link": null}, {"localized_name": "attention_mode", "name": "attention_mode", "shape": 7, "type": "COMBO", "widget": {"name": "attention_mode"}, "link": null}], "outputs": [{"localized_name": "model", "name": "model", "type": "FramePackMODEL", "links": null}], "properties": {"aux_id": "kijai/ComfyUI-FramePackWrapper", "ver": "49fe507eca8246cc9d08a8093892f40c1180e88f", "Node name for S&R": "DownloadAndLoadFramePackModel"}, "widgets_values": ["lllyasviel/FramePackI2V_HY", "bf16", "disabled", "sdpa"]}, {"id": 55, "type": "<PERSON>downNote", "pos": [567.05908203125, -628.8865966796875], "size": [459.8609619140625, 285.9714660644531], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Model links:\n\n[https://huggingface.co/Kijai/HunyuanVideo_comfy/blob/main/FramePackI2V_HY_fp8_e4m3fn.safetensors](https://huggingface.co/Kijai/HunyuanVideo_comfy/blob/main/FramePackI2V_HY_fp8_e4m3fn.safetensors)\n\n[https://huggingface.co/Kijai/HunyuanVideo_comfy/blob/main/FramePackI2V_HY_bf16.safetensors](https://huggingface.co/Kijai/HunyuanVideo_comfy/blob/main/FramePackI2V_HY_bf16.safetensors)\n\nsigclip:\n\n[https://huggingface.co/Comfy-Org/sigclip_vision_384/tree/main](https://huggingface.co/Comfy-Org/sigclip_vision_384/tree/main)\n\ntext encoder and VAE:\n\n[https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged/tree/main/split_files](https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged/tree/main/split_files)"], "color": "#432", "bgcolor": "#653"}, {"id": 13, "type": "DualCLIPLoader", "pos": [257.7228698730469, 271.56097412109375], "size": [340.2243957519531, 130], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "CLIP名称1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "CLIP名称2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": [102]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["clip_l.safetensors", "llava_llama3_fp16.safetensors", "hunyuan_video", "default"], "color": "#432", "bgcolor": "#653"}, {"id": 19, "type": "LoadImage", "pos": [221.91770935058594, 702.9730834960938], "size": [315, 314], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [122, 126]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "LoadImage"}, "widgets_values": ["ComfyUI_temp_yrcht_00004_.png", "image"]}, {"id": 51, "type": "FramePackFindNearestBucket", "pos": [578.9364624023438, 773.94677734375], "size": [315, 78], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 126}, {"localized_name": "base_resolution", "name": "base_resolution", "type": "INT", "widget": {"name": "base_resolution"}, "link": null}], "outputs": [{"localized_name": "width", "name": "width", "type": "INT", "links": [128]}, {"localized_name": "height", "name": "height", "type": "INT", "links": [127]}], "properties": {"aux_id": "kijai/ComfyUI-FramePackWrapper", "ver": "4f9030a9f4c0bd67d86adf3d3dc07e37118c40bd", "Node name for S&R": "FramePackFindNearestBucket"}, "widgets_values": [1024]}, {"id": 47, "type": "CLIPTextEncode", "pos": [758.1417236328125, 250.06182861328125], "size": [400, 200], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 102}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [114, 118]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["A high-tech powerhouse with a rotating, glowing globe in the middle", [false, true]], "color": "#232", "bgcolor": "#353"}, {"id": 27, "type": "FramePackTorchCompileSettings", "pos": [606.052001953125, -45.00597381591797], "size": [531.5999755859375, 202], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "backend", "name": "backend", "type": "COMBO", "widget": {"name": "backend"}, "link": null}, {"localized_name": "fullgraph", "name": "fullgraph", "type": "BOOLEAN", "widget": {"name": "fullgraph"}, "link": null}, {"localized_name": "mode", "name": "mode", "type": "COMBO", "widget": {"name": "mode"}, "link": null}, {"localized_name": "dynamic", "name": "dynamic", "type": "BOOLEAN", "widget": {"name": "dynamic"}, "link": null}, {"localized_name": "dynamo_cache_size_limit", "name": "dynamo_cache_size_limit", "type": "INT", "widget": {"name": "dynamo_cache_size_limit"}, "link": null}, {"localized_name": "compile_single_blocks", "name": "compile_single_blocks", "type": "BOOLEAN", "widget": {"name": "compile_single_blocks"}, "link": null}, {"localized_name": "compile_double_blocks", "name": "compile_double_blocks", "type": "BOOLEAN", "widget": {"name": "compile_double_blocks"}, "link": null}], "outputs": [{"localized_name": "torch_compile_args", "name": "torch_compile_args", "type": "FRAMEPACKCOMPILEARGS", "links": []}], "properties": {"aux_id": "lllyasviel/FramePack", "ver": "0e5fe5d7ca13c76fb8e13708f4b92e7c7a34f20c", "Node name for S&R": "FramePackTorchCompileSettings"}, "widgets_values": ["inductor", false, "default", false, 64, true, true]}, {"id": 12, "type": "VAELoader", "pos": [649.27392578125, -173.91029357910156], "size": [469.0488586425781, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": [22, 62]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "VAELoader"}, "widgets_values": ["hunyuan_video_vae_bf16.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 15, "type": "ConditioningZeroOut", "pos": [1380.2691650390625, 365.0367126464844], "size": [317.4000244140625, 26], "flags": {"collapsed": false}, "order": 10, "mode": 0, "inputs": [{"localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 118}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [108]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "ConditioningZeroOut"}, "widgets_values": [], "color": "#332922", "bgcolor": "#593930"}, {"id": 52, "type": "LoadFramePackModel", "pos": [1213.90625, 33.53042984008789], "size": [480.7601013183594, 174], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "compile_args", "name": "compile_args", "shape": 7, "type": "FRAMEPACKCOMPILEARGS", "link": null}, {"localized_name": "lora", "name": "lora", "shape": 7, "type": "FPLORA", "link": null}, {"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "base_precision", "name": "base_precision", "type": "COMBO", "widget": {"name": "base_precision"}, "link": null}, {"localized_name": "quantization", "name": "quantization", "type": "COMBO", "widget": {"name": "quantization"}, "link": null}, {"localized_name": "load_device", "name": "load_device", "type": "COMBO", "widget": {"name": "load_device"}, "link": null}, {"localized_name": "attention_mode", "name": "attention_mode", "shape": 7, "type": "COMBO", "widget": {"name": "attention_mode"}, "link": null}], "outputs": [{"localized_name": "model", "name": "model", "type": "FramePackMODEL", "links": [129]}], "properties": {"aux_id": "kijai/ComfyUI-FramePackWrapper", "ver": "49fe507eca8246cc9d08a8093892f40c1180e88f", "Node name for S&R": "LoadFramePackModel"}, "widgets_values": ["FramePackI2V_HY_fp8_e4m3fn.safetensors", "bf16", "fp8_e4m3fn", "main_device", "sdpa"]}, {"id": 39, "type": "FramePackSampler", "pos": [1740.602783203125, 169.78778076171875], "size": [393, 852.631591796875], "flags": {}, "order": 15, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "FramePackMODEL", "link": 129}, {"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 114}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 108}, {"localized_name": "start_latent", "name": "start_latent", "type": "LATENT", "link": 86}, {"localized_name": "image_embeds", "name": "image_embeds", "shape": 7, "type": "CLIP_VISION_OUTPUT", "link": 83}, {"localized_name": "end_latent", "name": "end_latent", "shape": 7, "type": "LATENT", "link": null}, {"localized_name": "end_image_embeds", "name": "end_image_embeds", "shape": 7, "type": "CLIP_VISION_OUTPUT", "link": null}, {"localized_name": "initial_samples", "name": "initial_samples", "shape": 7, "type": "LATENT", "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "use_teacache", "name": "use_teacache", "type": "BOOLEAN", "widget": {"name": "use_teacache"}, "link": null}, {"localized_name": "teacache_rel_l1_thresh", "name": "teacache_rel_l1_thresh", "type": "FLOAT", "widget": {"name": "teacache_rel_l1_thresh"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "guidance_scale", "name": "guidance_scale", "type": "FLOAT", "widget": {"name": "guidance_scale"}, "link": null}, {"localized_name": "shift", "name": "shift", "type": "FLOAT", "widget": {"name": "shift"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "latent_window_size", "name": "latent_window_size", "type": "INT", "widget": {"name": "latent_window_size"}, "link": null}, {"localized_name": "total_second_length", "name": "total_second_length", "type": "FLOAT", "widget": {"name": "total_second_length"}, "link": null}, {"localized_name": "gpu_memory_preservation", "name": "gpu_memory_preservation", "type": "FLOAT", "widget": {"name": "gpu_memory_preservation"}, "link": null}, {"localized_name": "sampler", "name": "sampler", "type": "COMBO", "widget": {"name": "sampler"}, "link": null}, {"localized_name": "embed_interpolation", "name": "embed_interpolation", "shape": 7, "type": "COMBO", "widget": {"name": "embed_interpolation"}, "link": null}, {"localized_name": "start_embed_strength", "name": "start_embed_strength", "shape": 7, "type": "FLOAT", "widget": {"name": "start_embed_strength"}, "link": null}, {"localized_name": "denoise_strength", "name": "denoise_strength", "shape": 7, "type": "FLOAT", "widget": {"name": "denoise_strength"}, "link": null}], "outputs": [{"localized_name": "samples", "name": "samples", "type": "LATENT", "links": [85]}], "properties": {"aux_id": "kijai/ComfyUI-FramePackWrapper", "ver": "8e5ec6b7f3acf88255c5d93d062079f18b43aa2b", "Node name for S&R": "FramePackSampler"}, "widgets_values": [30, true, 0.15, 1, 10, 0, 47, "fixed", 9, 5, 6, "unipc_bh1", "linear", 0, 1]}, {"id": 33, "type": "VAEDecodeTiled", "pos": [1767.0732421875, -45.76095199584961], "size": [315, 150], "flags": {}, "order": 16, "mode": 0, "inputs": [{"localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 85}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 62}, {"localized_name": "分块尺寸", "name": "tile_size", "type": "INT", "widget": {"name": "tile_size"}, "link": null}, {"localized_name": "重叠", "name": "overlap", "type": "INT", "widget": {"name": "overlap"}, "link": null}, {"localized_name": "时间尺寸", "name": "temporal_size", "type": "INT", "widget": {"name": "temporal_size"}, "link": null}, {"localized_name": "时间重叠", "name": "temporal_overlap", "type": "INT", "widget": {"name": "temporal_overlap"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [96]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "VAEDecodeTiled"}, "widgets_values": [256, 64, 64, 8], "color": "#322", "bgcolor": "#533"}, {"id": 44, "type": "GetImageSizeAndCount", "pos": [2147.5146484375, -32.2537727355957], "size": [277.20001220703125, 86], "flags": {}, "order": 17, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 96}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [97]}, {"label": "608 width", "localized_name": "width", "name": "width", "type": "INT", "links": null}, {"label": "640 height", "localized_name": "height", "name": "height", "type": "INT", "links": null}, {"label": "145 count", "localized_name": "count", "name": "count", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "8ecf5cd05e0a1012087b0da90eea9a13674668db", "Node name for S&R": "GetImageSizeAndCount"}, "widgets_values": []}, {"id": 23, "type": "VHS_VideoCombine", "pos": [2219.645263671875, 122.3675765991211], "size": [908.428955078125, 334], "flags": {}, "order": 18, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": 97}, {"localized_name": "audio", "name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"localized_name": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"localized_name": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}, {"localized_name": "frame_rate", "name": "frame_rate", "type": "FLOAT", "widget": {"name": "frame_rate"}, "link": null}, {"localized_name": "loop_count", "name": "loop_count", "type": "INT", "widget": {"name": "loop_count"}, "link": null}, {"localized_name": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}, {"localized_name": "format", "name": "format", "type": "COMBO", "widget": {"name": "format"}, "link": null}, {"localized_name": "pingpong", "name": "pingpong", "type": "BOOLEAN", "widget": {"name": "pingpong"}, "link": null}, {"localized_name": "save_output", "name": "save_output", "type": "BOOLEAN", "widget": {"name": "save_output"}, "link": null}], "outputs": [{"localized_name": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "FramePack", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "FramePack_00057.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 24, "workflow": "FramePack_00057.png", "fullpath": "N:\\AI\\ComfyUI\\temp\\FramePack_00057.mp4"}}}}], "links": [[18, 18, 0, 17, 0, "CLIP_VISION"], [22, 12, 0, 20, 1, "VAE"], [62, 12, 0, 33, 1, "VAE"], [83, 17, 0, 39, 4, "CLIP_VISION_OUTPUT"], [85, 39, 0, 33, 0, "LATENT"], [86, 20, 0, 39, 3, "LATENT"], [96, 33, 0, 44, 0, "IMAGE"], [97, 44, 0, 23, 0, "IMAGE"], [102, 13, 0, 47, 0, "CLIP"], [108, 15, 0, 39, 2, "CONDITIONING"], [114, 47, 0, 39, 1, "CONDITIONING"], [116, 48, 0, 17, 1, "IMAGE"], [117, 48, 0, 20, 0, "IMAGE"], [118, 47, 0, 15, 0, "CONDITIONING"], [122, 19, 0, 50, 0, "IMAGE"], [125, 50, 0, 48, 0, "IMAGE"], [126, 19, 0, 51, 0, "IMAGE"], [127, 51, 1, 50, 2, "INT"], [128, 51, 0, 50, 1, "INT"], [129, 52, 0, 39, 0, "FramePackMODEL"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.6727499949325823, "offset": [-101.98335175553812, 539.7905569391395]}, "frontendVersion": "1.18.10", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}