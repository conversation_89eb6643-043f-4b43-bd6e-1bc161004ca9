{"id": "a078e052-2109-4c7e-91e8-253993b93ee5", "revision": 0, "last_node_id": 60, "last_link_id": 87, "nodes": [{"id": 57, "type": "PreviewImage", "pos": [1099.0391845703125, 234.31687927246094], "size": [578.784423828125, 555.8528442382812], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 83}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 19, "type": "UNETLoader", "pos": [70.00119018554688, 558.5648803710938], "size": [315, 82], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "UNet名称", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "数据类型", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [28, 49]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev-fp8.safetensors", "fp8_e4m3fn"]}, {"id": 30, "type": "BasicScheduler", "pos": [62.461673736572266, 424.43035888671875], "size": [315, 106], "flags": {}, "order": 5, "mode": 0, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 49}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"label": "降噪", "localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"label": "Sigmas", "localized_name": "Sigmas", "name": "SIGMAS", "type": "SIGMAS", "links": [38]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 20, 0.9000000000000001]}, {"id": 22, "type": "SamplerCustom", "pos": [404.53912353515625, 211.76583862304688], "size": [424.28216552734375, 547.2218017578125], "flags": {}, "order": 9, "mode": 0, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 28}, {"label": "正面条件", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 81}, {"label": "负面条件", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 82}, {"label": "采样器", "localized_name": "采样器", "name": "sampler", "type": "SAMPLER", "link": 37}, {"label": "Sigmas", "localized_name": "Sigmas", "name": "sigmas", "type": "SIGMAS", "link": 38}, {"label": "Latent", "localized_name": "Latent", "name": "latent_image", "type": "LATENT", "link": 86}, {"localized_name": "添加噪波", "name": "add_noise", "type": "BOOLEAN", "widget": {"name": "add_noise"}, "link": null}, {"localized_name": "噪波种子", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}], "outputs": [{"label": "输出", "localized_name": "Latent", "name": "output", "type": "LATENT", "links": [46]}, {"label": "降噪输出", "localized_name": "降噪Latent", "name": "denoised_output", "type": "LATENT", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "SamplerCustom"}, "widgets_values": [true, 1068701138486853, "randomize", 3.5]}, {"id": 58, "type": "CLIPTextEncode", "pos": [-387.1260070800781, 204.56773376464844], "size": [400, 200], "flags": {"collapsed": false}, "order": 7, "mode": 0, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 84}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [85]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 23, "type": "CLIPTextEncode", "pos": [-387.20550537109375, 476.0742492675781], "size": [400, 200], "flags": {"collapsed": false}, "order": 6, "mode": 0, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 26}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [82]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 31, "type": "FluxGuidance", "pos": [72.50950622558594, 310.2338562011719], "size": [315, 58], "flags": {"collapsed": false}, "order": 8, "mode": 0, "inputs": [{"label": "条件", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 85}, {"localized_name": "引导", "name": "guidance", "type": "FLOAT", "widget": {"name": "guidance"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [81]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "FluxGuidance"}, "widgets_values": [3.5]}, {"id": 20, "type": "DualCLIPLoader", "pos": [-805.************, 320.4905700683594], "size": [315, 130], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "CLIP名称1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "CLIP名称2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": [26, 84]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 29, "type": "KSamplerSelect", "pos": [78.22371673583984, 193.5703887939453], "size": [315, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}], "outputs": [{"label": "采样器", "localized_name": "采样器", "name": "SAMPLER", "type": "SAMPLER", "links": [37]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 59, "type": "EmptyLatentImage", "pos": [92.68653106689453, 689.28125], "size": [270, 106], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "批量大小", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}, "link": null}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "links": [86]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "EmptyLatentImage"}, "widgets_values": [512, 512, 1]}, {"id": 35, "type": "VAEDecode", "pos": [863.1451416015625, 217.61032104492188], "size": [210, 46], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "Latent", "localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 46}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 87}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [48, 83]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 60, "type": "VAELoader", "pos": [111.06365966796875, 64.36797332763672], "size": [270, 58], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": [87]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}], "links": [[26, 20, 0, 23, 0, "CLIP"], [28, 19, 0, 22, 0, "MODEL"], [37, 29, 0, 22, 3, "SAMPLER"], [38, 30, 0, 22, 4, "SIGMAS"], [46, 22, 0, 35, 0, "LATENT"], [49, 19, 0, 30, 0, "MODEL"], [81, 31, 0, 22, 1, "CONDITIONING"], [82, 23, 0, 22, 2, "CONDITIONING"], [83, 35, 0, 57, 0, "IMAGE"], [84, 20, 0, 58, 0, "CLIP"], [85, 58, 0, 31, 0, "CONDITIONING"], [86, 59, 0, 22, 5, "LATENT"], [87, 60, 0, 35, 1, "VAE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.45, "offset": [551.2966996227747, 1075.514529780356]}, "frontendVersion": "1.18.9", "ue_links": [], "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true, "0246.VERSION": [0, 0, 4]}, "version": 0.4}