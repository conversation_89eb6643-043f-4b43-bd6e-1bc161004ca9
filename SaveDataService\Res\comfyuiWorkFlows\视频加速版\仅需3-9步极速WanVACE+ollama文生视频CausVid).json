{"id": "30f87672-f453-401d-8e1e-dfa85254e4a2", "revision": 0, "last_node_id": 100, "last_link_id": 105, "nodes": [{"id": 28, "type": "WanVideoDecode", "pos": [1915.41943359375, -1142.79736328125], "size": [315, 174], "flags": {}, "order": 24, "mode": 0, "inputs": [{"label": "vae", "localized_name": "vae", "name": "vae", "type": "WANVAE", "link": 43}, {"label": "samples", "localized_name": "samples", "name": "samples", "type": "LATENT", "link": 33}, {"localized_name": "enable_vae_tiling", "name": "enable_vae_tiling", "type": "BOOLEAN", "widget": {"name": "enable_vae_tiling"}, "link": null}, {"localized_name": "tile_x", "name": "tile_x", "type": "INT", "widget": {"name": "tile_x"}, "link": null}, {"localized_name": "tile_y", "name": "tile_y", "type": "INT", "widget": {"name": "tile_y"}, "link": null}, {"localized_name": "tile_stride_x", "name": "tile_stride_x", "type": "INT", "widget": {"name": "tile_stride_x"}, "link": null}, {"localized_name": "tile_stride_y", "name": "tile_stride_y", "type": "INT", "widget": {"name": "tile_stride_y"}, "link": null}], "outputs": [{"label": "images", "localized_name": "images", "name": "images", "type": "IMAGE", "slot_index": 0, "links": [36]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "fa7015315a484c15eb79f41744ef501c728dc9d4", "Node name for S&R": "WanVideoDecode"}, "widgets_values": [true, 272, 272, 144, 128]}, {"id": 71, "type": "easy cleanGpuUsed", "pos": [961.2777099609375, -583.990966796875], "size": [210, 26], "flags": {"collapsed": true}, "order": 17, "mode": 0, "inputs": [{"label": "anything", "localized_name": "输入任何", "name": "anything", "type": "*", "link": 77}], "outputs": [{"label": "output", "localized_name": "output", "name": "output", "type": "*", "slot_index": 0, "links": [76]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy cleanGpuUsed"}, "widgets_values": []}, {"id": 66, "type": "easy cleanGpuUsed", "pos": [1303.1878662109375, -596.536376953125], "size": [210, 26], "flags": {"collapsed": true}, "order": 22, "mode": 0, "inputs": [{"label": "anything", "localized_name": "输入任何", "name": "anything", "type": "*", "link": 68}], "outputs": [{"label": "output", "localized_name": "output", "name": "output", "type": "*", "slot_index": 0, "links": [70]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy cleanGpuUsed"}, "widgets_values": []}, {"id": 37, "type": "WanVideoEmptyEmbeds", "pos": [1540.9771728515625, -1415.0899658203125], "size": [252.78868103027344, 158], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "control_embeds", "localized_name": "control_embeds", "name": "control_embeds", "shape": 7, "type": "WANVIDIMAGE_EMBEDS"}, {"label": "width", "localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 95}, {"label": "height", "localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 96}, {"label": "num_frames", "localized_name": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": 97}], "outputs": [{"label": "image_embeds", "localized_name": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "slot_index": 0, "links": [71]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "fa7015315a484c15eb79f41744ef501c728dc9d4", "Node name for S&R": "WanVideoEmptyEmbeds"}, "widgets_values": [832, 480, 81]}, {"id": 38, "type": "WanVideoVAELoader", "pos": [1862.26904296875, -1308.77978515625], "size": [349.28662109375, 86.18130493164062], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "model_name", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}, {"localized_name": "precision", "name": "precision", "shape": 7, "type": "COMBO", "widget": {"name": "precision"}, "link": null}], "outputs": [{"label": "vae", "localized_name": "vae", "name": "vae", "type": "WANVAE", "slot_index": 0, "links": [43]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "fa7015315a484c15eb79f41744ef501c728dc9d4", "Node name for S&R": "WanVideoVAELoader"}, "widgets_values": ["Wan2_1_VAE_bf16.safetensors", "bf16"]}, {"id": 16, "type": "WanVideoTextEncode", "pos": [987.4197998046875, -839.2832641601562], "size": [406.05181884765625, 186], "flags": {}, "order": 21, "mode": 0, "inputs": [{"label": "t5", "localized_name": "t5", "name": "t5", "type": "WANTEXTENCODER", "link": 76}, {"label": "model_to_offload", "localized_name": "model_to_offload", "name": "model_to_offload", "shape": 7, "type": "WANVIDEOMODEL"}, {"label": "positive_prompt", "localized_name": "positive_prompt", "name": "positive_prompt", "type": "STRING", "widget": {"name": "positive_prompt"}, "link": 103}, {"localized_name": "negative_prompt", "name": "negative_prompt", "type": "STRING", "widget": {"name": "negative_prompt"}, "link": null}, {"localized_name": "force_offload", "name": "force_offload", "shape": 7, "type": "BOOLEAN", "widget": {"name": "force_offload"}, "link": null}], "outputs": [{"label": "text_embeds", "localized_name": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "slot_index": 0, "links": [68]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "fa7015315a484c15eb79f41744ef501c728dc9d4", "Node name for S&R": "WanVideoTextEncode"}, "widgets_values": ["", "色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走", true, [false, true], [false, true]]}, {"id": 35, "type": "WanVideoTorchCompileSettings", "pos": [1043.4482421875, -1418.3040771484375], "size": [390.5999755859375, 202], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "backend", "name": "backend", "type": "COMBO", "widget": {"name": "backend"}, "link": null}, {"localized_name": "fullgraph", "name": "fullgraph", "type": "BOOLEAN", "widget": {"name": "fullgraph"}, "link": null}, {"localized_name": "mode", "name": "mode", "type": "COMBO", "widget": {"name": "mode"}, "link": null}, {"localized_name": "dynamic", "name": "dynamic", "type": "BOOLEAN", "widget": {"name": "dynamic"}, "link": null}, {"localized_name": "dynamo_cache_size_limit", "name": "dynamo_cache_size_limit", "type": "INT", "widget": {"name": "dynamo_cache_size_limit"}, "link": null}, {"localized_name": "compile_transformer_blocks_only", "name": "compile_transformer_blocks_only", "type": "BOOLEAN", "widget": {"name": "compile_transformer_blocks_only"}, "link": null}, {"localized_name": "dynamo_recompile_limit", "name": "dynamo_recompile_limit", "shape": 7, "type": "INT", "widget": {"name": "dynamo_recompile_limit"}, "link": null}], "outputs": [{"label": "torch_compile_args", "localized_name": "torch_compile_args", "name": "torch_compile_args", "type": "WANCOMPILEARGS", "slot_index": 0, "links": [84]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "fa7015315a484c15eb79f41744ef501c728dc9d4", "Node name for S&R": "WanVideoTorchCompileSettings"}, "widgets_values": ["inductor", false, "default", false, 64, true, 128]}, {"id": 27, "type": "WanVideoSampler", "pos": [1542.003173828125, -1173.8759765625], "size": [315, 558], "flags": {}, "order": 23, "mode": 0, "inputs": [{"label": "model", "localized_name": "model", "name": "model", "type": "WANVIDEOMODEL", "link": 29}, {"label": "text_embeds", "localized_name": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "link": 70}, {"label": "image_embeds", "localized_name": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "link": 71}, {"label": "samples", "localized_name": "samples", "name": "samples", "shape": 7, "type": "LATENT"}, {"label": "feta_args", "localized_name": "feta_args", "name": "feta_args", "shape": 7, "type": "FETAARGS"}, {"label": "context_options", "localized_name": "context_options", "name": "context_options", "shape": 7, "type": "WANVIDCONTEXT"}, {"label": "teacache_args", "localized_name": "teacache_args", "name": "teacache_args", "shape": 7, "type": "TEACACHEARGS"}, {"label": "flowedit_args", "localized_name": "flowedit_args", "name": "flowedit_args", "shape": 7, "type": "FLOWEDITARGS"}, {"label": "slg_args", "localized_name": "slg_args", "name": "slg_args", "shape": 7, "type": "SLGARGS"}, {"label": "loop_args", "localized_name": "loop_args", "name": "loop_args", "shape": 7, "type": "LOOPARGS"}, {"label": "experimental_args", "localized_name": "experimental_args", "name": "experimental_args", "shape": 7, "type": "EXPERIMENTALARGS"}, {"label": "sigmas", "localized_name": "sigmas", "name": "sigmas", "shape": 7, "type": "SIGMAS"}, {"label": "unianimate_poses", "localized_name": "unianimate_poses", "name": "unianimate_poses", "shape": 7, "type": "UNIANIMATE_POSE"}, {"label": "fantasytalking_embeds", "localized_name": "fantasytalking_embeds", "name": "fantasytalking_embeds", "shape": 7, "type": "FANTASYTALKING_EMBEDS"}, {"label": "steps", "localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": 98}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "shift", "name": "shift", "type": "FLOAT", "widget": {"name": "shift"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "force_offload", "name": "force_offload", "type": "BOOLEAN", "widget": {"name": "force_offload"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "riflex_freq_index", "name": "riflex_freq_index", "type": "INT", "widget": {"name": "riflex_freq_index"}, "link": null}, {"localized_name": "denoise_strength", "name": "denoise_strength", "shape": 7, "type": "FLOAT", "widget": {"name": "denoise_strength"}, "link": null}, {"localized_name": "batched_cfg", "name": "batched_cfg", "shape": 7, "type": "BOOLEAN", "widget": {"name": "batched_cfg"}, "link": null}, {"localized_name": "rope_function", "name": "rope_function", "shape": 7, "type": "COMBO", "widget": {"name": "rope_function"}, "link": null}], "outputs": [{"label": "samples", "localized_name": "samples", "name": "samples", "type": "LATENT", "slot_index": 0, "links": [33]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "fa7015315a484c15eb79f41744ef501c728dc9d4", "Node name for S&R": "WanVideoSampler"}, "widgets_values": [15, 1.0000000000000002, 8.000000000000002, 466685929795204, "randomize", true, "unipc", 0, 1, false, "default"]}, {"id": 91, "type": "easy int", "pos": [1092.574951171875, -1560.9678955078125], "size": [270, 58], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "值", "name": "value", "type": "INT", "widget": {"name": "value"}, "link": null}], "outputs": [{"localized_name": "整数", "name": "int", "type": "INT", "links": [95]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy int"}, "widgets_values": [848]}, {"id": 92, "type": "easy int", "pos": [1381.441650390625, -1561.8677978515625], "size": [270, 58], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "值", "name": "value", "type": "INT", "widget": {"name": "value"}, "link": null}], "outputs": [{"localized_name": "整数", "name": "int", "type": "INT", "links": [96]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy int"}, "widgets_values": [480]}, {"id": 94, "type": "easy int", "pos": [596.7750244140625, -986.5343627929688], "size": [270, 58], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "值", "name": "value", "type": "INT", "widget": {"name": "value"}, "link": null}], "outputs": [{"localized_name": "整数", "name": "int", "type": "INT", "links": [98]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy int"}, "widgets_values": [9]}, {"id": 55, "type": "Fast Groups Bypasser (rgthree)", "pos": [196.51707458496094, -1283.61083984375], "size": [297.80706787109375, 182.35092163085938], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"label": "OPT_CONNECTION", "name": "OPT_CONNECTION", "type": "*"}], "properties": {"matchColors": "", "matchTitle": "", "showNav": true, "sort": "position", "customSortAlphabet": "", "toggleRestriction": "default"}}, {"id": 73, "type": "Text Multiline", "pos": [789.0775146484375, -1783.52978515625], "size": [317.0408935546875, 149.82444763183594], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"label": "STRING", "localized_name": "字符串", "name": "STRING", "type": "STRING", "links": [100]}], "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "1.0.2", "Node name for S&R": "Text Multiline"}, "widgets_values": ["中国国风，古风美少女，旗袍，转身看向镜头", [false, true]]}, {"id": 100, "type": "OllamaOptionsV2", "pos": [448.67498779296875, -2143.98828125], "size": [286.103515625, 754], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "enable_mirostat", "name": "enable_mirostat", "type": "BOOLEAN", "widget": {"name": "enable_mirostat"}, "link": null}, {"localized_name": "mi<PERSON><PERSON>", "name": "mi<PERSON><PERSON>", "type": "INT", "widget": {"name": "mi<PERSON><PERSON>"}, "link": null}, {"localized_name": "enable_mirostat_eta", "name": "enable_mirostat_eta", "type": "BOOLEAN", "widget": {"name": "enable_mirostat_eta"}, "link": null}, {"localized_name": "mirostat_eta", "name": "mirostat_eta", "type": "FLOAT", "widget": {"name": "mirostat_eta"}, "link": null}, {"localized_name": "enable_mirostat_tau", "name": "enable_mirostat_tau", "type": "BOOLEAN", "widget": {"name": "enable_mirostat_tau"}, "link": null}, {"localized_name": "mirostat_tau", "name": "mirostat_tau", "type": "FLOAT", "widget": {"name": "mirostat_tau"}, "link": null}, {"localized_name": "enable_num_ctx", "name": "enable_num_ctx", "type": "BOOLEAN", "widget": {"name": "enable_num_ctx"}, "link": null}, {"localized_name": "num_ctx", "name": "num_ctx", "type": "INT", "widget": {"name": "num_ctx"}, "link": null}, {"localized_name": "enable_repeat_last_n", "name": "enable_repeat_last_n", "type": "BOOLEAN", "widget": {"name": "enable_repeat_last_n"}, "link": null}, {"localized_name": "repeat_last_n", "name": "repeat_last_n", "type": "INT", "widget": {"name": "repeat_last_n"}, "link": null}, {"localized_name": "enable_repeat_penalty", "name": "enable_repeat_penalty", "type": "BOOLEAN", "widget": {"name": "enable_repeat_penalty"}, "link": null}, {"localized_name": "repeat_penalty", "name": "repeat_penalty", "type": "FLOAT", "widget": {"name": "repeat_penalty"}, "link": null}, {"localized_name": "enable_temperature", "name": "enable_temperature", "type": "BOOLEAN", "widget": {"name": "enable_temperature"}, "link": null}, {"localized_name": "temperature", "name": "temperature", "type": "FLOAT", "widget": {"name": "temperature"}, "link": null}, {"localized_name": "enable_seed", "name": "enable_seed", "type": "BOOLEAN", "widget": {"name": "enable_seed"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "enable_stop", "name": "enable_stop", "type": "BOOLEAN", "widget": {"name": "enable_stop"}, "link": null}, {"localized_name": "stop", "name": "stop", "type": "STRING", "widget": {"name": "stop"}, "link": null}, {"localized_name": "enable_tfs_z", "name": "enable_tfs_z", "type": "BOOLEAN", "widget": {"name": "enable_tfs_z"}, "link": null}, {"localized_name": "tfs_z", "name": "tfs_z", "type": "FLOAT", "widget": {"name": "tfs_z"}, "link": null}, {"localized_name": "enable_num_predict", "name": "enable_num_predict", "type": "BOOLEAN", "widget": {"name": "enable_num_predict"}, "link": null}, {"localized_name": "num_predict", "name": "num_predict", "type": "INT", "widget": {"name": "num_predict"}, "link": null}, {"localized_name": "enable_top_k", "name": "enable_top_k", "type": "BOOLEAN", "widget": {"name": "enable_top_k"}, "link": null}, {"localized_name": "top_k", "name": "top_k", "type": "INT", "widget": {"name": "top_k"}, "link": null}, {"localized_name": "enable_top_p", "name": "enable_top_p", "type": "BOOLEAN", "widget": {"name": "enable_top_p"}, "link": null}, {"localized_name": "top_p", "name": "top_p", "type": "FLOAT", "widget": {"name": "top_p"}, "link": null}, {"localized_name": "enable_min_p", "name": "enable_min_p", "type": "BOOLEAN", "widget": {"name": "enable_min_p"}, "link": null}, {"localized_name": "min_p", "name": "min_p", "type": "FLOAT", "widget": {"name": "min_p"}, "link": null}, {"localized_name": "debug", "name": "debug", "type": "BOOLEAN", "widget": {"name": "debug"}, "link": null}], "outputs": [{"localized_name": "options", "name": "options", "type": "OLLAMA_OPTIONS", "links": [105]}], "properties": {"cnr_id": "comfyui-o<PERSON><PERSON>", "ver": "2.0.3", "Node name for S&R": "OllamaOptionsV2"}, "widgets_values": [false, 0, false, 0.1, false, 5, false, 2048, false, 64, false, 1.1, false, 0.8, false, 814937199, "randomize", false, "", false, 1, false, -1, false, 40, false, 0.9, false, 0, false]}, {"id": 93, "type": "easy int", "pos": [796.4722290039062, -1558.83740234375], "size": [270, 58], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "值", "name": "value", "type": "INT", "widget": {"name": "value"}, "link": null}], "outputs": [{"localized_name": "整数", "name": "int", "type": "INT", "links": [97]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy int"}, "widgets_values": [81]}, {"id": 85, "type": "WanVideoLoraSelect", "pos": [564.0900268554688, -1246.98828125], "size": [414.48162841796875, 126], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "prev_lora", "localized_name": "prev_lora", "name": "prev_lora", "shape": 7, "type": "WANVIDLORA"}, {"label": "blocks", "localized_name": "blocks", "name": "blocks", "shape": 7, "type": "SELECTEDBLOCKS"}, {"localized_name": "lora", "name": "lora", "type": "COMBO", "widget": {"name": "lora"}, "link": null}, {"localized_name": "strength", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}, "link": null}, {"localized_name": "low_mem_load", "name": "low_mem_load", "shape": 7, "type": "BOOLEAN", "widget": {"name": "low_mem_load"}, "link": null}], "outputs": [{"label": "lora", "localized_name": "lora", "name": "lora", "type": "WANVIDLORA", "links": [91]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "fa7015315a484c15eb79f41744ef501c728dc9d4", "Node name for S&R": "WanVideoLoraSelect"}, "widgets_values": ["CausVid\\Wan21_CausVid_14B_T2V_lora_rank32.safetensors", 0.40010000000000007, false]}, {"id": 89, "type": "WanVideoVACEModelSelect", "pos": [108.215576171875, -1047.65283203125], "size": [445.4362487792969, 76.24562072753906], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "vace_model", "name": "vace_model", "type": "COMBO", "widget": {"name": "vace_model"}, "link": null}], "outputs": [{"label": "vace_model", "localized_name": "vace_model", "name": "vace_model", "type": "VACEPATH", "links": [94]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "fa7015315a484c15eb79f41744ef501c728dc9d4", "Node name for S&R": "WanVideoVACEModelSelect"}, "widgets_values": ["vace\\Wan2_1-VACE_module_14B_fp8_e4m3fn.safetensors"]}, {"id": 22, "type": "WanVideoModelLoader", "pos": [978.6958618164062, -1148.200439453125], "size": [477.4410095214844, 254], "flags": {}, "order": 19, "mode": 0, "inputs": [{"label": "compile_args", "localized_name": "compile_args", "name": "compile_args", "shape": 7, "type": "WANCOMPILEARGS", "link": 84}, {"label": "block_swap_args", "localized_name": "block_swap_args", "name": "block_swap_args", "shape": 7, "type": "BLOCKSWAPARGS", "link": 85}, {"label": "lora", "localized_name": "lora", "name": "lora", "shape": 7, "type": "WANVIDLORA", "link": 91}, {"label": "vram_management_args", "localized_name": "vram_management_args", "name": "vram_management_args", "shape": 7, "type": "VRAM_MANAGEMENTARGS"}, {"label": "vace_model", "localized_name": "vace_model", "name": "vace_model", "shape": 7, "type": "VACEPATH", "link": 94}, {"label": "fantasytalking_model", "localized_name": "fantasytalking_model", "name": "fantasytalking_model", "shape": 7, "type": "FANTASYTALKINGMODEL"}, {"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "base_precision", "name": "base_precision", "type": "COMBO", "widget": {"name": "base_precision"}, "link": null}, {"localized_name": "quantization", "name": "quantization", "type": "COMBO", "widget": {"name": "quantization"}, "link": null}, {"localized_name": "load_device", "name": "load_device", "type": "COMBO", "widget": {"name": "load_device"}, "link": null}, {"localized_name": "attention_mode", "name": "attention_mode", "shape": 7, "type": "COMBO", "widget": {"name": "attention_mode"}, "link": null}], "outputs": [{"label": "model", "localized_name": "model", "name": "model", "type": "WANVIDEOMODEL", "slot_index": 0, "links": [29]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "fa7015315a484c15eb79f41744ef501c728dc9d4", "Node name for S&R": "WanVideoModelLoader"}, "widgets_values": ["WAN_kijia\\Wan2_1-T2V-14B_fp8_e4m3fn.safetensors", "bf16", "fp8_e4m3fn", "offload_device", "sageattn"]}, {"id": 11, "type": "LoadWanVideoT5TextEncoder", "pos": [611.2511596679688, -663.4885864257812], "size": [377.1661376953125, 130], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "model_name", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}, {"localized_name": "precision", "name": "precision", "type": "COMBO", "widget": {"name": "precision"}, "link": null}, {"localized_name": "load_device", "name": "load_device", "shape": 7, "type": "COMBO", "widget": {"name": "load_device"}, "link": null}, {"localized_name": "quantization", "name": "quantization", "shape": 7, "type": "COMBO", "widget": {"name": "quantization"}, "link": null}], "outputs": [{"label": "wan_t5_model", "localized_name": "wan_t5_model", "name": "wan_t5_model", "type": "WANTEXTENCODER", "slot_index": 0, "links": [77]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "fa7015315a484c15eb79f41744ef501c728dc9d4", "Node name for S&R": "LoadWanVideoT5TextEncoder"}, "widgets_values": ["umt5-xxl-enc-bf16.safetensors", "bf16", "offload_device", "fp8_e4m3fn"]}, {"id": 96, "type": "Text Multiline", "pos": [805.6294555664062, -1978.9456787109375], "size": [317.0408935546875, 149.82444763183594], "flags": {}, "order": 13, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"label": "STRING", "localized_name": "字符串", "name": "STRING", "type": "STRING", "links": [101]}], "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "1.0.2", "Node name for S&R": "Text Multiline"}, "widgets_values": ["你是一个视频生成的提示词优化器，", [false, true]]}, {"id": 98, "type": "OllamaConnectivityV2", "pos": [823.1090698242188, -2178.986083984375], "size": [270, 130], "flags": {}, "order": 14, "mode": 0, "inputs": [{"localized_name": "url", "name": "url", "type": "STRING", "widget": {"name": "url"}, "link": null}, {"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "keep_alive", "name": "keep_alive", "type": "INT", "widget": {"name": "keep_alive"}, "link": null}, {"localized_name": "keep_alive_unit", "name": "keep_alive_unit", "type": "COMBO", "widget": {"name": "keep_alive_unit"}, "link": null}], "outputs": [{"localized_name": "connection", "name": "connection", "type": "OLLAMA_CONNECTIVITY", "links": [104]}], "properties": {"cnr_id": "comfyui-o<PERSON><PERSON>", "ver": "2.0.3", "Node name for S&R": "OllamaConnectivityV2"}, "widgets_values": ["http://127.0.0.1:11434", "qwen2.5:32b", 5, "minutes"]}, {"id": 78, "type": "WanVideoBlockSwap", "pos": [616.1707763671875, -878.5821533203125], "size": [315, 154], "flags": {}, "order": 15, "mode": 4, "inputs": [{"localized_name": "blocks_to_swap", "name": "blocks_to_swap", "type": "INT", "widget": {"name": "blocks_to_swap"}, "link": null}, {"localized_name": "offload_img_emb", "name": "offload_img_emb", "type": "BOOLEAN", "widget": {"name": "offload_img_emb"}, "link": null}, {"localized_name": "offload_txt_emb", "name": "offload_txt_emb", "type": "BOOLEAN", "widget": {"name": "offload_txt_emb"}, "link": null}, {"localized_name": "use_non_blocking", "name": "use_non_blocking", "shape": 7, "type": "BOOLEAN", "widget": {"name": "use_non_blocking"}, "link": null}, {"localized_name": "vace_blocks_to_swap", "name": "vace_blocks_to_swap", "shape": 7, "type": "INT", "widget": {"name": "vace_blocks_to_swap"}, "link": null}], "outputs": [{"label": "block_swap_args", "localized_name": "block_swap_args", "name": "block_swap_args", "type": "BLOCKSWAPARGS", "links": [85]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "fa7015315a484c15eb79f41744ef501c728dc9d4", "Node name for S&R": "WanVideoBlockSwap"}, "widgets_values": [20, false, false, true, 0]}, {"id": 97, "type": "ShowText|pysssss", "pos": [1582.37744140625, -1969.515625], "size": [521.5534057617188, 350.2666931152344], "flags": {}, "order": 20, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "link": 102}], "outputs": [{"localized_name": "字符串", "name": "STRING", "shape": 6, "type": "STRING", "links": [103]}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["为了使这个提示词更吸引人并且更具体，我们可以增加一些细节和情感色彩。这里有一个改进后的版本：\n\n\"一位身着精致丝绸旗袍的古典美少女，在柔和的光线中缓缓转过身来，眼神温柔而含蓄地与镜头相遇，背景是中国传统的园林景致，营造出一种典雅而又梦幻的氛围。\"\n\n这样的描述不仅强调了人物和服装的主要元素，还增加了场景的细节以及情感层次，能够帮助创作者更好地理解和创作所需的画面。"]}, {"id": 95, "type": "OllamaGenerateV2", "pos": [1144.1854248046875, -1921.338134765625], "size": [400, 270], "flags": {}, "order": 18, "mode": 0, "inputs": [{"localized_name": "connectivity", "name": "connectivity", "shape": 7, "type": "OLLAMA_CONNECTIVITY", "link": 104}, {"localized_name": "options", "name": "options", "shape": 7, "type": "OLLAMA_OPTIONS", "link": 105}, {"localized_name": "images", "name": "images", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "context", "name": "context", "shape": 7, "type": "OLLAMA_CONTEXT", "link": null}, {"localized_name": "meta", "name": "meta", "shape": 7, "type": "OLLAMA_META", "link": null}, {"localized_name": "system", "name": "system", "type": "STRING", "widget": {"name": "system"}, "link": 101}, {"localized_name": "prompt", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": 100}, {"localized_name": "keep_context", "name": "keep_context", "type": "BOOLEAN", "widget": {"name": "keep_context"}, "link": null}, {"localized_name": "format", "name": "format", "type": "COMBO", "widget": {"name": "format"}, "link": null}], "outputs": [{"localized_name": "result", "name": "result", "type": "STRING", "links": [102]}, {"localized_name": "context", "name": "context", "type": "OLLAMA_CONTEXT", "links": null}, {"localized_name": "meta", "name": "meta", "type": "OLLAMA_META", "links": null}], "properties": {"cnr_id": "comfyui-o<PERSON><PERSON>", "ver": "2.0.3", "Node name for S&R": "OllamaGenerateV2"}, "widgets_values": ["You are an AI artist.", "What is art?", false, "text", [false, true], [false, true]]}, {"id": 30, "type": "VHS_VideoCombine", "pos": [2280.408203125, -1370.42333984375], "size": [814.15234375, 796.5157470703125], "flags": {}, "order": 25, "mode": 0, "inputs": [{"label": "images", "localized_name": "images", "name": "images", "type": "IMAGE", "link": 36}, {"label": "audio", "localized_name": "audio", "name": "audio", "shape": 7, "type": "AUDIO"}, {"label": "meta_batch", "localized_name": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager"}, {"label": "vae", "localized_name": "vae", "name": "vae", "shape": 7, "type": "VAE"}, {"localized_name": "frame_rate", "name": "frame_rate", "type": "FLOAT", "widget": {"name": "frame_rate"}, "link": null}, {"localized_name": "loop_count", "name": "loop_count", "type": "INT", "widget": {"name": "loop_count"}, "link": null}, {"localized_name": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}, {"localized_name": "format", "name": "format", "type": "COMBO", "widget": {"name": "format"}, "link": null}, {"localized_name": "pingpong", "name": "pingpong", "type": "BOOLEAN", "widget": {"name": "pingpong"}, "link": null}, {"localized_name": "save_output", "name": "save_output", "type": "BOOLEAN", "widget": {"name": "save_output"}, "link": null}], "outputs": [{"label": "Filenames", "localized_name": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES"}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "a7ce59e381934733bfae03b1be029756d6ce936d", "Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "pl-WanVideo2_1_T2V", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": false, "trim_to_audio": false, "pingpong": false, "save_output": true, "videopreview": {"paused": false, "hidden": false, "params": {"filename": "pl-WanVideo2_1_T2V_00001.mp4", "workflow": "pl-WanVideo2_1_T2V_00001.png", "fullpath": "H:\\ComfyUI_124_251\\ComfyUI\\output\\pl-WanVideo2_1_T2V_00001.mp4", "format": "video/h264-mp4", "subfolder": "", "type": "output", "frame_rate": 16}}}}, {"id": 90, "type": "ColorMatch", "pos": [2316.376708984375, -1558.805419921875], "size": [315, 102], "flags": {}, "order": 1, "mode": 4, "inputs": [{"label": "image_ref", "localized_name": "image_ref", "name": "image_ref", "type": "IMAGE"}, {"label": "image_target", "localized_name": "image_target", "name": "image_target", "type": "IMAGE"}, {"localized_name": "method", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}, {"localized_name": "strength", "name": "strength", "shape": 7, "type": "FLOAT", "widget": {"name": "strength"}, "link": null}], "outputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE"}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.1.0", "Node name for S&R": "ColorMatch"}, "widgets_values": ["mkl", 1]}], "links": [[29, 22, 0, 27, 0, "WANVIDEOMODEL"], [33, 27, 0, 28, 1, "LATENT"], [36, 28, 0, 30, 0, "IMAGE"], [43, 38, 0, 28, 0, "VAE"], [68, 16, 0, 66, 0, "*"], [70, 66, 0, 27, 1, "WANVIDEOTEXTEMBEDS"], [71, 37, 0, 27, 2, "WANVIDIMAGE_EMBEDS"], [76, 71, 0, 16, 0, "WANTEXTENCODER"], [77, 11, 0, 71, 0, "*"], [84, 35, 0, 22, 0, "WANCOMPILEARGS"], [85, 78, 0, 22, 1, "BLOCKSWAPARGS"], [91, 85, 0, 22, 2, "WANVIDLORA"], [94, 89, 0, 22, 4, "VACEPATH"], [95, 91, 0, 37, 1, "INT"], [96, 92, 0, 37, 2, "INT"], [97, 93, 0, 37, 3, "INT"], [98, 94, 0, 27, 14, "INT"], [100, 73, 0, 95, 6, "STRING"], [101, 96, 0, 95, 5, "STRING"], [102, 95, 0, 97, 0, "STRING"], [103, 97, 0, 16, 2, "STRING"], [104, 98, 0, 95, 0, "OLLAMA_CONNECTIVITY"], [105, 100, 0, 95, 1, "OLLAMA_OPTIONS"]], "groups": [{"id": 1, "title": "文生视频", "bounding": [600.4197387695312, -1459.7972412109375, 2062, 903], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "提示词", "bounding": [786.3629760742188, -1963.945068359375, 1163.862060546875, 330.5249328613281], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.7972024500000005, "offset": [48.66637796279525, 1890.7371031809637]}, "frontendVersion": "1.18.10", "VHS_KeepIntermediate": true, "workspace_info": {"saveLock": false, "id": "CF_buEVAO0IANpYc67vSQ"}, "VHS_MetadataImage": true, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "VHS_latentpreview": false, "node_versions": {"ComfyUI-WanVideoWrapper": "518013fd189b8445d3b674c19f56003d7b4de39e", "ComfyUI-VideoHelperSuite": "2c25b8b53835aaeb63f831b3137c705cf9f85dce"}}, "version": 0.4}