{"id": "79350e4e-20b9-49e9-b85a-eaa28263d98a", "revision": 0, "last_node_id": 212, "last_link_id": 859, "nodes": [{"id": 39, "type": "LoadImage", "pos": [821.8904418945312, 1243.2294921875], "size": [315, 314.00006103515625], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": []}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["pexels-jtucker-954254 (1).jpg", "image"], "color": "#223", "bgcolor": "#335"}, {"id": 77, "type": "PrepImageForClipVision", "pos": [813.8904418945312, 1081.2294921875], "size": [315, 106], "flags": {}, "order": 14, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 728}, {"localized_name": "interpolation", "name": "interpolation", "type": "COMBO", "widget": {"name": "interpolation"}, "link": null}, {"localized_name": "crop_position", "name": "crop_position", "type": "COMBO", "widget": {"name": "crop_position"}, "link": null}, {"localized_name": "sharpening", "name": "sharpening", "type": "FLOAT", "widget": {"name": "sharpening"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [543]}], "properties": {"cnr_id": "comfyui_ipadapter_plus", "ver": "2.0.0", "Node name for S&R": "PrepImageForClipVision"}, "widgets_values": ["LANCZOS", "left", 0], "color": "#223", "bgcolor": "#335"}, {"id": 19, "type": "CLIPTextEncode", "pos": [871.560546875, 734.4476318359375], "size": [397.1569519042969, 124.60340881347656], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 29}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [648]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""], "color": "#322", "bgcolor": "#533"}, {"id": 175, "type": "INPAINT_VAEEncodeInpaintConditioning", "pos": [1622, 580], "size": [292.20001220703125, 106], "flags": {}, "order": 23, "mode": 0, "inputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 647}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 648}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 649}, {"localized_name": "pixels", "name": "pixels", "type": "IMAGE", "link": 839}, {"localized_name": "mask", "name": "mask", "type": "MASK", "link": 752}], "outputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [672]}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [673]}, {"localized_name": "latent_inpaint", "name": "latent_inpaint", "type": "LATENT", "slot_index": 2, "links": [816]}, {"localized_name": "latent_samples", "name": "latent_samples", "type": "LATENT", "slot_index": 3, "links": [817]}], "properties": {"cnr_id": "comfyui-inpaint-nodes", "ver": "1.0.4", "Node name for S&R": "INPAINT_VAEEncodeInpaintConditioning"}, "widgets_values": []}, {"id": 110, "type": "Image Comparer (rgthree)", "pos": [2088, 946], "size": [1434.0069580078125, 1076.956787109375], "flags": {}, "order": 31, "mode": 0, "inputs": [{"dir": 3, "name": "image_a", "type": "IMAGE", "link": 781}, {"dir": 3, "name": "image_b", "type": "IMAGE", "link": 792}], "outputs": [], "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide"}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_btvpu_00069_.png&type=temp&subfolder=&rand=0.9139251048384709"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_btvpu_00070_.png&type=temp&subfolder=&rand=0.8893177004857191"}]]}, {"id": 173, "type": "INPAINT_MaskedFill", "pos": [-95.75188446044922, 386.6933288574219], "size": [315, 102], "flags": {}, "order": 16, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 637}, {"localized_name": "mask", "name": "mask", "type": "MASK", "link": 770}, {"localized_name": "fill", "name": "fill", "type": "COMBO", "widget": {"name": "fill"}, "link": null}, {"localized_name": "falloff", "name": "falloff", "type": "INT", "widget": {"name": "falloff"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [716, 809]}], "properties": {"cnr_id": "comfyui-inpaint-nodes", "ver": "1.0.4", "Node name for S&R": "INPAINT_MaskedFill"}, "widgets_values": ["telea", 0]}, {"id": 140, "type": "MaskPreview+", "pos": [-120.96389770507812, 583.7896118164062], "size": [209.73806762695312, 246], "flags": {}, "order": 15, "mode": 0, "inputs": [{"localized_name": "mask", "name": "mask", "type": "MASK", "link": 769}], "outputs": [], "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "MaskPreview+"}, "widgets_values": []}, {"id": 135, "type": "PreviewImage", "pos": [-359.62481689453125, 571.37451171875], "size": [210, 246], "flags": {}, "order": 13, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 491}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 15, "type": "CheckpointLoaderSimple", "pos": [767, 303], "size": [426.86322021484375, 98], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "Checkpoint名称", "name": "ckpt_name", "type": "COMBO", "widget": {"name": "ckpt_name"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [841]}, {"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [27, 29]}, {"localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 2, "links": [25, 649]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["juggernautXL_ragnarokBy.safetensors"]}, {"id": 188, "type": "ImageCompositeMasked", "pos": [2831.857421875, 318.0821838378906], "size": [315, 146], "flags": {}, "order": 30, "mode": 0, "inputs": [{"localized_name": "目标图像", "name": "destination", "type": "IMAGE", "link": 810}, {"localized_name": "来源图像", "name": "source", "type": "IMAGE", "link": 794}, {"localized_name": "遮罩", "name": "mask", "shape": 7, "type": "MASK", "link": 795}, {"localized_name": "x", "name": "x", "type": "INT", "widget": {"name": "x"}, "link": null}, {"localized_name": "y", "name": "y", "type": "INT", "widget": {"name": "y"}, "link": null}, {"localized_name": "缩放来源图像", "name": "resize_source", "type": "BOOLEAN", "widget": {"name": "resize_source"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [781, 842]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageCompositeMasked"}, "widgets_values": [0, 0, false]}, {"id": 131, "type": "Image Save", "pos": [3209.45263671875, 322.4014892578125], "size": [315, 558], "flags": {}, "order": 32, "mode": 2, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": 842}, {"localized_name": "output_path", "name": "output_path", "type": "STRING", "widget": {"name": "output_path"}, "link": null}, {"localized_name": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}, {"localized_name": "filename_delimiter", "name": "filename_delimiter", "type": "STRING", "widget": {"name": "filename_delimiter"}, "link": null}, {"localized_name": "filename_number_padding", "name": "filename_number_padding", "type": "INT", "widget": {"name": "filename_number_padding"}, "link": null}, {"localized_name": "filename_number_start", "name": "filename_number_start", "type": "COMBO", "widget": {"name": "filename_number_start"}, "link": null}, {"localized_name": "extension", "name": "extension", "type": "COMBO", "widget": {"name": "extension"}, "link": null}, {"localized_name": "dpi", "name": "dpi", "type": "INT", "widget": {"name": "dpi"}, "link": null}, {"localized_name": "quality", "name": "quality", "type": "INT", "widget": {"name": "quality"}, "link": null}, {"localized_name": "optimize_image", "name": "optimize_image", "type": "COMBO", "widget": {"name": "optimize_image"}, "link": null}, {"localized_name": "lossless_webp", "name": "lossless_webp", "type": "COMBO", "widget": {"name": "lossless_webp"}, "link": null}, {"localized_name": "overwrite_mode", "name": "overwrite_mode", "type": "COMBO", "widget": {"name": "overwrite_mode"}, "link": null}, {"localized_name": "show_history", "name": "show_history", "type": "COMBO", "widget": {"name": "show_history"}, "link": null}, {"localized_name": "show_history_by_prefix", "name": "show_history_by_prefix", "type": "COMBO", "widget": {"name": "show_history_by_prefix"}, "link": null}, {"localized_name": "embed_workflow", "name": "embed_workflow", "type": "COMBO", "widget": {"name": "embed_workflow"}, "link": null}, {"localized_name": "show_previews", "name": "show_previews", "type": "COMBO", "widget": {"name": "show_previews"}, "link": null}], "outputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "links": null}, {"localized_name": "files", "name": "files", "type": "STRING", "links": null}], "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "1.0.2", "Node name for S&R": "Image Save"}, "widgets_values": ["D:\\gophoto.co.il\\tutorials\\2024\\comfyui\\SmallAreaInpainting", "ComfyUI", "_", 4, "false", "png", 100, "false", "false", "false", "true", "true", "true", "true", "true"]}, {"id": 187, "type": "PreviewImage", "pos": [385.5404052734375, 588.3145751953125], "size": [210, 246], "flags": {}, "order": 22, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 719}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 185, "type": "PreviewImage", "pos": [128.54693603515625, 593.0372314453125], "size": [210, 246], "flags": {}, "order": 19, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 716}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 205, "type": "GetImageSize+", "pos": [-935.8598022460938, 740.3443603515625], "size": [159.50155639648438, 66], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 844}], "outputs": [{"localized_name": "width", "name": "width", "type": "INT", "links": []}, {"localized_name": "height", "name": "height", "type": "INT", "links": []}, {"localized_name": "count", "name": "count", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "GetImageSize+"}}, {"id": 209, "type": "LayerUtility: ImageScaleByAspectRatio", "pos": [-985.1697998046875, 373.5687561035156], "size": [342.6714782714844, 306], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 849}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": null}, {"localized_name": "aspect_ratio", "name": "aspect_ratio", "type": "COMBO", "widget": {"name": "aspect_ratio"}, "link": null}, {"localized_name": "proportional_width", "name": "proportional_width", "type": "INT", "widget": {"name": "proportional_width"}, "link": null}, {"localized_name": "proportional_height", "name": "proportional_height", "type": "INT", "widget": {"name": "proportional_height"}, "link": null}, {"localized_name": "fit", "name": "fit", "type": "COMBO", "widget": {"name": "fit"}, "link": null}, {"localized_name": "method", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}, {"localized_name": "round_to_multiple", "name": "round_to_multiple", "type": "COMBO", "widget": {"name": "round_to_multiple"}, "link": null}, {"localized_name": "scale_to_longest_side", "name": "scale_to_longest_side", "type": "BOOLEAN", "widget": {"name": "scale_to_longest_side"}, "link": null}, {"localized_name": "longest_side", "name": "longest_side", "type": "INT", "widget": {"name": "longest_side"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [850, 851, 854]}, {"localized_name": "mask", "name": "mask", "type": "MASK", "links": null}, {"localized_name": "original_size", "name": "original_size", "type": "BOX", "links": null}, {"localized_name": "width", "name": "width", "type": "INT", "links": null}, {"localized_name": "height", "name": "height", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerUtility: ImageScaleByAspectRatio"}, "widgets_values": ["original", 2, 1, "letterbox", "lanc<PERSON>s", "8", true, 1024], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 17, "type": "VAEDecode", "pos": [1679.5543212890625, 1319.0804443359375], "size": [210, 46], "flags": {}, "order": 27, "mode": 0, "inputs": [{"localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 749}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 25}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [251]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEDecode"}, "widgets_values": [], "color": "#323", "bgcolor": "#535"}, {"id": 66, "type": "PreviewBridge", "pos": [2088.599365234375, 254.97386169433594], "size": [727.852294921875, 603.7252197265625], "flags": {}, "order": 29, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": 251}, {"localized_name": "image", "name": "image", "type": "STRING", "widget": {"name": "image"}, "link": null}, {"localized_name": "block", "name": "block", "shape": 7, "type": "BOOLEAN", "widget": {"name": "block"}, "link": null}, {"localized_name": "restore_mask", "name": "restore_mask", "shape": 7, "type": "COMBO", "widget": {"name": "restore_mask"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [794]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "slot_index": 1, "links": []}], "properties": {"cnr_id": "comfyui-impact-pack", "ver": "8.14.2", "Node name for S&R": "PreviewBridge"}, "widgets_values": ["$66-0", false, "never"], "color": "#233", "bgcolor": "#355"}, {"id": 207, "type": "GetImageSizeAndCount", "pos": [-751.7984619140625, 731.3580932617188], "size": [190.86483764648438, 86], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 850}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": null}, {"label": "688 width", "localized_name": "width", "name": "width", "type": "INT", "links": []}, {"label": "1024 height", "localized_name": "height", "name": "height", "type": "INT", "links": []}, {"label": "1 count", "localized_name": "count", "name": "count", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.1.0", "Node name for S&R": "GetImageSizeAndCount"}}, {"id": 41, "type": "CLIPVisionLoader", "pos": [1205.8902587890625, 1159.2294921875], "size": [315, 58], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "clip名称", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}], "outputs": [{"localized_name": "CLIP视觉", "name": "CLIP_VISION", "type": "CLIP_VISION", "links": [94]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPVisionLoader"}, "widgets_values": ["clip_vision_h.safetensors"], "color": "#223", "bgcolor": "#335"}, {"id": 211, "type": "Florence2ModelLoader", "pos": [-249.04327392578125, 1102.0362548828125], "size": [270, 106], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "lora", "name": "lora", "shape": 7, "type": "PEFTLORA", "link": null}, {"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "precision", "name": "precision", "type": "COMBO", "widget": {"name": "precision"}, "link": null}, {"localized_name": "attention", "name": "attention", "type": "COMBO", "widget": {"name": "attention"}, "link": null}], "outputs": [{"localized_name": "florence2_model", "name": "florence2_model", "type": "FL2MODEL", "links": [856]}], "properties": {"cnr_id": "comfyui-florence2", "ver": "1.0.3", "Node name for S&R": "Florence2ModelLoader"}, "widgets_values": ["Florence-2-Flux-Large", "fp16", "sdpa"]}, {"id": 186, "type": "INPAINT_MaskedBlur", "pos": [318.0837097167969, 368.993408203125], "size": [315, 102], "flags": {}, "order": 20, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 809}, {"localized_name": "mask", "name": "mask", "type": "MASK", "link": 771}, {"localized_name": "blur", "name": "blur", "type": "INT", "widget": {"name": "blur"}, "link": null}, {"localized_name": "falloff", "name": "falloff", "type": "INT", "widget": {"name": "falloff"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [719, 839]}], "properties": {"cnr_id": "comfyui-inpaint-nodes", "ver": "1.0.4", "Node name for S&R": "INPAINT_MaskedBlur"}, "widgets_values": [40, 0]}, {"id": 139, "type": "ImagePadForOutpaintMasked", "pos": [-542.459228515625, 346.4622497558594], "size": [315, 174], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 851}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": null}, {"localized_name": "left", "name": "left", "type": "INT", "widget": {"name": "left"}, "link": null}, {"localized_name": "top", "name": "top", "type": "INT", "widget": {"name": "top"}, "link": null}, {"localized_name": "right", "name": "right", "type": "INT", "widget": {"name": "right"}, "link": null}, {"localized_name": "bottom", "name": "bottom", "type": "INT", "widget": {"name": "bottom"}, "link": null}, {"localized_name": "feathering", "name": "feathering", "type": "INT", "widget": {"name": "feathering"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [491, 637, 728, 792, 810]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "slot_index": 1, "links": [752, 769, 770, 771, 795, 838]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.1.0", "Node name for S&R": "ImagePadForOutpaintMasked"}, "widgets_values": [0, 0, 256, 0, 60]}, {"id": 210, "type": "Florence2Run", "pos": [66.96595764160156, 1062.020751953125], "size": [400, 364], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 854}, {"localized_name": "florence2_model", "name": "florence2_model", "type": "FL2MODEL", "link": 856}, {"localized_name": "text_input", "name": "text_input", "type": "STRING", "widget": {"name": "text_input"}, "link": null}, {"localized_name": "task", "name": "task", "type": "COMBO", "widget": {"name": "task"}, "link": null}, {"localized_name": "fill_mask", "name": "fill_mask", "type": "BOOLEAN", "widget": {"name": "fill_mask"}, "link": null}, {"localized_name": "keep_model_loaded", "name": "keep_model_loaded", "shape": 7, "type": "BOOLEAN", "widget": {"name": "keep_model_loaded"}, "link": null}, {"localized_name": "max_new_tokens", "name": "max_new_tokens", "shape": 7, "type": "INT", "widget": {"name": "max_new_tokens"}, "link": null}, {"localized_name": "num_beams", "name": "num_beams", "shape": 7, "type": "INT", "widget": {"name": "num_beams"}, "link": null}, {"localized_name": "do_sample", "name": "do_sample", "shape": 7, "type": "BOOLEAN", "widget": {"name": "do_sample"}, "link": null}, {"localized_name": "output_mask_select", "name": "output_mask_select", "shape": 7, "type": "STRING", "widget": {"name": "output_mask_select"}, "link": null}, {"localized_name": "seed", "name": "seed", "shape": 7, "type": "INT", "widget": {"name": "seed"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": null}, {"localized_name": "mask", "name": "mask", "type": "MASK", "links": null}, {"localized_name": "caption", "name": "caption", "type": "STRING", "links": [857]}, {"localized_name": "data", "name": "data", "type": "JSON", "links": null}], "properties": {"cnr_id": "comfyui-florence2", "ver": "1.0.3", "Node name for S&R": "Florence2Run"}, "widgets_values": ["", "detailed_caption", true, false, 1024, 3, true, "", 592518792303588, "randomize"]}, {"id": 169, "type": "INPAINT_ApplyFooocusInpaint", "pos": [1697.4671630859375, 749.6885375976562], "size": [210, 66], "flags": {}, "order": 24, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 743}, {"localized_name": "patch", "name": "patch", "type": "INPAINT_PATCH", "link": 590}, {"localized_name": "latent", "name": "latent", "type": "LATENT", "link": 816}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [741]}], "properties": {"cnr_id": "comfyui-inpaint-nodes", "ver": "1.0.4", "Node name for S&R": "INPAINT_ApplyFooocusInpaint"}, "widgets_values": []}, {"id": 61, "type": "DifferentialDiffusion", "pos": [1687.20068359375, 884.22119140625], "size": [216.77987670898438, 26], "flags": {}, "order": 25, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 741}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [761]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "DifferentialDiffusion"}, "widgets_values": [], "color": "#323", "bgcolor": "#535"}, {"id": 170, "type": "INPAINT_LoadFooocusInpaint", "pos": [1614, 413], "size": [315, 82], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "head", "name": "head", "type": "COMBO", "widget": {"name": "head"}, "link": null}, {"localized_name": "patch", "name": "patch", "type": "COMBO", "widget": {"name": "patch"}, "link": null}], "outputs": [{"localized_name": "INPAINT_PATCH", "name": "INPAINT_PATCH", "type": "INPAINT_PATCH", "slot_index": 0, "links": [590]}], "properties": {"cnr_id": "comfyui-inpaint-nodes", "ver": "1.0.4", "Node name for S&R": "INPAINT_LoadFooocusInpaint"}, "widgets_values": ["fooocus_inpaint_head.pth", "inpaint_v26.fooocus.patch"]}, {"id": 193, "type": "RemoveNoiseMask", "pos": [1677.6026611328125, 1444.244140625], "size": [210, 26], "flags": {}, "order": 28, "mode": 0, "inputs": [{"localized_name": "samples", "name": "samples", "type": "LATENT", "link": 779}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": []}], "properties": {"cnr_id": "comfyui-impact-pack", "ver": "8.14.2", "Node name for S&R": "RemoveNoiseMask"}, "widgets_values": [], "color": "#323", "bgcolor": "#535"}, {"id": 16, "type": "K<PERSON><PERSON><PERSON>", "pos": [1618.629150390625, 1002.4096069335938], "size": [315, 262], "flags": {}, "order": 26, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 761}, {"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 672}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 673}, {"localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 817}, {"localized_name": "种子", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [749, 779]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [321016536324652, "randomize", 20, 7, "dpmpp_2m_sde_gpu", "karras", 1], "color": "#323", "bgcolor": "#535"}, {"id": 18, "type": "CLIPTextEncode", "pos": [868.7049560546875, 489.849609375], "size": [384.80865478515625, 138.21595764160156], "flags": {}, "order": 21, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 27}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 859}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [647]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""], "color": "#232", "bgcolor": "#353"}, {"id": 212, "type": "ShowText|pysssss", "pos": [480.34033203125, 1061.130859375], "size": [210, 369.4089660644531], "flags": {}, "order": 17, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "link": 857}], "outputs": [{"localized_name": "字符串", "name": "STRING", "shape": 6, "type": "STRING", "links": [859]}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["The image shows a magical anime-style illustration of a woman wearing a purple and black dress and a pointed hat, riding on the back of a white unicorn. The woman has long white hair and a mysterious expression on her face, and the unicorn has a white mane and tail. The background is filled with a magical aura, giving the image a magical and whimsical feel."]}, {"id": 40, "type": "IPAdapterModelLoader", "pos": [1176.4072265625, 1037.8026123046875], "size": [315, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "ipadapter_file", "name": "ipadapter_file", "type": "COMBO", "widget": {"name": "ipadapter_file"}, "link": null}], "outputs": [{"localized_name": "IPADAPTER", "name": "IPADAPTER", "type": "IPADAPTER", "links": [93]}], "properties": {"cnr_id": "comfyui_ipadapter_plus", "ver": "2.0.0", "Node name for S&R": "IPAdapterModelLoader"}, "widgets_values": ["ip-adapter-plus_sdxl_vit-h.safetensors"], "color": "#223", "bgcolor": "#335"}, {"id": 37, "type": "IPAdapterAdvanced", "pos": [1192.1485595703125, 1258.1728515625], "size": [315, 278], "flags": {}, "order": 18, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 841}, {"localized_name": "ipadapter", "name": "ipadapter", "type": "IPADAPTER", "link": 93}, {"localized_name": "image", "name": "image", "type": "IMAGE", "link": 543}, {"localized_name": "image_negative", "name": "image_negative", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "attn_mask", "name": "attn_mask", "shape": 7, "type": "MASK", "link": 838}, {"localized_name": "clip_vision", "name": "clip_vision", "shape": 7, "type": "CLIP_VISION", "link": 94}, {"localized_name": "weight", "name": "weight", "type": "FLOAT", "widget": {"name": "weight"}, "link": null}, {"localized_name": "weight_type", "name": "weight_type", "type": "COMBO", "widget": {"name": "weight_type"}, "link": null}, {"localized_name": "combine_embeds", "name": "combine_embeds", "type": "COMBO", "widget": {"name": "combine_embeds"}, "link": null}, {"localized_name": "start_at", "name": "start_at", "type": "FLOAT", "widget": {"name": "start_at"}, "link": null}, {"localized_name": "end_at", "name": "end_at", "type": "FLOAT", "widget": {"name": "end_at"}, "link": null}, {"localized_name": "embeds_scaling", "name": "embeds_scaling", "type": "COMBO", "widget": {"name": "embeds_scaling"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [743]}], "properties": {"cnr_id": "comfyui_ipadapter_plus", "ver": "2.0.0", "Node name for S&R": "IPAdapterAdvanced"}, "widgets_values": [0.30000000000000004, "style transfer", "concat", 0, 1, "V only"], "color": "#223", "bgcolor": "#335"}, {"id": 4, "type": "LoadImage", "pos": [1378.08251953125, 931.887451171875], "size": [696.7280883789062, 1102.657470703125], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [844, 849]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "slot_index": 1, "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["ComfyUI_temp_gfpbc_00003_.png", "image"]}], "links": [[25, 15, 2, 17, 1, "VAE"], [27, 15, 1, 18, 0, "CLIP"], [29, 15, 1, 19, 0, "CLIP"], [93, 40, 0, 37, 1, "IPADAPTER"], [94, 41, 0, 37, 5, "CLIP_VISION"], [251, 17, 0, 66, 0, "IMAGE"], [491, 139, 0, 135, 0, "IMAGE"], [543, 77, 0, 37, 2, "IMAGE"], [590, 170, 0, 169, 1, "INPAINT_PATCH"], [637, 139, 0, 173, 0, "IMAGE"], [647, 18, 0, 175, 0, "CONDITIONING"], [648, 19, 0, 175, 1, "CONDITIONING"], [649, 15, 2, 175, 2, "VAE"], [672, 175, 0, 16, 1, "CONDITIONING"], [673, 175, 1, 16, 2, "CONDITIONING"], [716, 173, 0, 185, 0, "IMAGE"], [719, 186, 0, 187, 0, "IMAGE"], [728, 139, 0, 77, 0, "IMAGE"], [741, 169, 0, 61, 0, "MODEL"], [743, 37, 0, 169, 0, "MODEL"], [749, 16, 0, 17, 0, "LATENT"], [752, 139, 1, 175, 4, "MASK"], [761, 61, 0, 16, 0, "MODEL"], [769, 139, 1, 140, 0, "MASK"], [770, 139, 1, 173, 1, "MASK"], [771, 139, 1, 186, 1, "MASK"], [779, 16, 0, 193, 0, "LATENT"], [781, 188, 0, 110, 0, "IMAGE"], [792, 139, 0, 110, 1, "IMAGE"], [794, 66, 0, 188, 1, "IMAGE"], [795, 139, 1, 188, 2, "MASK"], [809, 173, 0, 186, 0, "IMAGE"], [810, 139, 0, 188, 0, "IMAGE"], [816, 175, 2, 169, 2, "LATENT"], [817, 175, 3, 16, 3, "LATENT"], [838, 139, 1, 37, 4, "MASK"], [839, 186, 0, 175, 3, "IMAGE"], [841, 15, 0, 37, 0, "MODEL"], [842, 188, 0, 131, 0, "IMAGE"], [844, 4, 0, 205, 0, "IMAGE"], [849, 4, 0, 209, 0, "IMAGE"], [850, 209, 0, 207, 0, "IMAGE"], [851, 209, 0, 139, 0, "IMAGE"], [854, 209, 0, 210, 0, "IMAGE"], [856, 211, 0, 210, 1, "FL2MODEL"], [857, 210, 2, 212, 0, "STRING"], [859, 212, 0, 18, 1, "STRING"]], "groups": [{"id": 1, "title": "IPAdapter", "bounding": [776, 940, 768, 618], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Latent Space", "bounding": [1577, 263, 437, 1296], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Group", "bounding": [767, 431, 546, 476], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "Group", "bounding": [-1675, 241, 1061, 710], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "Group", "bounding": [-549, 240, 1189, 714], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"frontendVersion": "1.18.9", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}