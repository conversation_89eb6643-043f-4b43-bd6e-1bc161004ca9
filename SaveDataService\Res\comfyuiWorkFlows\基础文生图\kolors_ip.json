{"id": "2d434255-0c59-4be0-b2dc-0a93a2bd2ea5", "revision": 0, "last_node_id": 86, "last_link_id": 116, "nodes": [{"id": 5, "type": "EmptyLatentImage", "pos": [-1080, 350], "size": [315, 106], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "批量大小", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}, "link": null}], "outputs": [{"label": "Latent", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [2]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "EmptyLatentImage"}, "widgets_values": [1024, 1024, 1]}, {"id": 26, "type": "Note", "pos": [-1340, -60], "size": [230, 260], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["***需安装MZ节点：https://github.com/MinusZoneAI/ComfyUI-Kolors-MZ/tree/main?tab=readme-ov-file\n***\n1.kolors模型主页：https://huggingface.co/Kwai-Kolors/Kolors\n建议下载fp32的模型，unet模型放置在 models/unet/ 文件夹下\n2.chatLCM模型主页: https://huggingface.co/Kijai/ChatGLM3-safetensors\n建议下载fb8或fb4的，chatglm3放置在 models/LLM/ 文件夹下\n3.sdxl_vae模型主页：https://huggingface.co/stabilityai/sdxl-vae/tree/main\nvae模型放置在models/vae/ 文件夹下\n"], "color": "#432", "bgcolor": "#653"}, {"id": 27, "type": "Note", "pos": [-1350, 720], "size": [240, 190], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["1、IPA模型：https://huggingface.co/Kwai-Kolors/Kolors-IP-Adapter-Plus/resolve/main/image_encoder/pytorch_model.bin    模型放置到models/ipadapter/\n\n2、clip视觉模型：https://huggingface.co/Kwai-Kolors/Kolors-IP-Adapter-Plus/resolve/main/image_encoder/pytorch_model.bin    模型放置到 models/clip_vision/"], "color": "#432", "bgcolor": "#653"}, {"id": 12, "type": "MZ_ChatGLM3Loader", "pos": [-1070, 20], "size": [315, 58], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "chatglm3_checkpoint", "name": "chatglm3_checkpoint", "type": "COMBO", "widget": {"name": "chatglm3_checkpoint"}, "link": null}], "outputs": [{"label": "chatglm3_model", "localized_name": "chatglm3_model", "name": "chatglm3_model", "type": "CHATGLM3MODEL", "slot_index": 0, "links": [10, 11]}], "properties": {"cnr_id": "comfyui-kolors-mz", "ver": "43ec2701a1390259a17ef3bea6244a3134aa5153", "Node name for S&R": "MZ_ChatGLM3Loader"}, "widgets_values": ["checkpoints\\chatglm3-fp16.safetensors"]}, {"id": 17, "type": "MZ_ChatGLM3_V2", "pos": [-720.174072265625, 262.9193115234375], "size": [400, 200], "flags": {}, "order": 21, "mode": 0, "inputs": [{"label": "chatglm3_model", "localized_name": "chatglm3_model", "name": "chatglm3_model", "type": "CHATGLM3MODEL", "link": 11}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 104}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [17, 73]}], "properties": {"cnr_id": "comfyui-kolors-mz", "ver": "43ec2701a1390259a17ef3bea6244a3134aa5153", "Node name for S&R": "MZ_ChatGLM3_V2"}, "widgets_values": [""]}, {"id": 74, "type": "Reroute", "pos": [407.8711853027344, 1192.6580810546875], "size": [75, 26], "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 77}], "outputs": [{"name": "", "type": "IMAGE", "links": []}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 8, "type": "VAEDecode", "pos": [103.70376586914062, -16.444284439086914], "size": [210, 46], "flags": {}, "order": 25, "mode": 0, "inputs": [{"label": "Latent", "localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 7}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 15}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [9, 46, 77, 89, 112]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 25, "type": "LoadImage", "pos": [-1041.1226806640625, 781.0076904296875], "size": [456.7722473144531, 526.6103515625], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [57]}, {"label": "遮罩", "localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["ComfyUI_temp_rgddq_00039_.png", "image"]}, {"id": 10, "type": "MZ_KolorsUNETLoaderV2", "pos": [-1066.7197265625, 117.0142822265625], "size": [315, 58], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "unet_name", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}], "outputs": [{"label": "model", "localized_name": "model", "name": "model", "type": "MODEL", "slot_index": 0, "links": [52]}], "properties": {"cnr_id": "comfyui-kolors-mz", "ver": "43ec2701a1390259a17ef3bea6244a3134aa5153", "Node name for S&R": "MZ_KolorsUNETLoaderV2"}, "widgets_values": ["KOLORS_F32.safetensors"]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [-300.3039855957031, 35.23508834838867], "size": [320, 470], "flags": {}, "order": 24, "mode": 0, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 56}, {"label": "正面条件", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 16}, {"label": "负面条件", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 17}, {"label": "Latent", "localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 2}, {"localized_name": "种子", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"label": "Latent", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [7]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [623752027341304, "randomize", 22, 7, "dpmpp_2m_sde_gpu", "karras", 1]}, {"id": 62, "type": "MZ_IPAdapterAdvancedKolors", "pos": [-75.50187683105469, 937.2363891601562], "size": [327.6460876464844, 278], "flags": {}, "order": 15, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 52}, {"localized_name": "ipadapter", "name": "ipadapter", "type": "IPADAPTER", "link": 53}, {"localized_name": "image", "name": "image", "type": "IMAGE", "link": 57}, {"localized_name": "image_negative", "name": "image_negative", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "attn_mask", "name": "attn_mask", "shape": 7, "type": "MASK", "link": null}, {"localized_name": "clip_vision", "name": "clip_vision", "shape": 7, "type": "CLIP_VISION", "link": 54}, {"localized_name": "weight", "name": "weight", "type": "FLOAT", "widget": {"name": "weight"}, "link": null}, {"localized_name": "weight_type", "name": "weight_type", "type": "COMBO", "widget": {"name": "weight_type"}, "link": null}, {"localized_name": "combine_embeds", "name": "combine_embeds", "type": "COMBO", "widget": {"name": "combine_embeds"}, "link": null}, {"localized_name": "start_at", "name": "start_at", "type": "FLOAT", "widget": {"name": "start_at"}, "link": null}, {"localized_name": "end_at", "name": "end_at", "type": "FLOAT", "widget": {"name": "end_at"}, "link": null}, {"localized_name": "embeds_scaling", "name": "embeds_scaling", "type": "COMBO", "widget": {"name": "embeds_scaling"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [56]}], "properties": {"cnr_id": "comfyui-kolors-mz", "ver": "43ec2701a1390259a17ef3bea6244a3134aa5153", "Node name for S&R": "MZ_IPAdapterAdvancedKolors"}, "widgets_values": [1, "linear", "concat", 0, 1, "V only"]}, {"id": 24, "type": "CLIPVisionLoader", "pos": [-467.2416076660156, 1092.2908935546875], "size": [310, 70], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "clip名称", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}], "outputs": [{"label": "CLIP视觉", "localized_name": "CLIP视觉", "name": "CLIP_VISION", "type": "CLIP_VISION", "links": [54]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPVisionLoader"}, "widgets_values": ["clip_kolors.bin"]}, {"id": 20, "type": "MZ_IPAdapterModelLoaderKolors", "pos": [-503.6639099121094, 956.3816528320312], "size": [315, 58], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "ipadapter_file", "name": "ipadapter_file", "type": "COMBO", "widget": {"name": "ipadapter_file"}, "link": null}], "outputs": [{"label": "IPADAPTER", "localized_name": "IPADAPTER", "name": "IPADAPTER", "type": "IPADAPTER", "slot_index": 0, "links": [53]}], "properties": {"cnr_id": "comfyui-kolors-mz", "ver": "43ec2701a1390259a17ef3bea6244a3134aa5153", "Node name for S&R": "MZ_IPAdapterModelLoaderKolors"}, "widgets_values": ["kolors\\ip_adapter_plus_general.bin"]}, {"id": 79, "type": "Reroute", "pos": [347.2214050292969, 1366.6436767578125], "size": [75, 26], "flags": {}, "order": 33, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 93}], "outputs": [{"name": "", "type": "IMAGE", "links": [94]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 13, "type": "MZ_ChatGLM3_V2", "pos": [-730, 20], "size": [400, 200], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "chatglm3_model", "localized_name": "chatglm3_model", "name": "chatglm3_model", "type": "CHATGLM3MODEL", "link": 10}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 103}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [16, 72]}], "properties": {"cnr_id": "comfyui-kolors-mz", "ver": "43ec2701a1390259a17ef3bea6244a3134aa5153", "Node name for S&R": "MZ_ChatGLM3_V2"}, "widgets_values": ["赛博朋克，女孩"]}, {"id": 61, "type": "Fast Groups Bypasser (rgthree)", "pos": [-1426.084228515625, -243.1941375732422], "size": [340, 154], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"label": "可选连接", "name": "OPT_CONNECTION", "type": "*", "links": null}], "properties": {"matchColors": "", "matchTitle": "", "showNav": true, "sort": "position", "customSortAlphabet": "", "toggleRestriction": "default"}}, {"id": 82, "type": "easy stylesSelector", "pos": [-320.1797180175781, -634.8421630859375], "size": [425, 500], "flags": {}, "order": 16, "mode": 0, "inputs": [{"localized_name": "正面提示词（可选）", "name": "positive", "shape": 7, "type": "STRING", "link": 101}, {"localized_name": "负面提示词（可选）", "name": "negative", "shape": 7, "type": "STRING", "link": 102}, {"localized_name": "风格类型", "name": "styles", "type": "COMBO", "widget": {"name": "styles"}, "link": null}], "outputs": [{"localized_name": "正面提示词", "name": "positive", "type": "STRING", "links": [103, 105, 108]}, {"localized_name": "负面提示词", "name": "negative", "type": "STRING", "links": [104, 106, 109]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy stylesSelector", "values": ["<PERSON><PERSON><PERSON><PERSON>"]}, "widgets_values": ["fooocus_styles", "<PERSON><PERSON><PERSON><PERSON>"]}, {"id": 80, "type": "CR Prompt Text", "pos": [-762.1939086914062, -645.7405395507812], "size": [400, 200], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "prompt", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": null}], "outputs": [{"localized_name": "prompt", "name": "prompt", "type": "STRING", "links": [101]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Prompt Text"}, "widgets_values": ["1 cat，cute"]}, {"id": 81, "type": "CR Prompt Text", "pos": [-748.3841552734375, -376.6739807128906], "size": [400, 200], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "prompt", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": null}], "outputs": [{"localized_name": "prompt", "name": "prompt", "type": "STRING", "links": [102]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Prompt Text"}, "widgets_values": ["prompt"]}, {"id": 9, "type": "SaveImage", "pos": [25.45990562438965, 110.19606018066406], "size": [420, 580], "flags": {}, "order": 26, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图片", "name": "images", "type": "IMAGE", "link": 9}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["ComfyUI"]}, {"id": 51, "type": "Image Comparer (rgthree)", "pos": [1263.9632568359375, 206.98922729492188], "size": [651.1306762695312, 632.3910522460938], "flags": {}, "order": 32, "mode": 0, "inputs": [{"dir": 3, "label": "图像_A", "name": "image_a", "type": "IMAGE", "link": 46}, {"dir": 3, "label": "图像_B", "name": "image_b", "type": "IMAGE", "link": 40}], "outputs": [], "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide"}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_dvlpt_00009_.png&type=temp&subfolder=&rand=0.3832189980274088"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_dvlpt_00010_.png&type=temp&subfolder=&rand=0.26129547726670843"}]]}, {"id": 85, "type": "ShowText|pysssss", "pos": [168.10569763183594, -385.9381408691406], "size": [414.3753967285156, 243.2515106201172], "flags": {}, "order": 23, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "link": 109}], "outputs": [{"localized_name": "字符串", "name": "STRING", "shape": 6, "type": "STRING", "links": null}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["prompt, (worst quality, low quality, normal quality, lowres, low details, oversaturated, undersaturated, overexposed, underexposed, grayscale, bw, bad photo, bad photography, bad art:1.4), (watermark, signature, text font, username, error, logo, words, letters, digits, autograph, trademark, name:1.2), (blur, blurry, grainy), morbid, ugly, asymmetrical, mutated malformed, mutilated, poorly lit, bad shadow, draft, cropped, out of frame, cut off, censored, jpeg artifacts, out of focus, glitch, duplicate, (airbrushed, cartoon, anime, semi-realistic, cgi, render, blender, digital art, manga, amateur:1.3), (3D ,3D Game, 3D Game Scene, 3D Character:1.1), (bad hands, bad anatomy, bad body, bad face, bad teeth, bad arms, bad legs, deformities:1.3)"]}, {"id": 84, "type": "ShowText|pysssss", "pos": [164.115966796875, -673.6958618164062], "size": [419.9044494628906, 213.5333251953125], "flags": {}, "order": 20, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "link": 108}], "outputs": [{"localized_name": "字符串", "name": "STRING", "shape": 6, "type": "STRING", "links": null}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["1 cat，cute, "]}, {"id": 18, "type": "VAELoader", "pos": [-719.6524658203125, 513.40673828125], "size": [315, 58], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [15, 74, 80]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAELoader"}, "widgets_values": ["sdxl_vae.safetensors"]}, {"id": 54, "type": "CLIPTextEncode", "pos": [830.604248046875, 453.0040588378906], "size": [400, 200], "flags": {"collapsed": true}, "order": 22, "mode": 0, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 49}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 106}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [51]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 53, "type": "CLIPTextEncode", "pos": [606.7290649414062, 457.5020446777344], "size": [400, 200], "flags": {"collapsed": true}, "order": 19, "mode": 0, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 48}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 105}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [50]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 29, "type": "VAEEncode", "pos": [660.2566528320312, -7.235577583312988], "size": [210, 46], "flags": {}, "order": 29, "mode": 0, "inputs": [{"label": "图像", "localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 112}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 42}], "outputs": [{"label": "Latent", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [47]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEEncode"}, "widgets_values": []}, {"id": 47, "type": "VAEDecode", "pos": [921.73828125, 282.10186767578125], "size": [210, 46], "flags": {}, "order": 31, "mode": 0, "inputs": [{"label": "Latent", "localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 35}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 45}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [40, 93]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 65, "type": "PreviewImage", "pos": [895.2870483398438, 1511.5362548828125], "size": [681.2061767578125, 663.06396484375], "flags": {}, "order": 35, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 58}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 86, "type": "Reroute", "pos": [353.8883056640625, 1438.8927001953125], "size": [75, 26], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 110}], "outputs": [{"name": "", "type": "MODEL", "links": [111]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 50, "type": "K<PERSON><PERSON><PERSON>", "pos": [614.6553955078125, 452.50311279296875], "size": [576, 528], "flags": {}, "order": 30, "mode": 0, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 38}, {"label": "正面条件", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 50}, {"label": "负面条件", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 51}, {"label": "Latent", "localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 47}, {"localized_name": "种子", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"label": "Latent", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [35]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [59252444391247, "randomize", 8, 1, "dpmpp_sde", "karras", 0.25]}, {"id": 67, "type": "ControlNetApplyAdvanced", "pos": [926.86474609375, 1221.508544921875], "size": [380, 280], "flags": {}, "order": 28, "mode": 0, "inputs": [{"label": "正面条件", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 72}, {"label": "负面条件", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 73}, {"label": "ControlNet", "localized_name": "ControlNet", "name": "control_net", "type": "CONTROL_NET", "link": 116}, {"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 89}, {"localized_name": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": 74}, {"localized_name": "强度", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}, "link": null}, {"localized_name": "开始百分比", "name": "start_percent", "type": "FLOAT", "widget": {"name": "start_percent"}, "link": null}, {"localized_name": "结束百分比", "name": "end_percent", "type": "FLOAT", "widget": {"name": "end_percent"}, "link": null}], "outputs": [{"label": "正面条件", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [114]}, {"label": "负面条件", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [115]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ControlNetApplyAdvanced"}, "widgets_values": [1, 0, 0.96]}, {"id": 64, "type": "UpscaleModelLoader", "pos": [547.7521362304688, 1355.2413330078125], "size": [320, 60], "flags": {}, "order": 13, "mode": 0, "inputs": [{"localized_name": "模型名称", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}], "outputs": [{"label": "放大模型", "localized_name": "放大模型", "name": "UPSCALE_MODEL", "type": "UPSCALE_MODEL", "links": [61]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "UpscaleModelLoader"}, "widgets_values": ["4x-AnimeSharp.pth"]}, {"id": 63, "type": "ControlNetLoader", "pos": [539.2207641601562, 1219.6798095703125], "size": [343.3279113769531, 81.42477416992188], "flags": {}, "order": 14, "mode": 0, "inputs": [{"localized_name": "ControlNet名称", "name": "control_net_name", "type": "COMBO", "widget": {"name": "control_net_name"}, "link": null}], "outputs": [{"label": "ControlNet", "localized_name": "ControlNet", "name": "CONTROL_NET", "type": "CONTROL_NET", "links": [116]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ControlNetLoader"}, "widgets_values": ["XL\\diffusion_pytorch_model_XLTiltle.safetensors"]}, {"id": 49, "type": "CheckpointLoaderSimple", "pos": [537.9896240234375, 129.54832458496094], "size": [576, 98], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "Checkpoint名称", "name": "ckpt_name", "type": "COMBO", "widget": {"name": "ckpt_name"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [38, 110]}, {"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [48, 49]}, {"label": "VAE", "localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 2, "links": [42, 45]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["XL\\animagineXL40_v4Opt.safetensors"]}, {"id": 66, "type": "UltimateSDUpscale", "pos": [554.1522827148438, 1525.860595703125], "size": [320, 614], "flags": {}, "order": 34, "mode": 0, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 94}, {"label": "模型", "localized_name": "model", "name": "model", "type": "MODEL", "link": 111}, {"label": "正面条件", "localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 114}, {"label": "负面条件", "localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 115}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 80}, {"label": "放大模型", "localized_name": "upscale_model", "name": "upscale_model", "type": "UPSCALE_MODEL", "link": 61}, {"localized_name": "upscale_by", "name": "upscale_by", "type": "FLOAT", "widget": {"name": "upscale_by"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "denoise", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}, {"localized_name": "mode_type", "name": "mode_type", "type": "COMBO", "widget": {"name": "mode_type"}, "link": null}, {"localized_name": "tile_width", "name": "tile_width", "type": "INT", "widget": {"name": "tile_width"}, "link": null}, {"localized_name": "tile_height", "name": "tile_height", "type": "INT", "widget": {"name": "tile_height"}, "link": null}, {"localized_name": "mask_blur", "name": "mask_blur", "type": "INT", "widget": {"name": "mask_blur"}, "link": null}, {"localized_name": "tile_padding", "name": "tile_padding", "type": "INT", "widget": {"name": "tile_padding"}, "link": null}, {"localized_name": "seam_fix_mode", "name": "seam_fix_mode", "type": "COMBO", "widget": {"name": "seam_fix_mode"}, "link": null}, {"localized_name": "seam_fix_denoise", "name": "seam_fix_denoise", "type": "FLOAT", "widget": {"name": "seam_fix_denoise"}, "link": null}, {"localized_name": "seam_fix_width", "name": "seam_fix_width", "type": "INT", "widget": {"name": "seam_fix_width"}, "link": null}, {"localized_name": "seam_fix_mask_blur", "name": "seam_fix_mask_blur", "type": "INT", "widget": {"name": "seam_fix_mask_blur"}, "link": null}, {"localized_name": "seam_fix_padding", "name": "seam_fix_padding", "type": "INT", "widget": {"name": "seam_fix_padding"}, "link": null}, {"localized_name": "force_uniform_tiles", "name": "force_uniform_tiles", "type": "BOOLEAN", "widget": {"name": "force_uniform_tiles"}, "link": null}, {"localized_name": "tiled_decode", "name": "tiled_decode", "type": "BOOLEAN", "widget": {"name": "tiled_decode"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [58]}], "properties": {"cnr_id": "comfyui_ultimatesdupscale", "ver": "1.1.3", "Node name for S&R": "UltimateSDUpscale"}, "widgets_values": [2, 1, "fixed", 20, 4, "dpmpp_2m", "karras", 0.6000000000000001, "Linear", 1024, 1024, 8, 32, "None", 1, 64, 8, 16, true, false]}], "links": [[2, 5, 0, 3, 3, "LATENT"], [7, 3, 0, 8, 0, "LATENT"], [9, 8, 0, 9, 0, "IMAGE"], [10, 12, 0, 13, 0, "CHATGLM3MODEL"], [11, 12, 0, 17, 0, "CHATGLM3MODEL"], [15, 18, 0, 8, 1, "VAE"], [16, 13, 0, 3, 1, "CONDITIONING"], [17, 17, 0, 3, 2, "CONDITIONING"], [35, 50, 0, 47, 0, "LATENT"], [38, 49, 0, 50, 0, "MODEL"], [40, 47, 0, 51, 1, "IMAGE"], [42, 49, 2, 29, 1, "VAE"], [45, 49, 2, 47, 1, "VAE"], [46, 8, 0, 51, 0, "IMAGE"], [47, 29, 0, 50, 3, "LATENT"], [48, 49, 1, 53, 0, "CLIP"], [49, 49, 1, 54, 0, "CLIP"], [50, 53, 0, 50, 1, "CONDITIONING"], [51, 54, 0, 50, 2, "CONDITIONING"], [52, 10, 0, 62, 0, "MODEL"], [53, 20, 0, 62, 1, "IPADAPTER"], [54, 24, 0, 62, 5, "CLIP_VISION"], [56, 62, 0, 3, 0, "MODEL"], [57, 25, 0, 62, 2, "IMAGE"], [58, 66, 0, 65, 0, "IMAGE"], [61, 64, 0, 66, 5, "UPSCALE_MODEL"], [72, 13, 0, 67, 0, "CONDITIONING"], [73, 17, 0, 67, 1, "CONDITIONING"], [74, 18, 0, 67, 4, "VAE"], [77, 8, 0, 74, 0, "*"], [80, 18, 0, 66, 4, "VAE"], [89, 8, 0, 67, 3, "IMAGE"], [93, 47, 0, 79, 0, "*"], [94, 79, 0, 66, 0, "IMAGE"], [101, 80, 0, 82, 0, "STRING"], [102, 81, 0, 82, 1, "STRING"], [103, 82, 0, 13, 1, "STRING"], [104, 82, 1, 17, 1, "STRING"], [105, 82, 0, 53, 1, "STRING"], [106, 82, 1, 54, 1, "STRING"], [108, 82, 0, 84, 0, "STRING"], [109, 82, 1, 85, 0, "STRING"], [110, 49, 0, 86, 0, "*"], [111, 86, 0, 66, 1, "MODEL"], [112, 8, 0, 29, 0, "IMAGE"], [114, 67, 0, 66, 2, "CONDITIONING"], [115, 67, 1, 66, 3, "CONDITIONING"], [116, 63, 0, 67, 2, "CONTROL_NET"]], "groups": [{"id": 1, "title": "🎈文生图", "bounding": [-1101, -93, 1570, 750], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "IPA🎈", "bounding": [-1100, 664, 1572, 675], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "XL优化细节", "bounding": [482, -93, 2059, 1170], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "重绘放大", "bounding": [498.6116943359375, 1105.5921630859375, 1269.911865234375, 1106.362060546875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "Group", "bounding": [-772.1939086914062, -719.3405151367188, 1881.63427734375, 597.2628173828125], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.3380916604057101, "offset": [2588.657572807771, 614.4288728239248]}, "frontendVersion": "1.18.9", "0246.VERSION": [0, 0, 4], "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}