{"id": "2446dc95-5a32-4a94-b732-fc266c527e15", "revision": 0, "last_node_id": 109, "last_link_id": 156, "nodes": [{"id": 16, "type": "KSamplerSelect", "pos": [436.4535827636719, 1078.0330810546875], "size": [315, 58], "flags": {"collapsed": false}, "order": 0, "mode": 0, "inputs": [{"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}], "outputs": [{"label": "采样器", "localized_name": "采样器", "name": "SAMPLER", "type": "SAMPLER", "links": [19]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "KSamplerSelect", "widget_ue_connectable": {}}, "widgets_values": ["euler"]}, {"id": 25, "type": "RandomNoise", "pos": [436.6531982421875, 948.317138671875], "size": [315, 82], "flags": {"collapsed": false}, "order": 1, "mode": 0, "inputs": [{"localized_name": "噪波随机种", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "link": null}], "outputs": [{"label": "噪波生成", "localized_name": "噪波", "name": "NOISE", "type": "NOISE", "links": [37]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "RandomNoise", "widget_ue_connectable": {}}, "widgets_values": [1060028179070060, "randomize"]}, {"id": 10, "type": "VAELoader", "pos": [-193.608642578125, 1039.203857421875], "size": [315, 58], "flags": {"collapsed": false}, "order": 2, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"label": "VAE", "localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [12]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAELoader", "widget_ue_connectable": {}}, "widgets_values": ["ae.safetensors"]}, {"id": 13, "type": "SamplerCustomAdvanced", "pos": [792.2979736328125, 985.2478637695312], "size": [355.20001220703125, 326], "flags": {"collapsed": false}, "order": 24, "mode": 0, "inputs": [{"label": "噪波生成", "localized_name": "噪波", "name": "noise", "type": "NOISE", "link": 37}, {"label": "引导", "localized_name": "引导器", "name": "guider", "type": "GUIDER", "link": 30}, {"label": "采样器", "localized_name": "采样器", "name": "sampler", "type": "SAMPLER", "link": 19}, {"label": "Sigmas", "localized_name": "西格玛", "name": "sigmas", "type": "SIGMAS", "link": 20}, {"label": "Latent", "localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 23}], "outputs": [{"label": "输出", "localized_name": "Latent", "name": "output", "type": "LATENT", "slot_index": 0, "links": [24]}, {"label": "降噪输出", "localized_name": "降噪Latent", "name": "denoised_output", "type": "LATENT"}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SamplerCustomAdvanced", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 30, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-238.98809814453125, 1207.8006591796875], "size": [415.1219177246094, 127.6588363647461], "flags": {"collapsed": false}, "order": 15, "mode": 4, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 148}, {"label": "CLIP", "localized_name": "CLIPCLIP", "name": "clip", "type": "CLIP", "link": 149}, {"localized_name": "LoRA名称", "name": "lora_name", "type": "COMBO", "widget": {"name": "lora_name"}, "link": null}, {"localized_name": "模型强度", "name": "strength_model", "type": "FLOAT", "widget": {"name": "strength_model"}, "link": null}, {"localized_name": "CLIP强度", "name": "strength_clip", "type": "FLOAT", "widget": {"name": "strength_clip"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [46, 47]}, {"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [48]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widget_ue_connectable": {}}, "widgets_values": ["3D可爱Q版卡通插画-GPT-4o风格_v1.0", 0.9, 0.9]}, {"id": 11, "type": "DualCLIPLoader", "pos": [-1114.4483642578125, 1213.5262451171875], "size": [315, 130], "flags": {"collapsed": false}, "order": 3, "mode": 0, "inputs": [{"localized_name": "CLIP名称1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "CLIP名称2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [116]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "DualCLIPLoader", "widget_ue_connectable": {}}, "widgets_values": ["t5xxl_fp8_e4m3fn.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 12, "type": "UNETLoader", "pos": [-1399.4256591796875, 221.11431884765625], "size": [409.7802429199219, 85.96995544433594], "flags": {"collapsed": false}, "order": 4, "mode": 0, "inputs": [{"localized_name": "UNet名称", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "数据类型", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [115]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "UNETLoader", "widget_ue_connectable": {}}, "widgets_values": ["F.1基础算法模型.safetensors", "fp8_e4m3fn"]}, {"id": 102, "type": "LayerUtility: JoyCaption2", "pos": [-1325.545654296875, 467.7813720703125], "size": [303.7271423339844, 342], "flags": {}, "order": 14, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 147}, {"localized_name": "额外选项", "name": "extra_options", "shape": 7, "type": "JoyCaption2ExtraOption", "link": null}, {"localized_name": "LLM模型", "name": "llm_model", "type": "COMBO", "widget": {"name": "llm_model"}, "link": null}, {"localized_name": "设备", "name": "device", "type": "COMBO", "widget": {"name": "device"}, "link": null}, {"localized_name": "数据类型", "name": "dtype", "type": "COMBO", "widget": {"name": "dtype"}, "link": null}, {"localized_name": "VLM LoRA", "name": "vlm_lora", "type": "COMBO", "widget": {"name": "vlm_lora"}, "link": null}, {"localized_name": "字幕类型", "name": "caption_type", "type": "COMBO", "widget": {"name": "caption_type"}, "link": null}, {"localized_name": "字幕长度", "name": "caption_length", "type": "COMBO", "widget": {"name": "caption_length"}, "link": null}, {"localized_name": "用户提示", "name": "user_prompt", "type": "STRING", "widget": {"name": "user_prompt"}, "link": null}, {"localized_name": "最大新令牌数", "name": "max_new_tokens", "type": "INT", "widget": {"name": "max_new_tokens"}, "link": null}, {"localized_name": "顶部P", "name": "top_p", "type": "FLOAT", "widget": {"name": "top_p"}, "link": null}, {"localized_name": "温度", "name": "temperature", "type": "FLOAT", "widget": {"name": "temperature"}, "link": null}, {"localized_name": "缓存模型", "name": "cache_model", "type": "BOOLEAN", "widget": {"name": "cache_model"}, "link": null}, {"localized_name": "使用全局模型", "name": "use_global_model", "type": "BOOLEAN", "widget": {"name": "use_global_model"}, "link": null}], "outputs": [{"localized_name": "text", "name": "text", "shape": 6, "type": "STRING", "links": [142]}], "properties": {"cnr_id": "ComfyUI_LayerStyle_Advance", "ver": "fe35b54bd2781206994176f8913db4afabffcdb1", "Node name for S&R": "LayerUtility: JoyCaption2"}, "widgets_values": ["Orenguteng/Llama-3.1-8B-Lexi-Uncensored-V2", "cuda", "nf4", "text_model", "Descriptive", "any", "", 300, 0.9, 0.6, false, false], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 106, "type": "LayerMask: BiRefNetUltra", "pos": [-1664.512451171875, 481.2208557128906], "size": [315.3687438964844, 246], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 145}, {"localized_name": "细节方法", "name": "detail_method", "type": "COMBO", "widget": {"name": "detail_method"}, "link": null}, {"localized_name": "细节腐蚀", "name": "detail_erode", "type": "INT", "widget": {"name": "detail_erode"}, "link": null}, {"localized_name": "细节膨胀", "name": "detail_dilate", "type": "INT", "widget": {"name": "detail_dilate"}, "link": null}, {"localized_name": "黑点", "name": "black_point", "type": "FLOAT", "widget": {"name": "black_point"}, "link": null}, {"localized_name": "白点", "name": "white_point", "type": "FLOAT", "widget": {"name": "white_point"}, "link": null}, {"localized_name": "处理细节", "name": "process_detail", "type": "BOOLEAN", "widget": {"name": "process_detail"}, "link": null}, {"localized_name": "设备", "name": "device", "type": "COMBO", "widget": {"name": "device"}, "link": null}, {"localized_name": "最大百万像素", "name": "max_megapixels", "type": "FLOAT", "widget": {"name": "max_megapixels"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [146, 147]}, {"localized_name": "mask", "name": "mask", "type": "MASK", "links": null}], "properties": {"cnr_id": "ComfyUI_LayerStyle_Advance", "ver": "fe35b54bd2781206994176f8913db4afabffcdb1", "Node name for S&R": "LayerMask: BiRefNetUltra"}, "widgets_values": ["VITMatte", 6, 6, 0.01, 0.99, true, "cuda", 2], "color": "rgba(27, 80, 119, 0.7)"}, {"id": 82, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-663.0567626953125, 940.2996826171875], "size": [399.1570129394531, 126], "flags": {"collapsed": false}, "order": 11, "mode": 4, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 115}, {"label": "CLIP", "localized_name": "CLIPCLIP", "name": "clip", "type": "CLIP", "link": 116}, {"localized_name": "LoRA名称", "name": "lora_name", "type": "COMBO", "widget": {"name": "lora_name"}, "link": null}, {"localized_name": "模型强度", "name": "strength_model", "type": "FLOAT", "widget": {"name": "strength_model"}, "link": null}, {"localized_name": "CLIP强度", "name": "strength_clip", "type": "FLOAT", "widget": {"name": "strength_clip"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [121]}, {"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [122]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widget_ue_connectable": {}}, "widgets_values": ["【新疆女孩】 F.1_dev-fp8  新疆服饰_v1.0", 0.25, 0.25]}, {"id": 83, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-662.2974243164062, 1201.0555419921875], "size": [400.29736328125, 126], "flags": {"collapsed": false}, "order": 13, "mode": 4, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 121}, {"label": "CLIP", "localized_name": "CLIPCLIP", "name": "clip", "type": "CLIP", "link": 122}, {"localized_name": "LoRA名称", "name": "lora_name", "type": "COMBO", "widget": {"name": "lora_name"}, "link": null}, {"localized_name": "模型强度", "name": "strength_model", "type": "FLOAT", "widget": {"name": "strength_model"}, "link": null}, {"localized_name": "CLIP强度", "name": "strength_clip", "type": "FLOAT", "widget": {"name": "strength_clip"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [148]}, {"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [149]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widget_ue_connectable": {}}, "widgets_values": ["【新疆男孩】 F.1_dev-fp8 新疆服饰_V_0.1", 0.25, 0.25]}, {"id": 9, "type": "SaveImage", "pos": [1211.6475830078125, 464.81982421875], "size": [705.9500732421875, 954.0390014648438], "flags": {"collapsed": false}, "order": 26, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图片", "name": "images", "type": "IMAGE", "link": 110}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage", "widget_ue_connectable": {}}, "widgets_values": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"id": 8, "type": "VAEDecode", "pos": [1287.5595703125, 369.2613830566406], "size": [210, 46], "flags": {"collapsed": false}, "order": 25, "mode": 0, "inputs": [{"label": "Latent", "localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 24}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 12}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [110]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEDecode", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 108, "type": "easy showAnything", "pos": [193.5029296875, 470.04669189453125], "size": [340.4254455566406, 358.4297180175781], "flags": {}, "order": 20, "mode": 0, "inputs": [{"localized_name": "输入任何", "name": "anything", "shape": 7, "type": "*", "link": 153}], "outputs": [{"localized_name": "输出", "name": "output", "type": "*", "links": [154]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy showAnything"}, "widgets_values": ["Adorable 3D Character, \"Lady of Elegance: A Regal Figure from a Bygone Era. This dignified portrayal depicts a noblewoman clad in a majestic attire, exuding an aura of refinement and poise. The subject is draped in a flowing white gown, elegantly accentuated by a rich burgundy overdress adorned with intricate golden embroidery, symbolizing her esteemed social standing. The sleeves of her garment are puffed and adorned with delicate cuffs, while her long dark hair cascades down her back, framing her serene countenance. A delicate necklace and subtle jewelry add a touch of sophistication to her overall demeanor, as she stands poised, her arms extended in a gesture of gracious welcome.\", 3D render, adorable character, 3D art"]}, {"id": 17, "type": "BasicScheduler", "pos": [796.8448486328125, 833.2083740234375], "size": [315, 106], "flags": {"collapsed": false}, "order": 17, "mode": 0, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 47}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"label": "Sigmas", "localized_name": "Sigmas", "name": "SIGMAS", "type": "SIGMAS", "links": [20]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "BasicScheduler", "widget_ue_connectable": {}}, "widgets_values": ["simple", 25, 1]}, {"id": 22, "type": "BasicGuider", "pos": [478.9864807128906, 1172.9423828125], "size": [241.79998779296875, 46], "flags": {"collapsed": false}, "order": 23, "mode": 0, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 46}, {"label": "条件", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 73}], "outputs": [{"label": "引导", "localized_name": "引导器", "name": "GUIDER", "type": "GUIDER", "slot_index": 0, "links": [30]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "BasicGuider", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 5, "type": "EmptyLatentImage", "pos": [372.88250732421875, 1252.30419921875], "size": [388.************, 117.78314208984375], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "批量大小", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}, "link": null}], "outputs": [{"label": "Latent", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [23]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "EmptyLatentImage", "widget_ue_connectable": {}}, "widgets_values": [768, 1280, 1]}, {"id": 54, "type": "CLIPVisionLoader", "pos": [456.1221008300781, 859.35791015625], "size": [315, 58], "flags": {"collapsed": false}, "order": 6, "mode": 0, "inputs": [{"localized_name": "clip名称", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}], "outputs": [{"label": "CLIP视觉", "localized_name": "CLIP视觉", "name": "CLIP_VISION", "type": "CLIP_VISION", "links": [71]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPVisionLoader", "widget_ue_connectable": {}}, "widgets_values": ["sigclip_vision_patch14_384.safetensors"]}, {"id": 50, "type": "LayerUtility: Text<PERSON>oin", "pos": [-955.2711791992188, 549.559814453125], "size": [315, 130], "flags": {"collapsed": false}, "order": 16, "mode": 0, "inputs": [{"label": "文本_1", "localized_name": "text_1", "name": "text_1", "type": "STRING", "widget": {"name": "text_1"}, "link": 142}, {"label": "文本_2", "localized_name": "text_2", "name": "text_2", "shape": 7, "type": "STRING", "widget": {"name": "text_2"}, "link": 143}, {"label": "文本_3", "localized_name": "text_3", "name": "text_3", "shape": 7, "type": "STRING", "widget": {"name": "text_3"}, "link": null}, {"localized_name": "text_4", "name": "text_4", "shape": 7, "type": "STRING", "widget": {"name": "text_4"}, "link": null}], "outputs": [{"label": "文本", "localized_name": "text", "name": "text", "type": "STRING", "slot_index": 0, "links": [155]}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "c0fb64d0ebcb81c6c445a8af79ecee24bc3845b0", "Node name for S&R": "LayerUtility: Text<PERSON>oin", "widget_ue_connectable": {"text_1": true, "text_2": true, "text_3": true, "text_4": true}}, "widgets_values": ["", "", "", ""], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 39, "type": "LoadImage", "pos": [1954.6878662109375, 457.7735595703125], "size": [493.35687255859375, 916.9695434570312], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [145]}, {"label": "遮罩", "localized_name": "遮罩", "name": "MASK", "type": "MASK"}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI_02758_.png", "image"]}, {"id": 109, "type": "ShowText|pysssss", "pos": [-577.4338989257812, 487.7704162597656], "size": [266.475341796875, 334.4797058105469], "flags": {}, "order": 18, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "link": 155}], "outputs": [{"localized_name": "字符串", "name": "STRING", "shape": 6, "type": "STRING", "links": [156]}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["\"Lady of Elegance: A Regal Figure from a Bygone Era. This dignified portrayal depicts a noblewoman clad in a majestic attire, exuding an aura of refinement and poise. The subject is draped in a flowing white gown, elegantly accentuated by a rich burgundy overdress adorned with intricate golden embroidery, symbolizing her esteemed social standing. The sleeves of her garment are puffed and adorned with delicate cuffs, while her long dark hair cascades down her back, framing her serene countenance. A delicate necklace and subtle jewelry add a touch of sophistication to her overall demeanor, as she stands poised, her arms extended in a gesture of gracious welcome.\""]}, {"id": 101, "type": "CR Text", "pos": [-1411.3916015625, 868.690185546875], "size": [400, 200], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "text", "name": "text", "type": "*", "links": [143]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text"}, "widgets_values": ["", [false, true]]}, {"id": 100, "type": "Note", "pos": [-939.6754150390625, 773.1323852539062], "size": [241.9338836669922, 116.81838989257812], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["LoRA这里可以根据需要，可加可不加。\n新疆风格lora：https://www.liblib.art/userpage/e7072f6143e543f1aca7b066b9eb5248/publish"], "color": "#432", "bgcolor": "#653"}, {"id": 107, "type": "easy stylesSelector", "pos": [-257.8346252441406, 466.29644775390625], "size": [425, 500], "flags": {}, "order": 19, "mode": 0, "inputs": [{"localized_name": "正面提示词（可选）", "name": "positive", "shape": 7, "type": "STRING", "link": 156}, {"localized_name": "负面提示词（可选）", "name": "negative", "shape": 7, "type": "STRING", "link": null}, {"localized_name": "风格类型", "name": "styles", "type": "COMBO", "widget": {"name": "styles"}, "link": null}], "outputs": [{"localized_name": "正面提示词", "name": "positive", "type": "STRING", "links": [153]}, {"localized_name": "负面提示词", "name": "negative", "type": "STRING", "links": null}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy stylesSelector", "values": ["Adorable 3D Character"]}, "widgets_values": ["fooocus_styles", "Adorable 3D Character"]}, {"id": 6, "type": "CLIPTextEncode", "pos": [618.4441528320312, 458.9376525878906], "size": [410.9529113769531, 161.54441833496094], "flags": {"collapsed": true}, "order": 21, "mode": 0, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 48}, {"label": "文本", "localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 154}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [72]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode", "widget_ue_connectable": {"text": true}}, "widgets_values": ["A youthful and energetic girl, dressed in high waisted jeans, a striped shirt with exposed navel, and canvas shoes, riding a bike. The basket was filled with sunflowers, and she faced the wind with a smile on her face. Her hair fluttered, revealing the free sunshine, and the picture was filled with the beauty of youth and nature.", [false, true]]}, {"id": 53, "type": "StyleModelLoader", "pos": [794.8320922851562, 395.1341552734375], "size": [315, 58], "flags": {"collapsed": false}, "order": 10, "mode": 0, "inputs": [{"localized_name": "风格模型名称", "name": "style_model_name", "type": "COMBO", "widget": {"name": "style_model_name"}, "link": null}], "outputs": [{"label": "风格模型", "localized_name": "风格模型", "name": "STYLE_MODEL", "type": "STYLE_MODEL", "links": [70]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "StyleModelLoader", "widget_ue_connectable": {}}, "widgets_values": ["flux1-redux-dev.safetensors"]}, {"id": 52, "type": "ReduxAdvanced", "pos": [788.0513305664062, 543.42236328125], "size": [317.4000244140625, 234], "flags": {"collapsed": false}, "order": 22, "mode": 0, "inputs": [{"label": "conditioning", "localized_name": "conditioning", "name": "conditioning", "type": "CONDITIONING", "link": 72}, {"label": "style_model", "localized_name": "style_model", "name": "style_model", "type": "STYLE_MODEL", "link": 70}, {"label": "clip_vision", "localized_name": "clip_vision", "name": "clip_vision", "type": "CLIP_VISION", "link": 71}, {"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 146}, {"label": "mask", "localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK"}, {"localized_name": "downsampling_factor", "name": "downsampling_factor", "type": "FLOAT", "widget": {"name": "downsampling_factor"}, "link": null}, {"localized_name": "downsampling_function", "name": "downsampling_function", "type": "COMBO", "widget": {"name": "downsampling_function"}, "link": null}, {"localized_name": "mode", "name": "mode", "type": "COMBO", "widget": {"name": "mode"}, "link": null}, {"localized_name": "weight", "name": "weight", "type": "FLOAT", "widget": {"name": "weight"}, "link": null}, {"localized_name": "autocrop_margin", "name": "autocrop_margin", "shape": 7, "type": "FLOAT", "widget": {"name": "autocrop_margin"}, "link": null}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [73]}, {"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE"}, {"label": "MASK", "localized_name": "遮罩", "name": "MASK", "type": "MASK"}], "properties": {"cnr_id": "ComfyUI_AdvancedRefluxControl", "ver": "2b95c2c866399ca1914b4da486fe52808f7a9c60", "Node name for S&R": "ReduxAdvanced", "widget_ue_connectable": {}}, "widgets_values": [2, "area", "center crop (square)", 1, 0.1]}], "links": [[12, 10, 0, 8, 1, "VAE"], [19, 16, 0, 13, 2, "SAMPLER"], [20, 17, 0, 13, 3, "SIGMAS"], [23, 5, 0, 13, 4, "LATENT"], [24, 13, 0, 8, 0, "LATENT"], [30, 22, 0, 13, 1, "GUIDER"], [37, 25, 0, 13, 0, "NOISE"], [46, 30, 0, 22, 0, "MODEL"], [47, 30, 0, 17, 0, "MODEL"], [48, 30, 1, 6, 0, "CLIP"], [70, 53, 0, 52, 1, "STYLE_MODEL"], [71, 54, 0, 52, 2, "CLIP_VISION"], [72, 6, 0, 52, 0, "CONDITIONING"], [73, 52, 0, 22, 1, "CONDITIONING"], [110, 8, 0, 9, 0, "IMAGE"], [115, 12, 0, 82, 0, "MODEL"], [116, 11, 0, 82, 1, "CLIP"], [121, 82, 0, 83, 0, "MODEL"], [122, 82, 1, 83, 1, "CLIP"], [142, 102, 0, 50, 0, "STRING"], [143, 101, 0, 50, 1, "STRING"], [145, 39, 0, 106, 0, "IMAGE"], [146, 106, 0, 52, 3, "IMAGE"], [147, 106, 0, 102, 0, "IMAGE"], [148, 83, 0, 30, 0, "MODEL"], [149, 83, 1, 30, 1, "CLIP"], [153, 107, 0, 108, 0, "*"], [154, 108, 0, 6, 1, "STRING"], [155, 50, 0, 109, 0, "STRING"], [156, 109, 0, 107, 0, "STRING"]], "groups": [{"id": 2, "title": "保存图像", "bounding": [434.4295654296875, 311.5597229003906, 715.2106323242188, 1037.666015625], "color": "#88A", "font_size": 30, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.8769226950000021, "offset": [1800.833446296737, -91.56190153165113]}, "frontendVersion": "1.18.9", "0246.VERSION": [0, 0, 4], "ue_links": [], "links_added_by_ue": [], "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}